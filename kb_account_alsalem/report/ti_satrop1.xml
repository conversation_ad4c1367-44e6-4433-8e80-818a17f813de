<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="paperformat_reports" model="report.paperformat">
        <field name="name">A4 reports</field>
        <field name="default" eval="True" />
        <field name="format">A4</field>
        <field name="page_height">0</field>
        <field name="page_width">0</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">20</field>
        <field name="margin_bottom">43</field>
        <field name="margin_left">3</field>
        <field name="margin_right">0</field>
        <field name="header_line" eval="False" />
        <field name="header_spacing">15</field>
        <field name="dpi">90</field>
    </record>
    <record id="tax_invoice9" model="ir.actions.report">
        <field name="name">Satrop 1 - ساتوب 1</field>
        <field name="model">account.move</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">kb_account_alsalem.print_tax_invoice9</field>
        <field name="report_file">kb_account_alsalem.print_tax_invoice9</field>
        <field name="paperformat_id" ref="kb_account_alsalem.paperformat_reports"/>
        <field name="print_report_name">'Satrop-1 - %s' % (object.name).replace('/', '')</field>
        <field name="binding_model_id" ref="model_account_move" />
        <field name="binding_type">report</field>
    </record>

    <template id="print_tax_invoice9">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <link href="https://fonts.googleapis.com/css2?family=Tajawal" rel="stylesheet" />
                <div class="page" style="font-family: 'Tajawal', sans-serif;">
                    <div style="text-align: center; direction: rtl;">
                        <h2>فاتورة ضريبية<br />
                            TAX INVOICE <br />
                        </h2>
                    </div>
                    <t t-foreach="docs" t-as="o">

                        <table
                            style="border: none; border-collapse: collapse; width: 98%; margin-left: auto; margin-right: auto; ">
                            <tr>
                                <th colspan="3" style="border:none; text-align: left; padding: 8px;">
                                    Invoice No:
                                    <t t-esc="o.name" />
                                </th>
                                <th colspan="3" style="direction: rtl; border:none; text-align: right; padding: 8px;">
                                    رقم الفاتورة:
                                    <t t-esc="o.name" />
                                </th>
                            </tr>
                            <tr>
                                <th colspan="6" style="border: none; text-align: left; padding: 8px;">
                                    DATE:
                                    <t t-esc="o.invoice_date" t-options='{"widget":"date","format":"dd-MM-yyyy"}' />
                                </th>
                            </tr>
                        </table>
                    </t>
                    <table style="border: none; width: 35%; height: 10%; margin-left: 580px; direction: rtl;">
                        <tr>
                            <th colspan="3" style="border:none; text-align: left; padding: 8px;">
                                Contract No:
                            </th>
                            <td colspan="3" style="border: none; text-align: center; padding: 8px; direction: ltr; ">
                                <t t-esc="o.contract_num" />
                            </td>
                            <th colspan="3" style="direction: rtl; border:none; text-align: right; padding: 8px;">
                                رقم العقد:
                            </th>
                        </tr>
                    </table>
                    <t t-foreach="docs" t-as="o">
                        <table style="border: 1px solid black; width: 100%; direction: rtl;">
                            <tr>
                                <th style="text-align: center; border: 1px solid black;">
                                    رقم أمر شراء<br></br>
                                    PO #
                                    <t t-esc="o.po_number" />
                                </th>
                                
                                <th colspan="4" style="text-align: center; border: 1px solid black;">
                                    Subject:- Shuttle Bus Service
                                </th>
                            </tr>
                            <tr>
                            <td style="text-align: center; border: 1px solid black;">
                                    السالم الرقم الضريبي
                                </td>
                                
                                <td style="text-align: center; border: 1px solid black;">
                                    310037266100003
                                </td>
                                <td style="text-align: center; border: 1px solid black;">
                                    ALSalem VAT Number
                                </td>
                                
                                <td style="text-align: center; border: 1px solid black;">
                                    <t t-esc="o.remarks_note_from" t-options='{"widget":"date","format":"dd-MM-yyyy"}'/>
                                </td>
                                <td style="text-align: center; border: 1px solid black;">
                                    Period From
                                </td>
                                <!-- <td style="text-align: center; border: 1px solid black;">
                                    &#160;<br></br>
                                </td> -->
                            </tr>
                            <tr>
                                
                                 <td style="text-align: center; border: 1px solid black;">
                                    الرقم الضريبي لـ <t t-esc="o.partner_id.name" />
                                </td>
                                <td style="text-align: center; border: 1px solid black;">
                                    <t t-esc="o.partner_id.vat" />
                                </td>
                                <td style="text-align: center; border: 1px solid black;">
                                    <t t-esc="o.partner_id.company_name_ar" /> VAT Number
                                </td>
                               
                                <td style="text-align: center; border: 1px solid black;">
                                    <t t-esc="o.remarks_note_to" t-options='{"widget":"date","format":"dd-MM-yyyy"}' />
                                </td>
                                 <td style="text-align: center; border: 1px solid black;">
                                    Period To
                                </td>
                                <!-- <td style="text-align: center; border: 1px solid black;">
                                    &#160;<br></br>
                                </td> -->
                            </tr>
                            <tr>
                                <td style="text-align: center; border: 1px solid black;">
                                    رقم السجل التجاري لـ<t t-esc="o.partner_id.name" />
                                </td>

                                <td style="text-align: center; border: 1px solid black;">
                                  <t t-esc="o.partner_id.kb_company_cr" />
                                </td>

                                <td colspan="3" style="text-align: center; border: 1px solid black; background-color: white;">
                                    CR Number:
                                </td>
                            </tr>
                        </table>
                    </t>
                    <br></br>
                    <p style="text-align: center; padding-left: 240px;">
                        نحن نقدم فاتورتنا لتأجير الحافلات لشهر
                        <t t-esc="o.months" /> /
                        <t t-esc="o.years" />
                    </p>

                    <p style="text-align: center;">
                        We here by submit our invoice for lease of Buses for the month of
                        <t t-esc="o.months" /> /
                        <t t-esc="o.years" />
                    </p>
                    <!-- here is the vat table start -->
                    <table style="border: 1px solid black; width: 100%; direction: ltr; width: 100%;">
                        <tbody class="invoice_tbody">
                            <tr>
                                <td style="text-align:center; border: 1px solid black;">
                                    رقم <br></br>
                                    sr.NO
                                </td>
                                <td style="text-align:center; border: 1px solid black; width: 28%">
                                    وصف البضاعة<br></br>
                                    Description
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    عدد الأيام<br></br>
                                    Number of Days
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    كمية <br></br>
                                    Quantity
                                </td>
                                <!-- <td style="text-align:center; border: 1px solid black;">
                                    وحدة <br></br>
                                    Unit
                                </td> -->
                                <td style="text-align:center; border: 1px solid black;">
                                    سعر الوحدة<br></br> ريال سعودي <br></br>
                                    Unit Price SAR
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    المبلغ الأجمالي<br></br> ريال سعودي <br></br>
                                    Total Amount SAR
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    ناقص 10% مبلغ الاحتفاظ <br></br>
                                    Less Retention 10%
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    مبلغ الضريبة<br></br>
                                    VAT Amount
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    صافي المبلغ<br></br> بالريال السعودي<br></br>
                                    Net Total Amount SAR
                                </td>
                            </tr>
                            <t t-set="current_subtotal" t-value="0" />
                            <t t-set="ln" t-value="1" />
                            <t t-set="lines"
                                t-value="o.invoice_line_ids.sorted(key=lambda l: (-l.sequence, l.date, l.move_name, -l.id), reverse=True)" />
                            <t t-foreach="lines" t-as="line">
                                <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal"
                                    groups="account.group_show_line_subtotals_tax_excluded" />
                                <t t-set="current_subtotal" t-value="current_subtotal + line.price_total"
                                    groups="account.group_show_line_subtotals_tax_included" />
                                <tr
                                    t-att-class="'bg-200 font-weight-bold o_line_section' if line.display_type == 'line_section' else 'font-italic o_line_note' if line.display_type == 'line_note' else ''">
                                    <!-- <t t-if="not line.display_type" name="account_invoice_line_accountable"> -->
                                    <td style="text-align:center; border: 1px solid black;"
                                        name="account_invoice_line_name">
                                        <span t-esc="ln" />
                                           <t t-set="ln" t-value="ln + 1" />
                                    </td>
                                    <td style="text-align:center; border: 1px solid black;"
                                        name="account_invoice_line_name"><span t-field="line.name"
                                            t-options="{'widget': 'text'}" />
                                    </td>
                                    <td style="text-align:center; border: 1px solid black;">
                                        <span t-field="line.num_of_days" />
                                    </td>
                                    <td style="text-align:center; border: 1px solid black;">
                                        <span t-field="line.quantity" />
                                        <span t-field="line.product_uom_id" groups="uom.group_uom" />
                                        <!-- <span t-field="line.price_unit" /> -->
                                    </td>
                                    <td style="text-align:center; border: 1px solid black;"
                                        t-attf-class="text-right {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                                        <span class="text-nowrap" t-field="line.price_unit" t-options="{&quot;widget&quot;: &quot;float&quot;, &quot;precision&quot;: 2}"/>
                                    </td>



                                    <td style="text-align:center; border: 1px solid black;"
                                        class="text-right o_price_total">
                                        <span class="text-nowrap" t-field="line.price_subtotal" t-options='{"widget": "float", "precision": 2}'/>
                                        <!-- <span class="text-nowrap" t-field="line.price_subtotal"
                                            groups="account.group_show_line_subtotals_tax_excluded"
                                            t-options='{"widget": "float", "precision": 2}' />
                                        <span class="text-nowrap" t-field="line.price_total"
                                            groups="account.group_show_line_subtotals_tax_included"
                                            t-options='{"widget": "float", "precision": 2}' /> -->
                                    </td>
                                    <td style="text-align:center; border: 1px solid black;">
                                        <t t-esc="line.lessRetention" t-options="{&quot;widget&quot;: &quot;float&quot;, &quot;precision&quot;: 2}" />
                                    </td>
                                    <td style="text-align:center; border: 1px solid black;">
                                        <t t-esc="line.price_subtotal * 0.15" t-options="{&quot;widget&quot;: &quot;float&quot;, &quot;precision&quot;: 2}" />
                                    </td>
                                    <td style="text-align:center; border: 1px solid black;">
                                        <t t-esc="line.price_total"  t-options="{&quot;widget&quot;: &quot;float&quot;, &quot;precision&quot;: 2}"/>
                                    </td>

                                    <t t-if="line.display_type == 'line_section'">
                                        <td style="text-align:center; border: 1px solid black;" colspan="99">
                                            <span t-field="line.name" t-options="{'widget': 'text'}" />
                                        </td>
                                        <t t-set="current_section" t-value="line" />
                                        <t t-set="current_subtotal" t-value="0" />
                                    </t>
                                    <t t-if="line.display_type == 'line_note'">
                                        <td style="text-align:center; border: 1px solid black;" colspan="99">
                                            <span t-field="line.name" t-options="{'widget': 'text'}" />
                                        </td>
                                    </t>

                                </tr>
                            </t>
                            <tr>
                                <td colspan="2" style="text-align:center; border: 1px solid black;">

                                </td>
                                <td colspan="6" style="text-align:center; border: 1px solid black;">
                                    Total BeforeThe VAT:\ الاجمالي قبل الضريبة المضافة
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    <t t-esc="o.amount_untaxed" t-options="{&quot;widget&quot;: &quot;float&quot;, &quot;precision&quot;: 2}"/>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" style="text-align:center; border: 1px solid black;">

                                </td>
                                <td colspan="6" style="text-align:center; border: 1px solid black;">
                                    VAT: Standard Rate (15%):\ قيمة الضريبة
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    <t t-esc="o.amount_tax" t-options="{&quot;widget&quot;: &quot;float&quot;, &quot;precision&quot;: 2}"/>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" style="text-align:center; border: 1px solid black;">

                                </td>
                                <td colspan="6" style="text-align:center; border: 1px solid black;">
                                    Gross Invoice amount SAR :\ مبلغ الفاتورة الاجمالي
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    <t t-esc="o.amount_total" t-options="{&quot;widget&quot;: &quot;float&quot;, &quot;precision&quot;: 2}"/>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" style="text-align:center; border: 1px solid black;">

                                </td>
                                <td colspan="6" style="text-align:center; border: 1px solid black;">
                                     Retention SAR :\  مبلغ محتجز <t t-esc="o.x_dedication"/> %  من الفاتورة  
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    <t t-esc="o.lessRetention" t-options="{&quot;widget&quot;: &quot;float&quot;, &quot;precision&quot;: 2}"/>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" style="text-align:center; border: 1px solid black;">

                                </td>
                                <td colspan="6" style="text-align:center; border: 1px solid black;">
                                    Net Amount Recivable After Retention SAR:\ صافي الفاتورة بعد حجز المبلغ مسترد
                                </td>
                                <td style="text-align:center; border: 1px solid black;">
                                    <t t-esc="o.netTotal" t-options="{&quot;widget&quot;: &quot;float&quot;, &quot;precision&quot;: 2}" />
                                </td>
                            </tr>
                            <tr>
                                <td colspan="9" style="text-align:center; border: 1px solid black;">
                                    <span t-esc="o.amount_to_text_custom(o.amount_total, 'en')" />
                                    <br />
                                    <span t-esc="o.amount_to_text_custom(o.amount_total, 'ar')" />
                                </td>
                            </tr>
                            <!-- <t
                                    t-if="current_section and (line_last or lines[line_index+1].display_type == 'line_section')">
                                    <tr class="is-subtotal text-right">
                                        <td style="text-align:center; border: 1px solid black;" colspan="99">
                                            <strong class="mr16">Subtotal</strong>
                                            <span t-esc="current_subtotal"
                                                t-options="{&quot;widget&quot;: &quot;monetary&quot;, &quot;display_currency&quot;: o.currency_id}" />
                                        </td>
                                    </tr>
                                </t> -->
                            <!-- </t> -->
                        </tbody>
                    </table>
                    <!-- end here -->
                    <br></br>
                    <p style="text-align: left; text-decoration: underline; padding-left: 140px;">
                        FINANCE MANAGER/ المدير المالي</p>

                        <table style="border: 1px solid black; width: 40%; height: 10%; float: right; direction: rtl;">
                        <tr>
                            <th colspan="3" style="border: 1px solid black; text-align: right; padding: 8px;">
                                <h4 style=" text-align: center;"> Account Numbers</h4>
                            </th>
                        </tr>
                        <tr>
                            <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                Bank Name
                            </th>
                            <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                IBAN
                            </th>
                            <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                <!--<p t-field="liness.bankimage1" /> -->
                            </th>
                        </tr>
                        <t t-foreach="o.partner_id.bankditel2" t-as="liness">
                            <tr>
                                <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                    <p t-field="liness.bankName1" />
                                </th>
                                <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                    <p t-field="liness.IBN1" />
                                </th>
                                <th style="border: 1px solid black; text-align: right; padding: 8px; font-size: 10px">
                                    <p t-field="liness.bankimage1" />
                                </th>
                            </tr>
                        </t>
                    </table>
                     &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;
                    <!-- <br/>
                  <br/> -->
                    <table style="border: 1px solid black; width: 40%; height: 10%; float: right; direction: ltr;">
                        <tr>
                            <th
                                style="border-bottom: 1px solid black; text-align: left; padding: 8px; font-size: 10px;">
                                Recieved Original Invoice
                            </th>
                            <th
                                style="direction: rtl; border-bottom: 1px solid black; text-align: right; padding: 8px; font-size: 7px">
                                توقيع استلام الفاتورة
                            </th>
                        </tr>
                        <tr>
                            <th style="border-bottom: 1px solid black; text-align: left; padding: 8px; font-size: 7px">
                                Sign:
                            </th>
                            <th
                                style="direction: rtl; border-bottom: 1px solid black; text-align: right; padding: 8px; font-size: 7px">
                                التوقيع:
                            </th>
                        </tr>
                        <tr>
                            <th style="border-bottom: 1px solid black; text-align: left; padding: 8px; font-size: 7px">
                                Name:
                            </th>
                            <th
                                style="direction: rtl; border-bottom: 1px solid black; text-align: right; padding: 8px; font-size: 7px">
                                الأسم:
                            </th>
                        </tr>
                        <tr>
                            <th style="border-bottom: 1px solid black; text-align: left; padding: 8px; font-size: 7px">
                                Date:
                            </th>
                            <th
                                style="direction: rtl; border-bottom: 1px solid black; text-align: right; padding: 8px; font-size: 7px">
                                التاريخ:
                            </th>
                        </tr>
                    </table>
                    <br/><br/><br/><br/><br/><br/><br/><br/><br/>
                     &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;
                     &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;
                     &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;
                     &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;
                     &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;
                     &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;
                     &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;
                     &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;

                    
                    <img t-att-src="'/report/barcode/?barcode_type=%s&amp;value=%s&amp;width=%s&amp;height=%s'%('QR', o.l10n_sa_qr_code_str, 100, 100)" />
                </div>
                <!-- <div class="footer" style="font-family: 'Tajawal', sans-serif;">
                    <div t-if="report_type == 'pdf'" style="font-family: 'Tajawal', sans-serif; text-align:center;">
                        Page: <span class="page" /> / <span class="topage" />
                    </div>
                </div> -->
            </t>
        </t>
    </template>
</odoo>