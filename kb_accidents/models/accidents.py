
from odoo import api, fields, models, _
from logging import getLogger
from odoo.exceptions import ValidationError
import logging
from datetime import date,datetime, timedelta
_logger = logging.getLogger(__name__)


class accidents(models.Model):
    _name = "accidents"
    _table = "accidents"
    _rec_name = "accidents_ids"
    _inherit = ['mail.thread', 'mail.activity.mixin']

    recname = fields.Char("A-")
    user_id = fields.Many2one('res.users', string="Create By:", index=True, tracking=2,
                              default=lambda self: self.env.user, readonly=1)
    electronicNumer_accd = fields.Char("Asset Number")
    accidentsDescription = fields.Text("Accident Description")
    accidentsDate = fields.Date("Date of Accidents")
    accidentsAttachment = fields.Many2many('ir.attachment', string="Attachments")
    accidentNumber = fields.Char("Accident Number")
    driver_name = fields.Many2many('hr.employee', string="Drivers Name")
    accidentsStatus = fields.Char("Accident Status")
    ############################### Vehicle from fleet Module#######################################################################
    
    carModel_acc = fields.Char("Model")
    # busNameAR_acc = fields.Char("Bus Name Arabic")
    # busNameEN_acc = fields.Char("Bus Name English")
    searialNumbers_acc = fields.Char("Serial Number")
    plateNumberAR_acc = fields.Char("Plate Number (Arabic)")
    ownerName_acc = fields.Char("Owner Name")
    electronicNumer_acc = fields.Char("Asset Number")
    insuranceName_acc = fields.Char("Insurance Name")
    insuranceStartDate_acc = fields.Date("Insurance Start Date")
    insuranceEndDate_acc = fields.Date("Insurance End Date")
    modelYear_acc = fields.Char("Model Year")
    passengerCapacity_acc = fields.Char("Passenger Capacity")
    registrationEndDate_acc = fields.Date("Registration Expiration Date")
    ownerElectronicumber_acc = fields.Char("Electronic Owner Number")
    status_field_acc = fields.Char("Vehicle Status")
    plateNumberAR_order = fields.Char("Plate Number (Arabic)")
    plateNumberEN_order = fields.Char("Plate Number (English)")


    @api.onchange('electronicNumer_accd')
    def _get_vehicle_info(self):
        if self.electronicNumer_accd:
            fleet_id = self.env['fleet.vehicle'].search([('electronicNumer', '=', self.electronicNumer_accd)])

            for fleet_ids in fleet_id:
                self.carModel_acc = fleet_ids.model_id.name
                self.modelYear_acc = fleet_ids.modelYear
                # self.busNameAR_acc = fleet_ids.busNameAR
                # self.busNameEN_acc = fleet_ids.busNameEN
                self.searialNumbers_acc = fleet_ids.searialNumbers
                self.ownerName_acc = fleet_ids.ownerName.name
                self.electronicNumer_acc = fleet_ids.electronicNumer
                self.insuranceName_acc = fleet_ids.insuranceName
                self.insuranceStartDate_acc = fleet_ids.insuranceStartDate
                self.insuranceEndDate_acc = fleet_ids.insuranceEndDate

                self.passengerCapacity_acc = fleet_ids.passengerCapacity
                self.registrationEndDate_acc = fleet_ids.registrationEndDate
                self.ownerElectronicumber_acc = fleet_ids.ownerElectronicumber
                self.status_field_acc = fleet_ids.status_field.name
                self.plateNumberAR_order = fleet_ids.plateNumberAR
                self.plateNumberEN_order = fleet_ids.license_plate


    state = fields.Selection([
        ('draft', 'Draft'),
        ('T1', 'Documents Received'),
        ('T2', 'In Insurance'),
        ('T3', 'Compensation'),
        ('complete', 'Complete'),

    ], string='Status', default="draft", help='Choose the state', tracking=True)

    # def action_draft(self):
    #     self.state = 'draft'
    #
    # def action_T1(self):
    #     self.state = 'T1'
    #
    # def action_T2(self):
    #     self.state = 'T2'
    #
    # def action_T3(self):
    #     self.state = 'T3'
    #
    # def action_T4(self):
    #     self.state = 'T4'
        # actions = self.env.ref('kb_car_workshop_s.completes_wizard').read()[0]
        # return actions

    # def action_cancel(self):
    #     self.state = 'cancel'
        # action = self.env.ref('kb_car_workshop_s.create_appointment_wizard').read()[0]
        # return action

    accidents_ids = fields.Char(string='Accident ID', required=True, readonly=True, default=lambda self: _('New'))
    G = "AC"

    note = fields.Char(string='')

    @api.model
    def create(self, vals):
        if not vals.get('note'):
            vals['note'] = 'New'
        if vals.get('accidents_ids', ('New')) == ('New'):
            vals['accidents_ids'] = self.env['ir.sequence'].next_by_code(
                'accidents') or ('New')
        res = super(accidents, self).create(vals)
        return res

    # def name_get(self):
    #     result = []
    #     for record in self:
    #         record_name = '[' + self.G + '] ' + record.accidents_ids
    #         result.append((record.id, record_name))
    #     return result

    order_count = fields.Integer(compute='_order_count', string='# Accidents')

    def _order_count(self):
        for each in self:
            order_id = self.env['accidents'].search([('electronicNumer_accd', '=', self.electronicNumer_accd)])
            each.order_count = len(order_id)

    def accidents_smart_button(self):
        self.ensure_one()
        domain = [('electronicNumer_accd', '=', self.electronicNumer_accd)]

        return {
            'name': _('accidents'),
            'domain': domain,
            'res_model': 'accidents',
            'type': 'ir.actions.act_window',
            'view_id': False,
            'view_mode': 'tree,form',
            'view_type': 'form',
            'context': "{'default_electronicNumer_accd': '%s'}" % self.id,
        }
