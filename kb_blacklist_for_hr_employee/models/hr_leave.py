# -*- coding: utf-8 -*-
from odoo import api, fields, models
from datetime import date, datetime
from odoo.exceptions import  ValidationError
from odoo import _

class kb_hr_hr_leave(models.Model):
  _inherit = "hr.leave"


  @api.onchange('employee_ids')
  def check_if_can_have_Violation(self):
    employee= self.env['hr.employee'].search([('id','=',self.employee_id.id)])
    for recod in employee:
      for record in recod.kb_black_list_id:
        if record.violation_check == False:
          raise ValidationError(_("Sorry, can't get time off  for this employee."))
