# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bi_pos_payment
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-12 07:15+0000\n"
"PO-Revision-Date: 2023-05-12 07:15+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: bi_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_pos_config__allow_pos_invoice
#: model:ir.model.fields,field_description:bi_pos_payment.field_res_config_settings__allow_pos_invoice
msgid "Allow POS Invoice Payment and Validation"
msgstr "Permitir pago y validación de facturas de TPV"

#. module: bi_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_pos_config__allow_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_res_config_settings__allow_pos_payment
msgid "Allow POS Payments"
msgstr "Permitir pagos POS"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Amount Due:"
msgstr "Cantidad adeudada:"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/js/Popup/RegisterInvoicePaymentPopupWidget.js:0
#, python-format
msgid "Amount Error"
msgstr "error de cantidad"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Amount:"
msgstr "Cantidad:"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Cancelled"
msgstr "Cancelada"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Close"
msgstr "Cerca"

#. module: bi_pos_payment
#: model:ir.model,name:bi_pos_payment.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: bi_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_pos_create_customer_payment__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: bi_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_pos_create_customer_payment__create_date
msgid "Created on"
msgstr "Creado en"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Customer Ref"
msgstr "Referencia del cliente"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Customer:"
msgstr "Cliente:"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Discard"
msgstr "Desechar"

#. module: bi_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_pos_create_customer_payment__display_name
msgid "Display Name"
msgstr "Nombre para mostrar"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Draft/New"
msgstr "Borrador / Nueva"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Due Amount"
msgstr "Cantidad debida"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Enter Payment Details Here"
msgstr "Ingrese los detalles de pago aquí"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/js/Popup/RegisterInvoicePaymentPopupWidget.js:0
#, python-format
msgid "Entered amount is larger then due amount. please enter valid amount"
msgstr "El monto ingresado es mayor que el monto adeudado. por favor ingrese una cantidad válida"

#. module: bi_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_pos_create_customer_payment__id
msgid "ID"
msgstr "IDENTIFICACIÓN"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "In Payment"
msgstr "En pago"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Invoice"
msgstr "Factura"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Invoice Details"
msgstr "Detalles de la factura"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Invoicing App Legacy"
msgstr "Legado de la aplicación de facturación"

#. module: bi_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_pos_create_customer_payment____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: bi_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_pos_create_customer_payment__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: bi_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_pos_create_customer_payment__write_date
msgid "Last Updated on"
msgstr "Ultima actualización en"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Loading...."
msgstr "Cargando...."

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Not Paid"
msgstr "No pagada"

#. module: bi_pos_payment
#: model:ir.model.fields,field_description:bi_pos_payment.field_account_payment__notes_pos
msgid "Notes"
msgstr "notas"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/js/Screens/POSInvoiceScreen.js:0
#, python-format
msgid "Offline"
msgstr "Desconectada"

#. module: bi_pos_payment
#: model_terms:ir.ui.view,arch_db:bi_pos_payment.bi_pos_reports
msgid "POS Payment Configuration"
msgstr "Configuración de pago de TPV"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Paid"
msgstr "Pagada"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Partially Paid"
msgstr "Parcialmente pagada"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Payment"
msgstr "Pago"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Payment Journal:"
msgstr "Diario de pagos:"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Payment Note:"
msgstr "Nota de pago:"

#. module: bi_pos_payment
#: model_terms:ir.ui.view,arch_db:bi_pos_payment.inherit_view_payment_form_notes
msgid "Payment Notes"
msgstr "Notas de pago"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Payment State"
msgstr "Estado de pago"

#. module: bi_pos_payment
#: model:ir.model,name:bi_pos_payment.model_account_payment
msgid "Payments"
msgstr "Pagos"

#. module: bi_pos_payment
#: model:ir.model,name:bi_pos_payment.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuración de punto de venta"

#. module: bi_pos_payment
#: model:ir.model,name:bi_pos_payment.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesión de punto de venta"

#. module: bi_pos_payment
#: model:ir.model,name:bi_pos_payment.model_pos_create_customer_payment
msgid "Pos Create Customer Payment"
msgstr "Pos Crear pago del cliente"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Posted"
msgstr "Al corriente"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Register Payment"
msgstr "Registrar Pago"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Reversed"
msgstr "Invertida"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "State"
msgstr "Estado"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Status:"
msgstr "Estado:"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Total Amount"
msgstr "Cantidad total"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/xml/pos_payment.xml:0
#, python-format
msgid "Total Amount:"
msgstr "Cantidad total:"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/js/Screens/POSInvoiceScreen.js:0
#, python-format
msgid "Unable to load orders."
msgstr "No se pueden cargar los pedidos."

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/js/Screens/ClientListScreen.js:0
#: code:addons/bi_pos_payment/static/src/js/Screens/POSInvoiceScreen.js:0
#, python-format
msgid "Unknown customer"
msgstr "Cliente desconocida"

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/js/Screens/POSInvoiceScreen.js:0
#, python-format
msgid "You cannot Register Payment. Select Invoice first."
msgstr "No puede Registrar Pago. Seleccione Factura primero."

#. module: bi_pos_payment
#. odoo-javascript
#: code:addons/bi_pos_payment/static/src/js/Screens/ClientListScreen.js:0
#, python-format
msgid "You cannot Register Payment. Select customer first."
msgstr "No puede Registrar Pago. Seleccione el cliente primero."
