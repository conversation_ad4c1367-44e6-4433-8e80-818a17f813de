# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_user_role
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0c\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-12-17 02:07+0000\n"
"PO-Revision-Date: 2016-12-17 02:07+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2016\n"
"Language-Team: Indonesian (https://www.transifex.com/oca/teams/23907/id/)\n"
"Language: id\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_groups__role_count
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__role_count
msgid "# Roles"
msgstr ""

#. module: base_user_role
#. odoo-python
#: code:addons/base_user_role/models/role.py:0
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: base_user_role
#: model_terms:ir.ui.view,arch_db:base_user_role.view_res_users_form_inherit
msgid "<em>Any configuration changes made here will not be persistent.</em>"
msgstr ""

#. module: base_user_role
#: model_terms:ir.ui.view,arch_db:base_user_role.view_res_users_form_inherit
msgid "<strong>The access rights of this user are managed by roles.</strong>"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__model_access
msgid "Access Controls"
msgstr ""

#. module: base_user_role
#: model:ir.model,name:base_user_role.model_res_groups
msgid "Access Groups"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__menu_access
msgid "Access Menu"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__model_access_ids
#: model_terms:ir.ui.view,arch_db:base_user_role.view_res_users_role_form
msgid "Access Rights"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__active
msgid "Active"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__category_id
msgid "Application"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_wizard_create_role_from_user__assign_to_user
msgid "Assign to user"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__group_category_id
msgid "Associated category"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__group_id
msgid "Associated group"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,help:base_user_role.field_res_users_role__group_category_id
msgid "Associated group's category"
msgstr ""

#. module: base_user_role
#: model_terms:ir.ui.view,arch_db:base_user_role.group_groups_into_role_wiz_view
msgid "Cancel"
msgstr ""

#. module: base_user_role
#: model_terms:ir.ui.view,arch_db:base_user_role.create_from_user_wizard_view
msgid "Close"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__color
msgid "Color Index"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__comment
msgid "Comment"
msgstr ""

#. module: base_user_role
#: model_terms:ir.ui.view,arch_db:base_user_role.create_from_user_wizard_view
#: model_terms:ir.ui.view,arch_db:base_user_role.group_groups_into_role_wiz_view
msgid "Create"
msgstr ""

#. module: base_user_role
#: model:ir.actions.act_window,name:base_user_role.action_wizard_groups_into_role
msgid "Create Role"
msgstr ""

#. module: base_user_role
#: model:ir.actions.act_window,name:base_user_role.create_from_user_wizard_action
#: model_terms:ir.ui.view,arch_db:base_user_role.create_from_user_wizard_view
msgid "Create role from user"
msgstr ""

#. module: base_user_role
#: model:ir.model,name:base_user_role.model_wizard_create_role_from_user
msgid "Create role from user wizard"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__create_uid
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__create_uid
#: model:ir.model.fields,field_description:base_user_role.field_wizard_create_role_from_user__create_uid
#: model:ir.model.fields,field_description:base_user_role.field_wizard_groups_into_role__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__create_date
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__create_date
#: model:ir.model.fields,field_description:base_user_role.field_wizard_create_role_from_user__create_date
#: model:ir.model.fields,field_description:base_user_role.field_wizard_groups_into_role__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__display_name
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__display_name
#: model:ir.model.fields,field_description:base_user_role.field_wizard_create_role_from_user__display_name
#: model:ir.model.fields,field_description:base_user_role.field_wizard_groups_into_role__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__is_enabled
msgid "Enabled"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__date_from
msgid "From"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__full_name
msgid "Group Name"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,help:base_user_role.field_res_users_role__share
msgid "Group created to set access rights for sharing data with some users."
msgstr ""

#. module: base_user_role
#: model:ir.model,name:base_user_role.model_wizard_groups_into_role
msgid "Group groups into a role"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,help:base_user_role.field_wizard_groups_into_role__name
msgid "Group groups into a role and specify a name for this role"
msgstr ""

#. module: base_user_role
#: model_terms:ir.ui.view,arch_db:base_user_role.view_res_users_role_form
msgid "Groups"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__id
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__id
#: model:ir.model.fields,field_description:base_user_role.field_wizard_create_role_from_user__id
#: model:ir.model.fields,field_description:base_user_role.field_wizard_groups_into_role__id
msgid "ID"
msgstr "ID"

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__implied_ids
msgid "Inherits"
msgstr ""

#. module: base_user_role
#: model_terms:ir.ui.view,arch_db:base_user_role.view_res_users_role_form
msgid "Internal Notes"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,help:base_user_role.field_res_groups__parent_ids
#: model:ir.model.fields,help:base_user_role.field_res_users_role__parent_ids
msgid ""
"Inverse relation for the Inherits field. The groups from which this group is "
"inheriting"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role____last_update
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line____last_update
#: model:ir.model.fields,field_description:base_user_role.field_wizard_create_role_from_user____last_update
#: model:ir.model.fields,field_description:base_user_role.field_wizard_groups_into_role____last_update
msgid "Last Modified on"
msgstr "Terakhir Dimodifikasi pada"

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__write_uid
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__write_uid
#: model:ir.model.fields,field_description:base_user_role.field_wizard_create_role_from_user__write_uid
#: model:ir.model.fields,field_description:base_user_role.field_wizard_groups_into_role__write_uid
msgid "Last Updated by"
msgstr "Diperbaharui oleh"

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__write_date
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__write_date
#: model:ir.model.fields,field_description:base_user_role.field_wizard_create_role_from_user__write_date
#: model:ir.model.fields,field_description:base_user_role.field_wizard_groups_into_role__write_date
msgid "Last Updated on"
msgstr "Diperbaharui pada"

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__model_access_count
msgid "Model Access Count"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__name
#: model:ir.model.fields,field_description:base_user_role.field_wizard_create_role_from_user__name
#: model:ir.model.fields,field_description:base_user_role.field_wizard_groups_into_role__name
msgid "Name"
msgstr "Nama"

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_groups__trans_parent_ids
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__trans_parent_ids
msgid "Parent Groups"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_groups__parent_ids
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__parent_ids
msgid "Parents"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__rule_ids
#: model_terms:ir.ui.view,arch_db:base_user_role.view_res_users_role_form
msgid "Record Rules"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,help:base_user_role.field_res_groups__role_id
#: model:ir.model.fields,help:base_user_role.field_res_users_role__role_id
msgid "Relation for the groups that represents a role"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_groups__role_id
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__role_id
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__role_id
msgid "Role"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users__role_line_ids
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__line_ids
msgid "Role lines"
msgstr ""

#. module: base_user_role
#: model:ir.actions.act_window,name:base_user_role.action_res_users_role_tree
#: model:ir.model.fields,field_description:base_user_role.field_res_groups__role_ids
#: model:ir.model.fields,field_description:base_user_role.field_res_users__role_ids
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__role_ids
#: model:ir.ui.menu,name:base_user_role.menu_action_res_users_role_tree
#: model_terms:ir.ui.view,arch_db:base_user_role.res_groups_view_form
#: model_terms:ir.ui.view,arch_db:base_user_role.view_res_users_form_inherit
#: model_terms:ir.ui.view,arch_db:base_user_role.view_res_users_role_search
msgid "Roles"
msgstr ""

#. module: base_user_role
#: model:ir.model.constraint,message:base_user_role.constraint_res_users_role_line_user_role_uniq
msgid "Roles can be assigned to a user only once at a time"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,help:base_user_role.field_res_groups__role_ids
#: model:ir.model.fields,help:base_user_role.field_res_users_role__role_ids
msgid "Roles in which the group is involved"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__rule_groups
msgid "Rules"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__rules_count
msgid "Rules Count"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__share
msgid "Share Group"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users__show_alert
msgid "Show Alert"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__date_to
msgid "To"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__trans_implied_ids
msgid "Transitively inherits"
msgstr ""

#. module: base_user_role
#: model:ir.actions.server,name:base_user_role.cron_update_users_ir_actions_server
#: model:ir.cron,cron_name:base_user_role.cron_update_users
msgid "Update user roles"
msgstr ""

#. module: base_user_role
#: model:ir.model,name:base_user_role.model_res_users
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role_line__user_id
msgid "User"
msgstr ""

#. module: base_user_role
#: model:ir.model,name:base_user_role.model_res_users_role
msgid "User role"
msgstr ""

#. module: base_user_role
#: model:ir.module.category,name:base_user_role.ir_module_category_role
msgid "User roles"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__users
#: model_terms:ir.ui.view,arch_db:base_user_role.view_res_users_role_form
msgid "Users"
msgstr ""

#. module: base_user_role
#: model:ir.model,name:base_user_role.model_res_users_role_line
msgid "Users associated to a role"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__user_ids
msgid "Users list"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,help:base_user_role.field_res_users_role__implied_ids
msgid "Users of this group automatically inherit those groups"
msgstr ""

#. module: base_user_role
#: model:ir.model.fields,field_description:base_user_role.field_res_groups__view_access
#: model:ir.model.fields,field_description:base_user_role.field_res_users_role__view_access
msgid "Views"
msgstr "Dilihat"

#. module: base_user_role
#: model_terms:ir.ui.view,arch_db:base_user_role.create_from_user_wizard_view
msgid "or"
msgstr ""
