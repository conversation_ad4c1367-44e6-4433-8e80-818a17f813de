<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="sh_product_template_only_form_inherit_view" model="ir.ui.view">
        <field name="name">sh.product.template.only.form.inherit.view</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view" />
        <field name="arch" type="xml">
            <field name="categ_id" position="after">
                <field name="minimum_quantity" />
            </field>
        </field>
    </record>
    
    <record id="sh_product_normal_only_form_inherit_view" model="ir.ui.view">
        <field name="name">sh.product.normal.only.form.inherit.view</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view" />
        <field name="arch" type="xml">
            <field name="categ_id" position="after">
                <field name="minimum_quantity" attrs="{'invisible': [('product_variant_count', '>', 1)]}" />
            </field>
        </field>
    </record>

    <record id="sh_product_variant_easy_edit_view" model="ir.ui.view">
        <field name="name">product.variant.easy.edit.view</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_variant_easy_edit_view" />
        <field name="arch" type="xml">
            <field name="barcode" position="after">
                <field name="minimum_quantity" />
            </field>
        </field>
    </record>

</odoo>
