# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll_account_community
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Galician (https://www.transifex.com/odoo/teams/41243/gl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_account_community
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_community.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_community.hr_salary_rule_form_inherit
msgid "Accounting"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_move_id
msgid "Accounting Entry"
msgstr ""

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:113
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:128
#, python-format
msgid "Adjustment Entry"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_contract_analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line_analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule_analytic_account_id
msgid "Analytic Account"
msgstr "Conta analítica"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line_account_credit
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule_account_credit
msgid "Credit Account"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_date
msgid "Date Account"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line_account_debit
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule_account_debit
msgid "Debit Account"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_payslip_date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip
msgid "Pay Slip"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_line
msgid "Payslip Line"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_run
msgid "Payslip Run"
msgstr ""

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:64
#, python-format
msgid "Payslip of %s"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_contract_journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_run_journal_id
msgid "Salary Journal"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line_account_tax_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule_account_tax_id
msgid "Tax"
msgstr ""

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:111
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr ""

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:126
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr ""

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_salary_rule
msgid "hr.salary.rule"
msgstr ""
