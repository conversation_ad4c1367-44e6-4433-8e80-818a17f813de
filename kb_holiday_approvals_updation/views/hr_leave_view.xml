<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="inherit_hr_leave_additional_approvals" model="ir.ui.view">
        <field name="name">hr.leave.additional_approvals.view.form.inherit</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_form_manager"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="replace">
                <header>
                    <button string="Confirm" name="action_confirm" type="object" class="oe_highlight" attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('active', '=', False)]}"/>
                    <button string="Approve" name="action_approve" type="object" class="oe_highlight" attrs="{'invisible': ['|', '|', ('active', '=', False), ('can_approve', '=', False), ('state', '!=', 'confirm')]}"/>
                    <button string="Validate" name="action_addtional_approval" states="additional_validate" type="object" groups="kb_holiday_approvals_updation.group_additional_holiday_administrator" class="oe_highlight"/>
                    <button string="Validate" name="action_validate" states="validate1" type="object" groups="hr_holidays.group_hr_holidays_user" class="oe_highlight"/>
                    <button string="Refuse" name="action_refuse" type="object" attrs="{'invisible': ['|', '|', ('active', '=', False), ('can_approve', '=', False), ('state', 'not in', ('confirm','validate1','validate','additional_validate'))]}"/>
                    <button string="Cancel" name="action_cancel" type="object" attrs="{'invisible': ['|', ('active', '=', False), ('can_cancel', '=', False)]}"/>
                    <button string="Mark as Draft" name="action_draft" type="object" attrs="{'invisible': ['|', ('can_reset', '=', False), ('state', 'not in', ['confirm', 'refuse'])]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="confirm,validate" attrs="{'invisible': [('active', '=', False)]}"/>
                </header>
            </xpath> 
        </field>
    </record>




</odoo>
