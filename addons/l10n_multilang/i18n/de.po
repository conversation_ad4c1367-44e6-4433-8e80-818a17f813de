# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_multilang
# 
# Translators:
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-23 08:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account
msgid "Account"
msgstr "Konto"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontenplanvorlage"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_group
msgid "Account Group"
msgstr "Kontengruppe"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account__name
msgid "Account Name"
msgstr "Kontoname"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account_tag
msgid "Account Tag"
msgstr "Konto-Stichwort"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_analytic_account
#: model:ir.model.fields,field_description:l10n_multilang.field_account_analytic_account__name
msgid "Analytic Account"
msgstr "Kostenstelle"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_template__description
msgid "Display on Invoices"
msgstr "Auf Rechnungen anzeigen"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_fiscal_position
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position__name
msgid "Fiscal Position"
msgstr "Steuerposition"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position_template__name
msgid "Fiscal Position Template"
msgstr "Steuerpositionsvorlage"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_base_language_install
msgid "Install Language"
msgstr "Sprache installieren"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_journal__name
msgid "Journal Name"
msgstr "Journalname"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax__description
msgid "Label on Invoices"
msgstr "Bezeichnung auf Rechnungen"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_account_fiscal_position__note
msgid "Legal mentions that have to be printed on the invoices."
msgstr ""
"Gesetzliche Vorgaben, die auf den Rechnungen ausgegeben werden müssen."

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account_template__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_chart_template__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_group__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_group_template__name
msgid "Name"
msgstr "Name"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position__note
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position_template__note
msgid "Notes"
msgstr "Notizen"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_chart_template__spoken_languages
msgid "Spoken Languages"
msgstr "Gesprochene Sprachen"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account_tag__name
msgid "Tag Name"
msgstr "Stichwortbezeichnung"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax
msgid "Tax"
msgstr "Steuer"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_template__name
msgid "Tax Name"
msgstr "Steuerbezeichnung"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_group_template
msgid "Template for Account Groups"
msgstr "Vorlage für Kontengruppen"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Vorlage für die Steuerposition"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account_template
msgid "Templates for Accounts"
msgstr "Vorlagen für Konten"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Vorlagen für Steuern"
