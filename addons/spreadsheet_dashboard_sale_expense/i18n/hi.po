# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_sale_expense
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:44+0000\n"
"PO-Revision-Date: 2022-12-16 09:48+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "# Expenses"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Category"
msgstr "वर्ग"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Employee"
msgstr "कर्मचारी"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expense"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis by Customer to Reinvoice"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - Expenses"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To reimburse"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To report"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To validate"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Order"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Period"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Product"
msgstr "उत्पाद"

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To reimburse"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To report"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To validate"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Categories"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Employees"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Expenses"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Reinvoiced Orders"
msgstr ""

#. module: spreadsheet_dashboard_sale_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_sale_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Total"
msgstr ""
