# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 20:34+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#, python-format
msgid " Add Images"
msgstr "Agregar imágenes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "\" alert with a"
msgstr "\" alerta con un"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "\"%s form submission\" <%s>"
msgstr "\"%s envío de formulario\" <%s>"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL from\" can not be empty."
msgstr "\"URL de\" no puede estar vacío."

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" can not be empty."
msgstr "\"URL a\" no puede estar vacío."

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" cannot contain parameter %s which is not used in \"URL from\"."
msgstr "\"URL a\" no puede contener un parámetro %s que no se utilice en \"URL de\"."

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" is invalid: %s"
msgstr "\"URL a\" es inválido: %s"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must contain parameter %s used in \"URL from\"."
msgstr "\"URL a\" debe contener el parámetro %s que se utilizó en \"URL de\"."

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must start with a leading slash."
msgstr "\"URL para\" debe comenzar con una barra diagonal."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__page_count
msgid "# Visited Pages"
msgstr "# Páginas visitadas"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__visit_count
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "# Visits"
msgstr "# Visitas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$10.50"
msgstr "$10.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$12.00"
msgstr "$12.00"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$15.50"
msgstr "$15.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$7.50"
msgstr "$7.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$9.00"
msgstr "$9.00"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "%s List"
msgstr "%s Lista"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.js:0
#, python-format
msgid "%s record(s) selected, are you sure you want to publish them all?"
msgstr "%s registros seleccionados, ¿está seguro de que desea publicar todo?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;/body&amp;gt;"
msgstr "&amp;lt;/body&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;head&amp;gt;"
msgstr "&amp;lt;head&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&gt;"
msgstr "&gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "' did not match any pages."
msgstr "' no coincide con ninguna página."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "' did not match anything."
msgstr "\" no coincide con nada."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid ""
"' did not match anything.\n"
"                        Results are displayed for '"
msgstr ""
"\" no coincidió con nada.\n"
"                        Se muestran los resultados para \""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "' to link to an anchor."
msgstr "\" para vincular a un ancla."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"' to search a page.\n"
"                    '"
msgstr ""
"\" para buscar una página.\n"
"                    \""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' no es una fecha correcta"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' no es una fecha y hora correctas"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "'. Showing results for '"
msgstr "'. Mostrando resultados para '"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "(could be used in"
msgstr "(podría utilizarse en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "+ Field"
msgstr "+ Campo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "+ New Website"
msgstr "+ Nuevo sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_2
msgid "+1 ************"
msgstr "+1 ************"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ", autor:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
".\n"
"                                                The website will still work if you reject or discard those cookies."
msgstr ""
".\n"
"                                                El sitio web seguirá funcionando aunque rechace o descarte las cookies."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid ""
".\n"
"                    Changing its name will break these calls."
msgstr ""
".\n"
"                    Cambiar su nombre romperá estas llamadas."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "...and switch the timeline contents to fit your needs."
msgstr ""
"... y cambie el contenido de su línea de tiempo según sus necesidades."

#. module: website
#: model:website,contact_us_button_url:website.default_website
#: model:website,contact_us_button_url:website.website2
#: model_terms:ir.ui.view,arch_db:website.layout
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "/contactus"
msgstr "/contactus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1 km"
msgstr "1 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/2 - 1/2"
msgstr "1/2 - 1/2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/3 - 2/3"
msgstr "1/3 - 2/3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/4 - 3/4"
msgstr "1/4 - 3/4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "10 m"
msgstr "10 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 km"
msgstr "100 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 m"
msgstr "100 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "100%"
msgstr "100%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1000 km"
msgstr "1000 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "12"
msgstr "12"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "15 km"
msgstr "15 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "2 <span class=\"visually-hidden\">(current)</span>"
msgstr "2 <span class=\"visually-hidden\">(actual)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2 km"
msgstr "2 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2.5 m"
msgstr "2.5 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "20 m"
msgstr "20 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 km"
msgstr "200 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 m"
msgstr "200 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2000 km"
msgstr "2000 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr "Soporte técnico gratuito 24/7"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "25%"
msgstr "25%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"
msgstr ""
"Executive Park Blvd 250, Suite 3400 <br/> San Francisco CA 94134 "
"<br/>Estados Unidos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
msgid ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"Executive Park Blvd 250, Suite 3400 • San Francisco CA 94134 • Estados "
"Unidos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "30 km"
msgstr "30 km"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/fields.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__301
#, python-format
msgid "301 Moved permanently"
msgstr "301 Movido de forma permanente"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/fields.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__302
#, python-format
msgid "302 Moved temporarily"
msgstr "302 Movido de forma temporal"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__308
msgid "308 Redirect / Rewrite"
msgstr "308 Redireccionar/Reescribir"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "4 km"
msgstr "4 km"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "4 steps"
msgstr "4 pasos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 km"
msgstr "400 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 m"
msgstr "400 m"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__404
msgid "404 Not Found"
msgstr "404 No encontrado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "5 m"
msgstr "5 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 km"
msgstr "50 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 m"
msgstr "50 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "50%"
msgstr "50%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "50,000+ companies run Odoo to grow their businesses."
msgstr "Más de 50,000 empresas utilizan Odoo para hacer crecer sus empresas."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "75%"
msgstr "75%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "8 km"
msgstr "8 km"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_progress_bar/options.js:0
#, python-format
msgid "80% Development"
msgstr "80% de desarrollo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr ""
"<b>Más de 50,000 empresas</b> utilizan Odoo para hacer crecer sus empresas."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Add</b> the selected image."
msgstr "<b>Agregue</b> la imagen seleccionada."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr ""
"<b>Haga clic en editar</b> para empezar a diseñar su pagina de inicio."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a snippet</b> to access its options menu."
msgstr "<b>Haga clic en el snippet</b> para acceder a su menú de opciones."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a text</b> to start editing it."
msgstr "<b>Haga clic en un texto</b> para empezar a editarlo."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this column to access its options."
msgstr "<b>Haga clic</b> en esta columna para acceder a sus opciones."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this header to configure it."
msgstr "<b>Haga clic</b> en este encabezado para configurarlo."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this option to change the %s of the block."
msgstr "<b>Haga clic</b> en esta opción para cambiar el %s del bloque."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"color of this block."
msgstr ""
"<b>Personalice</b> cualquier bloque a través de este menú. Intente cambiar "
"el color de fondo de este bloque."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"image of this block."
msgstr ""
"<b>Personalice</b> cualquier bloque a través de este menú. Intente cambiar "
"la imagen de fondo de este bloque."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "<b>Designed</b> <br/>for Companies"
msgstr "<b>Diseñado</b> <br/>para empresas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<b>Designed</b> for companies"
msgstr "<b>Diseñado</b> para empresas"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an icon</b> to change it with one of your choice."
msgstr ""
"<b>Haga doble clic en un icono</b> para cambiarlo por uno de su elección."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an image</b> to change it with one of your choice."
msgstr ""
"<b>Haga doble clic en una imagen</b> para cambiarla por una de su elección."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"
msgstr ""
"<b>Mi empresa</b><br/>Blvd. Executive Park 250, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>Estados Unidos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a %s."
msgstr "<b>Seleccionar</b> un %s."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a Color Palette."
msgstr "<b>Seleccionar</b> una paleta de colores."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the %s padding"
msgstr "<b>Deslice</b> este botón para cambiar el padding %s "

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the column size."
msgstr "<b>Deslice</b> este botón para cambiar el tamaño de la columna."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid ""
"<br/><br/>\n"
"                    Example of rule:<br/>"
msgstr ""
"<br/><br/>\n"
"                    Ejemplo de regla:<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"background-color: rgb(255, 255, 255);\">Good writing is "
"simple, but not simplistic.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 255, 255);\">La buena escritura es "
"simple, pero no simplista.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<font style=\"font-size: 14px;\">Created in 2021, the company is young and "
"dynamic. Discover the composition of the team and their skills.</font>"
msgstr ""
"<font style=\"font-size: 14px;\">Creada en 2021, la empresa es joven y "
"dinámica Descubra el equipo y sus habilidades.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">Edit "
"this title</font>"
msgstr ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">Edite"
" este título</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "<font style=\"font-size: 62px; font-weight: bold;\">Catchy Headline</font>"
msgstr ""
"<font style=\"font-size: 62px; font-weight: bold;\">Titular pegadizo</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "<font style=\"font-size: 62px;\">A punchy Headline</font>"
msgstr "<font style=\"font-size: 62px;\">Un titulo llamativo</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "<font style=\"font-size: 62px;\">Sell Online. Easily.</font>"
msgstr "<font style=\"font-size: 62px;\">Venda en línea. Fácilmente.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "<font style=\"font-size: 62px;\">Slide Title</font>"
msgstr "<font style=\"font-size: 62px;\">Título de diapositiva</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "<font style=\"font-size: 62px;\">Win $20</font>"
msgstr "<font style=\"font-size: 62px;\">Gane $20</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "<font style=\"font-size: 62px;\">Your Site Title</font>"
msgstr "<font style=\"font-size: 62px;\">Título de su sitio</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 days ago</small>"
msgstr "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>Hace 2 días</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"me-2\"/><span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"me-2\"/><span><EMAIL></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid ""
"<i class=\"fa fa-1x fa-fw fa-map-marker me-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-map-marker me-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • Estados Unidos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                        How to create my Plausible Shared Link"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                        Cómo crear mi enlace de compartición de Plausible"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                        How to get my Measurement ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                        Cómo obtener mi ID de seguimiento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-eye ms-2 text-muted\" title=\"Go to View\"/>"
msgstr "<i class=\"fa fa-eye ms-2 text-muted\" title=\"Ir a la vista\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-files-o ms-2 text-muted\" title=\"Show Arch Diff\"/>"
msgstr ""
"<i class=\"fa fa-files-o ms-2 text-muted\" title=\"Mostrar diferencia de "
"arquitectura\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr "<i class=\"fa fa-fw fa-circle\"/> Círculos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-fw fa-heart\"/>Corazones"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh me-1\"/> Replace Icon"
msgstr "<i class=\"fa fa-fw fa-refresh me-1\"/> Remplazar icono"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/>Cuadrados"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/>Estrellas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr "<i class=\"fa fa-fw fa-thumbs-up\"/> Pulgares"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Offline</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Desconectado</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Connected</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Conectado</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Sitio web\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"<i class=\"fa fa-info-circle\"/> Edit the content below this line to adapt "
"the default <strong>Page not found</strong> page."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Edite el contenido debajo de esta línea "
"para adaptar la <strong>Página no encontrada</strong> predeterminada."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">A password is required to access this page.</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Se requiere una contraseña para tener acceso a esta página.</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Wrong password</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Contraseña incorrecta</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/><span class=\"o_force_ltr\">3575 "
"Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/><span class=\"o_force_ltr\">3575 "
"Fake Buena Vista Avenue</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/><span class=\"o_force_ltr\">+1 "
"************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/><span class=\"o_force_ltr\">+1 "
"************</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_add_language
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>Add a language...</span>"
msgstr ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>Agregar un idioma...</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw me-2\"/>\n"
"                            <b>Events</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw me-2\"/>\n"
"                            <b>Eventos</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw me-2\"/>\n"
"                            <b>About us</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw me-2\"/>\n"
"                            <b>Sobre nosotros</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw me-2\"/>\n"
"                            <b>Partners</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw me-2\"/>\n"
"                            <b>Partners</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw me-2\"/>\n"
"                            <b>Services</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw me-2\"/>\n"
"                            <b>Servicios</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw me-2\"/>\n"
"                            <b>Help center</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw me-2\"/>\n"
"                            <b>Centro de ayuda</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw me-2\"/>\n"
"                            <b>Guides</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw me-2\"/>\n"
"                            <b>Guías</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw me-2\"/>\n"
"                            <b>Our blog</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw me-2\"/>\n"
"                            <b>Nuestro blog</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw me-2\"/>\n"
"                            <b>Customers</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw me-2\"/>\n"
"                            <b>Clientes</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw me-2\"/>\n"
"                            <b>Products</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw me-2\"/>\n"
"                            <b>Productos</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments me-2\"/> Contact us"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-comments me-2\"/> Contáctenos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-cube me-2\"/> Free returns"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-cube me-2\"/> Devoluciones "
"gratuitas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket me-2\"/> Pickup"
" in store"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket me-2\"/> "
"Recoger en tienda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck me-2\"/> Express delivery"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck me-2\"/> Entrega exprés"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr ""
"<i>Configuración instantánea, satisfacción total o le devolvemos su "
"dinero.</i>"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "<p>Attached files : </p>"
msgstr "<p>Archivos adjuntos:</p>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid ""
"<small class=\"s_social_media_title d-none\" "
"contenteditable=\"true\"><b>Follow us</b></small>"
msgstr ""
"<small class=\"s_social_media_title d-none\" "
"contenteditable=\"true\"><b>Síganos</b></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
msgid ""
"<small class=\"s_social_media_title text-muted d-none\" "
"contenteditable=\"true\"><b>Follow us</b></small>"
msgstr ""
"<small class=\"s_social_media_title text-muted d-none\" "
"contenteditable=\"true\"><b>Síganos</b></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<small class=\"text-muted\">\n"
"                                        <i class=\"fa fa-info\"/>: type some of the first chars after 'google' is enough, we'll guess the rest.\n"
"                                    </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                                        <i class=\"fa fa-info\"/>: escribir algunos de los primeros caracteres después de \"Google\" es suficiente, adivinaremos el resto.\n"
"                                    </small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Form field help "
"text</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Texto de ayuda del "
"campo del formulario</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share "
"your email with anyone else.</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Nunca compartiremos "
"su correo electrónico con nadie más.</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<small>/ month</small>"
msgstr "<small>/ mes</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "<small>TABS</small>"
msgstr "<small>PESTAÑAS</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small>We help you grow your business</small>"
msgstr "<small>Le ayudamos a hacer crecer su empresa</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2015</b></span>"
msgstr "<span class=\"bg-white\"><b>2015</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2018</b></span>"
msgstr "<span class=\"bg-white\"><b>2018</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2019</b></span>"
msgstr "<span class=\"bg-white\"><b>2019</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"visually-hidden\">Siguiente</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"visually-hidden\">Anterior</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">Discover our new products</font></b>\n"
"                        </span>"
msgstr ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">Descubra nuestros productos</font></b>\n"
"                        </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Your message has been sent <b>successfully</b></span>"
msgstr ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Se envió su mensaje con <b>éxito</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"fa fa-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"visually-hidden\">Anterior</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"fa fa-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"visually-hidden\">Siguiente</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "<span class=\"fa-2x\">×</span>"
msgstr "<span class=\"fa-2x\">×</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid "<span class=\"list-inline-item\">|</span>"
msgstr "<span class=\"list-inline-item\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr "<span class=\"mx-2\">/</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<span class=\"mx-2\">to</span>"
msgstr "<span class=\"mx-2\">a</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid ""
"<span class=\"o_add_language list-inline-item\" "
"groups=\"website.group_website_restricted_editor\">|</span>"
msgstr ""
"<span class=\"o_add_language list-inline-item\" "
"groups=\"website.group_website_restricted_editor\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_copyright_company_name
msgid ""
"<span class=\"o_footer_copyright_name me-2\">Copyright &amp;copy; Company "
"name</span>"
msgstr ""
"<span class=\"o_footer_copyright_name me-2\">Derechos de autor y nombre de "
"la empresa</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Email Marketing</span>"
msgstr "<span class=\"o_form_label\">Marketing por correo electrónico</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Livechat</span>"
msgstr "<span class=\"o_form_label\">Chat en vivo</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Robots.txt</span>"
msgstr "<span class=\"o_form_label\">Robots.txt</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Sitemap</span>"
msgstr "<span class=\"o_form_label\">Mapa de sitio</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
msgid ""
"<span class=\"pe-1\">We use cookies to provide you a better user experience "
"on this website.</span>"
msgstr ""
"<span class=\"pe-1\">Usamos cookies para proporcionarle una mejor "
"experiencia de usuario en este sitio web.</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_badge
msgid ""
"<span class=\"s_badge badge text-bg-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>Category\n"
"    </span>"
msgstr ""
"<span class=\"s_badge badge text-bg-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>Categoría\n"
"    </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO de "
"MiEmpresa</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO de "
"MiEmpresa</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO de "
"MiEmpresa</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">12</span>"
msgstr "<span class=\"s_number display-4\">12</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">37</span>"
msgstr "<span class=\"s_number display-4\">37</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">45</span>"
msgstr "<span class=\"s_number display-4\">45</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">8</span>"
msgstr "<span class=\"s_number display-4\">8</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "<span class=\"s_progress_bar_text\">80% Development</span>"
msgstr "<span class=\"s_progress_bar_text\">Desarrollo al 80%</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "<span class=\"s_website_form_label_content\">Company</span>"
msgstr "<span class=\"s_website_form_label_content\">Empresa</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "<span class=\"s_website_form_label_content\">Email To</span>"
msgstr "<span class=\"s_website_form_label_content\">Correo electrónico a</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Email</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Correo electrónico</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Name</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Nombre</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Número de teléfono</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Question</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Pregunta</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Asunto</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<span>Theme</span>"
msgstr "<span>Tema</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr ""
"Una CDN le ayuda a servir el contenido de su sitio web con alta "
"disponibilidad y alto rendimiento a cualquier visitante sea cual sea su "
"ubicación."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart
msgid "A Chart Title"
msgstr "Un título de gráfico"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"A Google Map error occurred. Make sure to read the key configuration popup "
"carefully."
msgstr ""
"Ocurrió un error con el mapa de Google. Asegúrese de leer cuidadosamente la "
"ventana emergente de configuración clave."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr "Un subtítulo de sección"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid ""
"A card is a flexible and extensible content container. It includes options "
"for headers and footers, a wide variety of content, contextual background "
"colors, and powerful display options."
msgstr ""
"Una tarjeta es un contenedor de contenido flexible y extensible. Incluye "
"opciones para encabezados y pies de página, una amplia variedad de "
"contenido, colores de fondo contextuales y potentes opciones de "
"visualización."

#. module: website
#: model:ir.model.fields,help:website.field_base_automation__website_published
#: model:ir.model.fields,help:website.field_ir_actions_server__website_published
#: model:ir.model.fields,help:website.field_ir_cron__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"Se puede ejecutar una acción de servidor de código desde el sitio web, "
"usando un controlador dedicado. La dirección es "
"<base>/website/action/<website_path>. Establezca este campo a verdadero para"
" permitir a los usuarios ejecutar esta acción. Si se establece a falso, la "
"acción no se podrá ejecutar a través del sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "A color block"
msgstr "Un bloque de color"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr "Un gran título"

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__field_names
msgid "A list of comma-separated field names"
msgstr "Una lista de nombres de campo separados por comas"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_stores_locator
msgid "A map and a listing of your stores"
msgstr "Un mapa y una lista de sus tiendas"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visit_count
msgid ""
"A new visit is considered if last connection was more than 8 hours ago."
msgstr ""
"Se considera una nueva visita si la última conexión fue hace más de 8 horas."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "A short description of this great feature."
msgstr "Una pequeña descripción de esta gran característica."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr ""
"Una pequeña explicación de esta gran <br/>característica, en palabras "
"claras. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"A timeline is a graphical representation on which important events are "
"marked."
msgstr ""
"Una línea de tiempo es una representación gráfica en la que se marcan "
"eventos importantes."

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__is_connected
msgid ""
"A visitor is considered as connected if his last page view was within the "
"last 5 minutes."
msgstr ""
"Un visitante se considera conectado si su última visita a la página fue en "
"los últimos 5 minutos."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "API Key"
msgstr "Clave API"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_about_us
msgid "About Us"
msgstr "Sobre nosotros"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "About us"
msgstr "Sobre nosotros"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#, python-format
msgid "Access Denied"
msgstr "Acceso denegado"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__access_token
msgid "Access Token"
msgstr "Token de acceso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid "Access to this page"
msgstr "Acceso a esta página"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_access_token_unique
msgid "Access token should be unique."
msgstr "El token de acceso debe ser único."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Accessories"
msgstr "Accesorios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Accordion"
msgstr "Acordeón"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr "Gestión de cuentas y ventas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Accounts are usable across all your multiple websites"
msgstr "Puede utilizar las cuentas en todos sus sitios web"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__redirect_type
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Action"
msgstr "Acción"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__active
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__active
#: model:ir.model.fields,field_description:website.field_website_page__active
#: model:ir.model.fields,field_description:website.field_website_rewrite__active
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Active"
msgstr "Activo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Adapte estas tres columnas para que se ajusten a sus necesidades de diseño. "
"Para duplicar, eliminar o mover columnas, seleccione la columna y use los "
"iconos superiores para realizar su acción."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#, python-format
msgid "Add"
msgstr "Agregar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Elements"
msgstr "Agregar elementos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Item"
msgstr "Agregar elemento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Add Media"
msgstr "Agregar archivo multimedia"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Add Mega Menu Item"
msgstr "Agregar elemento de mega menú"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Add Menu Item"
msgstr "Agregar elemento de menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_social_media_options
msgid "Add New Social Network"
msgstr "Agregar nueva red social"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
msgid "Add Product"
msgstr "Agregar producto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Row"
msgstr "Agregar fila"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Serie"
msgstr "Agregar serie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Slide"
msgstr "Agregar diapositiva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Add Tab"
msgstr "Agregar pestaña"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Add Year"
msgstr "Agregar año"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Add a Google Font"
msgstr "Agregar una fuente de Google"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Add a caption to enhance the meaning of this image."
msgstr "Agregue una descripción para mejorar el significado de esta imagen."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_product_catalog/options.js:0
#, python-format
msgid "Add a description here"
msgstr "Agregue una descripción aquí"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Add a great slogan."
msgstr "Agregue un buen eslogan."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Add a menu description."
msgstr "Agregar una descripción del menú"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.js:0
#, python-format
msgid "Add a menu item"
msgstr "Agregar elemento de menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field after this one"
msgstr "Agregar un nuevo campo después de este"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field at the end"
msgstr "Agregar un nuevo campo al final"

#. module: website
#. odoo-python
#: code:addons/website/models/res_lang.py:0
#, python-format
msgid "Add languages"
msgstr "Agregar idiomas"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Add new %s"
msgstr "Agregar nuevo %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Add to Cart Button"
msgstr "Botón de Agregar al carrito"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Add to cart"
msgstr "Agregar al carrito"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#, python-format
msgid "Add to menu"
msgstr "Agregar al menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Address"
msgstr "Dirección"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Advertising &amp; Marketing<br/>(optional)"
msgstr "Anuncios y marketing<br/>(opcional)"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__after
msgid "After"
msgstr "Después"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Alert"
msgstr "Alerta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Bottom"
msgstr "Alinear inferior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Middle"
msgstr "Alinear medio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Top"
msgstr "Alinear superior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Alignment"
msgstr "Alineación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline es una de las personas icónicas en la vida que pueden decir que aman "
"lo que hacen. Ella es mentora de más de 100 desarrolladores internos y "
"supervisa a una comunidad de miles de desarrolladores."

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__
msgid "All"
msgstr "Todos"

#. module: website
#: model:ir.model,name:website.model_website_route
msgid "All Website Route"
msgstr "Toda la ruta del sitio web"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_views_mixin.js:0
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
#, python-format
msgid "All Websites"
msgstr "Todos los sitios web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "All informations you need"
msgstr "Toda la información que necesita"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "All pages"
msgstr "Todas las páginas"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "All results"
msgstr "Todos los resultados"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr "Todos estos iconos son completamente gratuitos para uso comercial."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Allow all cookies"
msgstr "Permitir todas las cookies"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Allow the use of cookies from this website on this browser?"
msgstr "¿Permitir el uso de cookies de este sitio web en el navegador?"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__track
#: model:ir.model.fields,help:website.field_website_page__track
msgid "Allow to specify for one page of the website to be trackable or not"
msgstr ""
"Permita especificar si una página del sitio web pueda tener seguimiento o no"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr "Se permite su uso en formularios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Allows to do mass mailing campaigns to contacts"
msgstr "Permite enviar campañas de correo masivo a sus contactos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Alows your visitors to chat with you"
msgstr "Permite a sus visitantes chatear con usted"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Already installed"
msgstr "Ya instalado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Image Text"
msgstr "Alternar Imagen - Texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text"
msgstr "Alternar texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image"
msgstr "Alternar Texto - Imagen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr "Alternar Texto - Imagen - Texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"Aunque este sitio web puede estar vinculado a otros, esto no implica, ya sea"
" directa o indirectamente, la aprobación, asociación, patrocinio, respaldo o"
" afiliación por nuestra parte a dichos sitios salvo que se indique "
"específicamente aquí."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always Underlined"
msgstr "Siempre subrayado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Always Visible"
msgstr "Siempre visible"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Amazing pages"
msgstr "Páginas sorprendentes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map
msgid "An address must be specified for a map to be embedded"
msgstr "Se debe especificar una dirección para poder insertar un mapa"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr "Ocurrió un error, no se envió el formulario."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "An error occurred while rendering the template"
msgstr "Ocurrió un error al visualizar la plantilla"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_website_analytics
#, python-format
msgid "Analytics"
msgstr "Analítica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics cookies and privacy information."
msgstr "Cookies analíticas e información de privacidad."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics<br/>(optional)"
msgstr "Analytics<br/>(opcional)"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Anchor copied to clipboard<br>Link: %s"
msgstr "Enlace ancla pegado al portapapeles<br>Enlace: %s"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Anchor name"
msgstr "Nombre del ancla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr "Y un gran subtítulo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Animate text"
msgstr "Animar texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Animated"
msgstr "Animado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation"
msgstr "Animación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "Another color block"
msgstr "Otro bloque de color"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Another feature"
msgstr "Otra característica"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__append
msgid "Append"
msgstr "Anexar"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_add_features
#: model:ir.ui.menu,name:website.menu_website_add_features
msgid "Apps"
msgstr "Aplicaciones"

#. module: website
#. odoo-python
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Apps url"
msgstr "URL de las aplicaciones"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch
msgid "Arch"
msgstr "Arch"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_db
msgid "Arch Blob"
msgstr "Blob de la arquitectura"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_fs
msgid "Arch Filename"
msgstr "Nombre del archivo de la arquitectura"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch_fs
msgid "Arch Fs"
msgstr "Arquitectura del sistema de archivos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Archived"
msgstr "Archivado"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Are you sure you want to delete this page ?"
msgstr "¿Está seguro de que desea eliminar esta página?"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Are you sure you want to delete those pages ?"
msgstr "¿Está seguro de que desea eliminar esas páginas?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Arrows"
msgstr "Flechas"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
#, python-format
msgid "As promised, we will offer 4 free tickets to our next summit."
msgstr ""
"Como prometimos, ofreceremos 4 boletos gratis para nuestra siguiente "
"conferencia."

#. module: website
#: model:ir.model,name:website.model_ir_asset
msgid "Asset"
msgstr "Activo"

#. module: website
#: model:ir.model,name:website.model_web_editor_assets
msgid "Assets Utils"
msgstr "Utilidades de activos"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__copy_ids
msgid "Assets using a copy of me"
msgstr "Activos que usan una copia de mi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "At The End"
msgstr "Al final"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "Attachment"
msgstr "Archivo adjunto"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__copy_ids
msgid "Attachment using a copy of me"
msgstr "Archivo adjunto que utiliza una copia de mi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Authenticate users, protect user data and allow the website to deliver the services users expects,\n"
"                                                such as maintaining the content of their cart, or allowing file uploads."
msgstr ""
"Autentique usuarios, proteja los datos de los usuarios y permita que el sitio web proporcione los servicios que los usuarios esperan,\n"
"                                                como almacenar el contenido de sus carritos o permitir subir archivos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Author"
msgstr "Autor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Authorized Groups"
msgstr "Grupos autorizados"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Auto"
msgstr "Auto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Automatically opens the pop-up if the user stays on a page longer than the "
"specified time."
msgstr ""
"Abre la ventana emergente en automático si el usuario permanece en la página"
" por más tiempo del esperado."

#. module: website
#: model:ir.model.fields,field_description:website.field_website__auto_redirect_lang
msgid "Autoredirect Language"
msgstr "Redireccionamiento automático de idioma"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Autosizing"
msgstr "Dimensionamiento automático"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_automation__website_published
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_published
#: model:ir.model.fields,field_description:website.field_ir_cron__website_published
msgid "Available on the Website"
msgstr "Disponible en el sitio web"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"
msgstr "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "BTS Base Colors"
msgstr "Colores base BTS"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Backdrop"
msgstr "Fondo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Background"
msgstr "Fondo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#, python-format
msgid "Background Shape"
msgstr "Forma del fondo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Badge"
msgstr "Insignia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Bags"
msgstr "Bolsas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Banner"
msgstr "Banner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Horizontal"
msgstr "Barra horizontal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Vertical"
msgstr "Barra vertical"

#. module: website
#: model:ir.model,name:website.model_base
msgid "Base"
msgstr "Base"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_base
msgid "Base View Architecture"
msgstr "Arquitectura de la vista base"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__primary
msgid "Base view"
msgstr "Vista base"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr "Ventas básicas y marketing para hasta 2 usuarios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Beautiful snippets"
msgstr "Hermosos snippets"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Beef Carpaccio"
msgstr "Carpaccio de res"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__before
msgid "Before"
msgstr "Antes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "Principiante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Below Each Other"
msgstr "Debajo uno del otro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big"
msgstr "Grande"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Big Boxes"
msgstr "Cajas grandes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big Icons Subtitles"
msgstr "Subtítulos de iconos grandes"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr "Poner este campo en la lista negra de los formularios web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr "En lista negra de formularios web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Blazers"
msgstr "Blazers"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Block"
msgstr "Bloquear"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Blockquote"
msgstr "Bloque de cita"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Blog"
msgstr "Blog"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Blog Post"
msgstr "Publicación de blog"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_news
msgid "Blogging and posting relevant content"
msgstr "Publicando contenido relevante en blogs"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Blur"
msgstr "Desenfocar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Books"
msgstr "Libros"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Bootstrap-based templates"
msgstr "Plantillas basadas en Bootstrap"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
#, python-format
msgid "Border"
msgstr "Borde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Bottom"
msgstr "Borde inferior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Border Color"
msgstr "Color del borde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Radius"
msgstr "Radio del borde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Width"
msgstr "Ancho del borde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bordered"
msgstr "Bordeado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Bottom"
msgstr "Inferior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Bottom to Top"
msgstr "De abajo hacia arriba"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce"
msgstr "Rebote"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Boxed"
msgstr "Con caja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Boxes"
msgstr "Cajas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Breadcrumb"
msgstr "Migas de pan"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Build my website"
msgstr "Crear mi sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Building blocks system"
msgstr "Sistema de bloques de creación"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Building your %s"
msgstr "Creando su %s"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
#, python-format
msgid "Building your website..."
msgstr "Creando su sitio web..."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__bundle
msgid "Bundle"
msgstr "Paquete"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Button"
msgstr "Botón"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Button Position"
msgstr "Posición del botón"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Buttons"
msgstr "Botones"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_url
#: model:ir.model.fields,field_description:website.field_website__cdn_url
msgid "CDN Base URL"
msgstr "URL Base CDN"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,field_description:website.field_website__cdn_filters
msgid "CDN Filters"
msgstr "Filtros CDN"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "CTA"
msgstr "CTA"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Call to Action"
msgstr "Llamada a la acción"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Call us"
msgstr "Llámenos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call-to-action"
msgstr "Llamada a la acción"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Camera"
msgstr "Cámara"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__can_publish
#: model:ir.model.fields,field_description:website.field_res_users__can_publish
#: model:ir.model.fields,field_description:website.field_website_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__can_publish
msgid "Can Publish"
msgstr "Puede publicar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/utils.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: website
#. odoo-python
#: code:addons/website/models/res_lang.py:0
#, python-format
msgid "Cannot deactivate a language that is currently used on a website."
msgstr ""
"No se puede desactivar un idioma que actualmente se usa en un sitio web."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Cannot load google map."
msgstr "Google Maps no puede cargar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Card"
msgstr "Tarjeta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Card Body"
msgstr "Cuerpo de tarjeta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Card Footer"
msgstr "Pie de tarjeta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Card Header"
msgstr "Encabezado de tarjeta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Card Style"
msgstr "Estilo de la tarjeta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cards"
msgstr "Tarjetas"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_career
msgid "Career"
msgstr "Carrera"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Carousel"
msgstr "Carrusel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Case Studies"
msgstr "Casos de estudio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Category"
msgstr "Categoría"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Category of Cookie"
msgstr "Categoría de cookie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Center"
msgstr "Centro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered"
msgstr "Centrado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered Logo"
msgstr "Logo centrado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Change Icons"
msgstr "Cambiar iconos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Change theme in a few clicks, and browse through Odoo's catalog of\n"
"                            ready-to-use themes available in our app store."
msgstr ""
"Cambie el tema con solo unos clics, y navegue a través del catálogo de temas\n"
"                            de Odoo listos para usar disponibles en nuestra tienda de aplicaciones."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing the color palette will reset all your color customizations, are you"
" sure you want to proceed?"
msgstr ""
"Cambiar la paleta de colores restablecerá todas sus personalizaciones de "
"color, ¿está seguro de que desea continuar?"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing theme requires to leave the editor. This will save all your "
"changes, are you sure you want to proceed? Be careful that changing the "
"theme will reset all your color customizations."
msgstr ""
"Para cambiar el tema necesita salir del editor. Esto guardará todos sus "
"cambios, ¿está seguro de que desea continuar? Tome en cuenta que cambiar el "
"tema restablecerá todas sus personalizaciones de color."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Chart"
msgstr "Gráfico"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_live_chat
msgid "Chat with visitors to improve traction"
msgstr "Chatee con los visitantes para mejorar la tracción "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "Check out now and get $20 off your first order."
msgstr "Pague ahora y reciba $20 de descuento en su primera orden."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Check your configuration."
msgstr "Verifique su configuración."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Check your connection and try again"
msgstr "Compruebe su conexión y vuelva a intentarlo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Checkbox"
msgstr "Casilla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Cheese Onion Rings"
msgstr "Aros de cebolla con queso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Chefs Fresh Soup of the Day"
msgstr "Sopa del día del chef"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__field_parent
msgid "Child Field"
msgstr "Campo hijo"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__child_id
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Child Menus"
msgstr "Menús hijos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Children"
msgstr "Niños"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Choose"
msgstr "Elegir"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph about it.<br/> It "
"does not have to be long, but it should reinforce your image."
msgstr ""
"Elija una imagen vibrante y escriba un párrafo inspirador al respecto. "
"<br/>No tiene que ser largo, pero debe reforzar su imagen."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Choose an anchor name"
msgstr "Elija un nombre de ancla"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#, python-format
msgid "Choose another theme"
msgstr "Elegir otro tema"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Choose your favorite"
msgstr "Elija su favorito"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Circle"
msgstr "Círculo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Classic"
msgstr "Clásico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Clean"
msgstr "Claro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Clever Slogan"
msgstr "Eslogan inteligente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Click and change content directly from the front-end: no complex back\n"
"                            end to deal with."
msgstr ""
"Haga clic y cambie el contenido directamente desde el frontend: no tiene que\n"
"                            lidiar con el complejo backend."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Click here to go back to block tab."
msgstr "Haga clic aquí para regresar a la pestaña de bloques."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#, python-format
msgid "Click here to setup your social networks"
msgstr "Haga clic aquí para configurar sus redes sociales"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#, python-format
msgid "Click on"
msgstr "Haga clic en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code
msgid ""
"Click on <b>\"Edit\"</b> in the right panel to replace this with your own "
"HTML code"
msgstr ""
"Haga clic en <b>\"Editar\"</b> en el panel a la derecha para remplazar esto "
"con su propio código HTML"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Click on the icon to adapt it <br/>to your purpose."
msgstr "Haga clic en el icono para adaptarlo <br/>a su propósito."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Click to choose more images"
msgstr "Haga clic para elegir más imágenes"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Click to select"
msgstr "Haga clic para seleccionar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.s_popup
#: model_terms:ir.ui.view,arch_db:website.show_website_info
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Close Button Color"
msgstr "Color del botón de cerrar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Clothes"
msgstr "Ropa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
msgid "Code"
msgstr "Código"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Code Injection"
msgstr "Inyección de código"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Collapse Icon"
msgstr "Contraer icono"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Color"
msgstr "Color"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Color Presets"
msgstr "Valores predeterminados de color"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""
"Los bloques de color son una forma simple y efectiva de <b>presentar y "
"resaltar su contenido</b>. Elija una imagen o un color para el fondo. Puede "
"cambiar el tamaño y duplicar los bloques para crear su propio diseño. "
"Agregue imágenes o iconos para personalizar los bloques."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Color filter"
msgstr "Filtro de color"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Colors"
msgstr "Colores"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cols"
msgstr "Cols"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Columns"
msgstr "Columnas"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__website_config_preselection
msgid ""
"Comma-separated list of website type/purpose for which this feature should "
"be pre-selected"
msgstr ""
"Lista separada por comas de tipos/propósitos de sitio web para los que se "
"preselecciona esta función"

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website__company_id
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Company"
msgstr "Empresa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Comparisons"
msgstr "Comparaciones"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr "CRM completo para equipos de cualquier tamaño"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Components"
msgstr "Componentes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers"
msgstr "Computadoras"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers &amp; Devices"
msgstr "Computadora y dispositivos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Conditionally"
msgstr "De forma condicional"

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__configurator_done
msgid "Configurator Done"
msgstr "Configurador Hecho"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "Confirmación"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Connect Plausible"
msgstr "Conectar con Plausible"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Connect with us"
msgstr "Contáctenos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Connected"
msgstr "Conectado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Connector"
msgstr "Conector"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_search_console
msgid "Console Google Search"
msgstr "Consola de búsqueda de Google"

#. module: website
#: model:ir.model,name:website.model_res_partner
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_id
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Contact"
msgstr "Contacto"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.header_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_banner
#, python-format
msgid "Contact Us"
msgstr "Contáctenos"

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Contact Visitor"
msgstr "Contactar visitante"

#. module: website
#: model:website.menu,name:website.menu_contactus
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "Contact us"
msgstr "Contáctenos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                    We'll do our best to get back to you as soon as possible."
msgstr ""
"Contáctenos sobre cualquier cosa relacionada con nuestra empresa o servicios.<br/>\n"
"                                    Haremos todo lo posible por darle respuestas a la brevedad."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Contact us anytime"
msgstr "Contáctenos en cualquier momento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Contact us for any issue or question"
msgstr "Contáctenos si tiene algún problema o pregunta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Contacts"
msgstr "Contactos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Contain"
msgstr "Contener"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Contains"
msgstr "Contiene"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_robots__content
#: model:ir.ui.menu,name:website.menu_content
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Content"
msgstr "Contenido"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_activated
#: model:ir.model.fields,field_description:website.field_website__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr "Red de distribución de contenidos (CDN)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Content Width"
msgstr "Ancho del contenido"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/editor/editor.js:0
#, python-format
msgid "Content saved."
msgstr "Se guardó el contenido."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
#, python-format
msgid "Content to translate"
msgstr "Contenido por traducir"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"
msgstr "Continuar leyendo <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "Cookie Policy"
msgstr "Política de cookies"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,field_description:website.field_website__cookies_bar
msgid "Cookies Bar"
msgstr "Barra de cookies"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Cookies are small bits of text sent by our servers to your computer or device when you access our services.\n"
"                            They are stored in your browser and later sent back to our servers so that we can provide contextual content.\n"
"                            Without cookies, using the web would be a much more frustrating experience.\n"
"                            We use them to support your activities on our website. For example, your session (so you don't have to login again) or your shopping cart.\n"
"                            <br/>\n"
"                            Cookies are also used to help us understand your preferences based on previous or current activity on our website (the pages you have\n"
"                            visited), your language and country, which enables us to provide you with improved services.\n"
"                            We also use cookies to help us compile aggregate data about site traffic and site interaction so that we can offer\n"
"                            better site experiences and tools in the future."
msgstr ""
"Las cookies con pequeñas partes de texto que nuestros servidores envían a su computadora o dispositivo cuando accede a nuestros servicios.\n"
"                            Se guardan en su navegador y más tarde se envían de vuelta a nuestros servidores para poder proporcionar contenido contextual.\n"
"                            Sin las cookies, utilizar la web sería una experiencia mucho más frustrante.\n"
"                            Las utilizamos para apoyar sus actividades en nuestro sitio web. Por ejemplo, su sesión (para que no tenga que volver a iniciar sesión) o su carrito.\n"
"                            <br/>\n"
"                            También usamos las cookies para ayudarnos a entender sus preferencias según la actividad previa o actual de nuestro sitio web (las páginas que ha\n"
"                            visitado), su idioma y país, lo que nos permite proporcionarle un mejor servicio.\n"
"                            También utilizamos las cookies para ayudarnos a recopilar datos agregados sobre el tráfico e interacciones del sitio para ofrecer\n"
"                            mejores experiencias y herramientas en el futuro."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Copyright"
msgstr "Derechos de autor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Countdown"
msgstr "Contador"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Countdown ends in"
msgstr "El contador acaba en"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
#, python-format
msgid "Countdown is over - Firework"
msgstr "El contador ha acabado - Fuegos artificiales"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Country"
msgstr "País"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_flag
msgid "Country Flag"
msgstr "Bandera del país"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Course"
msgstr "Curso"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.record_cover
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Cover"
msgstr "Portada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Cover Photo"
msgstr "Foto de portada"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cover_properties_mixin__cover_properties
msgid "Cover Properties"
msgstr "Propiedades de la portada"

#. module: website
#: model:ir.model,name:website.model_website_cover_properties_mixin
msgid "Cover Properties Website Mixin"
msgstr "Mixin de propiedades de portada de sitio web"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#: code:addons/website/static/src/js/utils.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Create"
msgstr "Crear"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "Crear página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Create a"
msgstr "Cree un"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Create a Google Project and Get a Key"
msgstr "Cree un proyecto de Google y obtenga una clave"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Create a link to target this section"
msgstr "Crear enlace para redirigir a esta sección"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Create new"
msgstr "Crear nuevo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Create your page from scratch by dragging and dropping pre-made,\n"
"                            fully customizable building blocks."
msgstr ""
"Cree su página desde cero al arrastrar y soltar bloques de creación\n"
"                            precreados y totalmente personalizables."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website__create_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_uid
#: model:ir.model.fields,field_description:website.field_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_uid
#: model:ir.model.fields,field_description:website.field_website_robots__create_uid
#: model:ir.model.fields,field_description:website.field_website_route__create_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Created in 2021, the company is young and dynamic. Discover the composition "
"of the team and their skills."
msgstr ""
"Creada en 2021, la empresa es joven y dinámica. Descubra el equipo y sus "
"habilidades."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website__create_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_date
#: model:ir.model.fields,field_description:website.field_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_date
#: model:ir.model.fields,field_description:website.field_website_robots__create_date
#: model:ir.model.fields,field_description:website.field_website_route__create_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_date
msgid "Created on"
msgstr "Creado el"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Curved arrow"
msgstr "Flecha curva"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#, python-format
msgid "Custom"
msgstr "Personalizado"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_head
msgid "Custom <head> code"
msgstr "Código <head> personalizado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Custom Code"
msgstr "Código personalizado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Custom Key"
msgstr "Clave personalizada"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Custom Text"
msgstr "Texto personalizado"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Custom Url"
msgstr "URL personalizado"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_footer
msgid "Custom end of <body> code"
msgstr "Fin personalizado del <body> código"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom end of body code"
msgstr "Fin personalizado del cuerpo del código"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Custom field"
msgstr "Campo personalizado"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom head code"
msgstr "Inicio personalizado del código"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__auth_signup_uninvited
#: model:ir.model.fields,field_description:website.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "Cuenta de cliente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Customers"
msgstr "Clientes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Customization tool"
msgstr "Herramienta de personalización"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__customize_show
msgid "Customize Show"
msgstr "Personalizar mostrar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M"
msgstr "D - H - M"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M - S"
msgstr "D - H - M - S"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr "ARRASTRE BLOQUES DE CREACIÓN AQUÍ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
msgid "Danger"
msgstr "Peligro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dashed"
msgstr "Rayado"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/services/website_service.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#, python-format
msgid "Data"
msgstr "Datos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Border"
msgstr "Borde de los datos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Color"
msgstr "Color de los datos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Border"
msgstr "Borde del conjunto de datos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Color"
msgstr "Color del conjunto de datos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Date"
msgstr "Fecha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Date &amp; Time"
msgstr "Fecha y hora"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Days"
msgstr "Días"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Decimal Number"
msgstr "Número decimal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Default"
msgstr "Predeterminado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "Derecho de acceso predeterminados"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__default_lang_id
msgid "Default Language"
msgstr "Idioma predeterminado"

#. module: website
#: model:website.menu,name:website.main_menu
msgid "Default Main Menu"
msgstr "Menú principal predeterminado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Default Reversed"
msgstr "Predeterminado inverso"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,field_description:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Social Share Image"
msgstr "Imagen predeterminada al compartir en redes sociales"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Default Value"
msgstr "Valor predeterminado"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_id
msgid "Default language"
msgstr "Idioma predeterminado"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_code
msgid "Default language code"
msgstr "Código de idioma predeterminado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Delay"
msgstr "Retraso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Delete Blocks"
msgstr "Eliminar bloques"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Delete Menu Item"
msgstr "Eliminar elemento de menú"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Delete Page"
msgstr "Eliminar página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Elimine la imagen anterior o reemplácela por otra que ilustre su mensaje. "
"Haga clic en la imagen para cambiar su <em> esquina redondeada</em>."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Delete this font"
msgstr "Eliminar esta fuente"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Deleting a font requires a reload of the page. This will save all your "
"changes and reload the page, are you sure you want to proceed?"
msgstr ""
"Es necesario actualizar la página para eliminar una fuente. Esto guardará "
"todos sus cambios y actualizará la página, ¿está seguro de que desea "
"continuar?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Deliveries"
msgstr "Entregas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Departments"
msgstr "Departamentos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Dependencies"
msgstr "Dependencias"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Describe your field here."
msgstr "Describa aquí su campo."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__description
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#, python-format
msgid "Description"
msgstr "Descripción"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_our_services
msgid "Description of your services offer"
msgstr "Descripción de su oferta de servicios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "Descriptions"
msgstr "Descripciones"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Descriptive"
msgstr "Descriptivo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Design"
msgstr "Diseño"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Design features"
msgstr "Características de diseño"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_pricing
msgid "Designed to drive conversion"
msgstr "Diseñado para impulsar la conversación"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#, python-format
msgid "Desktop"
msgstr "Escritorio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Desktop computers"
msgstr "Computadoras de escritorio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Detail"
msgstr "Detalle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Details"
msgstr "Detalles"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Detect"
msgstr "Detectar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Direction"
msgstr "Dirección"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__directive
msgid "Directive"
msgstr "Directiva"

#. module: website
#: model:ir.actions.server,name:website.website_disable_unused_snippets_assets_ir_actions_server
#: model:ir.cron,cron_name:website.website_disable_unused_snippets_assets
msgid "Disable unused snippets assets"
msgstr "Deshabilitar snippets sin utilizar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Disabled"
msgstr "Deshabilitado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Disappearing"
msgstr "Desaparición"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disappears"
msgstr "Desaparece"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Discover"
msgstr "Descubrir"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Discover all the features"
msgstr "Descubra todas las características"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Discover more"
msgstr "Descubrir más"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Discover our culture and our values"
msgstr "Descubra nuestra cultura y nuestros valores"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our legal notice"
msgstr "Descubra nuestro aviso legal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our realisations"
msgstr "Descubra nuestros logros"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "Discover our team"
msgstr "Descubra nuestro equipo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Discrete"
msgstr "Discreto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr "Grupo de conversación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disk"
msgstr "Disco"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Display"
msgstr "Mostrar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Display Inline"
msgstr "Mostrar en línea"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website__display_name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__display_name
#: model:ir.model.fields,field_description:website.field_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website_rewrite__display_name
#: model:ir.model.fields,field_description:website.field_website_robots__display_name
#: model:ir.model.fields,field_description:website.field_website_route__display_name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__display_name
#: model:ir.model.fields,field_description:website.field_website_track__display_name
#: model:ir.model.fields,field_description:website.field_website_visitor__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display a customizable cookies bar on your website"
msgstr "Mostrar una barra de cookies personalizable en su sitio web"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,help:website.field_website__cookies_bar
msgid "Display a customizable cookies bar on your website."
msgstr "Mostrar una barra de cookies personalizable en su sitio web."

#. module: website
#. odoo-python
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the badges"
msgstr "Mostrar las insignias"

#. module: website
#. odoo-python
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the biography"
msgstr "Mostrar la biografía "

#. module: website
#. odoo-python
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the website description"
msgstr "Mostrar la descripción del sitio web"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_logo
#: model:ir.model.fields,help:website.field_website__logo
msgid "Display this logo on the website."
msgstr "Mostrar este logo en el sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this website when users visit this domain"
msgstr "Mostrar el sitio web cuando los usuarios visiten este dominio"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Do not copy/paste code you do not understand, this could put your data at "
"risk."
msgstr ""
"No copie ni pegue código que no comprenda, podría poner en riesgo sus datos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Do you need specific information? Our specialists will help you with "
"pleasure."
msgstr ""
"¿Necesita información específica? Nuestros especialistas con gusto le "
"ayudarán."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Do you want to edit the company data ?"
msgstr "¿Desea editar la información de la empresa?"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Do you want to install the \"%s\" App?"
msgstr "¿Desea instalar la aplicación \"%s\"?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Documentation"
msgstr "Documentación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Doesn't contain"
msgstr "No contiene"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Domain"
msgstr "Dominio"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Don't forget to update all links referring to it."
msgstr "No olvide actualizar todos los enlaces que hacen referencia a esto."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#, python-format
msgid "Don't worry, you can switch later."
msgstr "No se preocupe, puede cambiarlo más tarde."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation"
msgstr "Donación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation Button"
msgstr "Botón de donación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Dots"
msgstr "Puntos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dotted"
msgstr "Punteado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Double"
msgstr "Doble"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr "Haga doble clic en un icono para reemplazarlo por uno de su elección."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Doughnut"
msgstr "Dona"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"Drag the <b>%s</b> building block and drop it at the bottom of the page."
msgstr ""
"Arrastre el bloque de creación <b>%s</b> y suéltelo al final de su página."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Drag to the right to get a submenu"
msgstr "Arrastre hacia la derecha para obtener un submenú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Dresses"
msgstr "Vestidos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dropdown"
msgstr "Desplegable"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "Menú desplegable"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Due Date"
msgstr "Fecha de vencimiento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate"
msgstr "Duplicar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Duplicate Page"
msgstr "Duplicar página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Duplicate blocks <br/>to add more steps."
msgstr "Duplique bloques <br/>para agregar más pasos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr "Duplique bloques y columnas para agregar más características."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Duration"
msgstr "Duración"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Carousel"
msgstr "Carrusel dinámico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Content"
msgstr "Contenido dinámico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Snippet"
msgstr "Snippet dinámico"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_homepage_url
#: model:ir.model.fields,help:website.field_website__homepage_url
msgid "E.g. /contactus or /shop"
msgstr "Por ejemplo, /contáctenos o /tienda"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_domain
#: model:ir.model.fields,help:website.field_website__domain
msgid "E.g. https://www.mydomain.com"
msgstr "Por ejemplo, https://www.midominio.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Easily design your own Odoo templates thanks to clean HTML\n"
"                            structure and bootstrap CSS."
msgstr ""
"Diseñe con facilidad sus propias plantillas de Odoo gracias a una estructura HTML\n"
"                            clara y bootstrap CSS."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Easily track your visitor with Plausible"
msgstr "Use Plausible para rastrear a sus visitantes con facilidad"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/systray_items/edit_website.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/services/website_custom_menus.js:0
#, python-format
msgid "Edit %s"
msgstr "Editar %s"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.js:0
#: code:addons/website/static/src/js/widgets/link_popover_widget.js:0
#: model:ir.ui.menu,name:website.custom_menu_edit_menu
#, python-format
msgid "Edit Menu"
msgstr "Editar menú"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Edit Menu Item"
msgstr "Editar elemento de menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Edit Message"
msgstr "Editar mensaje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Edit Styles"
msgstr "Editar estilos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit embedded code"
msgstr "Editar código insertado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Edit in backend"
msgstr "Editar en el backend"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Edit robots.txt"
msgstr "Editar robots.txt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Edit this content"
msgstr "Editar este contenido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Edit video"
msgstr "Editar video"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Editor"
msgstr "Editor"

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr "Editor y diseñador"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Effect"
msgstr "Efecto"

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Either action_server_id or filter_id must be provided."
msgstr "Se debe proporcionar ya sea action_server_id o filter_id."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Electronics"
msgstr "Electrónicos"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__email
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Email"
msgstr "Correo electrónico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Email &amp; Marketing"
msgstr "Marketing por correo electrónico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Email address"
msgstr "Dirección de correo electrónico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr "Soporte técnico por correo electrónico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Embed Code"
msgstr "Insertar código"

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Empty field name in %r"
msgstr "Nombre de campo vacío en %r"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable billing on your Google Project"
msgstr "Habilite la facturación en su Proyecto de Google"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr "Habilite la función de creación de formularios para este modelo."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable the right google map APIs in your google account"
msgstr "Habilite las API adecuadas de Google Maps en su cuenta de Google"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Enter an API Key"
msgstr "Introduzca una clave API"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added before the </body> of every page of your site."
msgstr ""
"Introduzca el código que se agregará antes del </body> de cada página de su "
"sitio."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Enter code that will be added into every page of your site"
msgstr "Introduzca el código que se agregará a cada página de su sitio"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added into the <head> of every page of your site."
msgstr ""
"Introduzca el código que se agregará al <head> de cada página de su sitio."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Enter email"
msgstr "Introduzca el correo electrónico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Equal Widths"
msgstr "Anchos iguales"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Error"
msgstr "Error"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Essential oils"
msgstr "Aceites esenciales "

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Event"
msgstr "Evento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Event heading"
msgstr "Título del evento"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_event
#: model_terms:ir.ui.view,arch_db:website.external_snippets
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Events"
msgstr "Eventos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Every Time"
msgstr "Siempre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Everything"
msgstr "Todo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Examples"
msgstr "Ejemplos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Existing fields"
msgstr "Campos existentes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr "Experto"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_privacy_policy
msgid "Explain how you protect privacy"
msgstr "Explique cómo protege la privacidad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert
msgid ""
"Explain the benefits you offer. <br/>Don't write about products or services "
"here, write about solutions."
msgstr ""
"Explique los beneficios que ofrece. <br/>No escriba acerca de los productos "
"o servicios aquí, escriba sobre las soluciones. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Explore"
msgstr "Explorar"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__extension
msgid "Extension View"
msgstr "Vista de extensión"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_automation__xml_id
#: model:ir.model.fields,field_description:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,field_description:website.field_ir_cron__xml_id
#: model:ir.model.fields,field_description:website.field_website_page__xml_id
msgid "External ID"
msgstr "ID externo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Extra Large"
msgstr "Extra grande"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Extra link"
msgstr "Enlace adicional"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr "Extra grande"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Small"
msgstr "Extra pequeño"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "F.A.Q."
msgstr "Preguntas frecuentes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Facebook"
msgstr "Facebook"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_facebook
msgid "Facebook Account"
msgstr "Cuenta de Facebook"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade"
msgstr "Desvanecer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade Out"
msgstr "Desvanecer"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Failed to install \"%s\""
msgstr "Hubo un error en la instalación de \"%s\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Farm Friendly Chicken Supreme"
msgstr "Suprema de pollo de granja"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__favicon
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Favicon"
msgstr "Favicon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr "Característica uno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr "Característica tres"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "Feature Title"
msgstr "Título de la característica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr "Característica dos"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__feature_url
msgid "Feature Url"
msgstr "Mostrar URL"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Features"
msgstr "Funciones"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Features Grid"
msgstr "Tabla de funciones"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Fetched elements"
msgstr "Elementos obtenidos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Field"
msgstr "Campo"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__field_names
msgid "Field Names"
msgstr "Nombres de campos"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr "Campo para datos de formularios personalizados"

#. module: website
#: model:ir.model,name:website.model_ir_model_fields
msgid "Fields"
msgstr "Campos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "File Upload"
msgstr "Subir archivo"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_fs
msgid ""
"File from where the view originates.\n"
"                                                          Useful to (hard) reset broken views or to read arch from file in dev-xml mode."
msgstr ""
"Campo desde el que se origina la vista.\n"
"                                                          Útil para reestablecer (por completo) vistas dañadas o para leer la arquitectura de los archivos en el modo dev-xml."

#. module: website
#: model:ir.model,name:website.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr "Modelo de ayuda para la transmisión de archivos para controladores"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Filet Mignon 8oz"
msgstr "Filete mignon 8oz"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fill"
msgstr "Llenar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Fill and justify"
msgstr "Llenar y justificar"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__filter_id
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Filter"
msgstr "Filtro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Filter Intensity"
msgstr "Filtrar intensidad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Find a store near you"
msgstr "Encuentre una tienda cercana"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find all information about our deliveries, express deliveries and all you "
"need to know to return a product."
msgstr ""
"Encuentre toda la información sobre nuestras entregas, entregas exprés y "
"todo lo que necesita saber sobre cómo devolver un producto."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find out how we were able helping them and set in place solutions adapted to"
" their needs."
msgstr ""
"Descubra cómo pudimos ayudarles y poner en marcha soluciones adaptadas a sus"
" necesidades."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Find the perfect solution for you"
msgstr "Encuentre la solución perfecta para usted"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__create_date
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "First Connection"
msgstr "Primera conexión"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr "Primera función"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "First Menu"
msgstr "Primer menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "First Time Only"
msgstr "Solo la primera vez"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "First feature"
msgstr "Primera característica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "First list of Features"
msgstr "Primera lista de características"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,help:website.field_website_page__first_page_id
msgid "First page linked to this view"
msgstr "Primera página vinculada a esta vista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit content"
msgstr "Ajustar contenido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit text"
msgstr "Ajustar texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Fixed"
msgstr "Fijo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag"
msgstr "Marcar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag and Text"
msgstr "Marcar y texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flash"
msgstr "Flash"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Flat"
msgstr "Plano"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-X"
msgstr "Girar sobre eje X"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-Y"
msgstr "Girar sobre eje Y"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Float"
msgstr "Flotante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "Follow Us"
msgstr "Síganos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:ir.ui.view,arch_db:website.template_header_boxed_oe_structure_header_boxed_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_full_oe_structure_header_hamburger_full_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_1
msgid "Follow us"
msgstr "Síganos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font"
msgstr "Fuente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font Size"
msgstr "Tamaño de la fuente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font family"
msgstr "Familia de la fuente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font size"
msgstr "Tamaño de la fuente"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__footer_visible
#: model:ir.model.fields,field_description:website.field_website_page__footer_visible
msgid "Footer Visible"
msgstr "Pie de página visible"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Form"
msgstr "Formulario"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr ""
"Etiqueta de acción del formulario. Por ejemplo: crm.lead podría ser \"Enviar"
" un correo electrónico\" y project.issue podría ser \"Crear un ticket\"."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#: model:website.configurator.feature,name:website.feature_module_forum
#, python-format
msgid "Forum"
msgstr "Foro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing, and customer experience strategies."
msgstr ""
"Tony es el fundador y director, además también impulsa a la empresa. Le encanta\n"
"                                participar en el desarrollo del software y en\n"
"                                las estrategias de marketing y de experiencia del cliente."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Framed"
msgstr "Enmarcado"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Registro gratis"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Bottom"
msgstr "Desde abajo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Bottom Left"
msgstr "Desde abajo a la izquierda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Bottom Right"
msgstr "Desde abajo a la derecha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Left"
msgstr "Desde la izquierda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Right"
msgstr "Desde la derecha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Top"
msgstr "Desde arriba"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Top Left"
msgstr "Desde arriba a la izquierda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Top Right"
msgstr "Desde arriba a la derecha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"From seminars to team building activities, we offer a wide choice of events "
"to organize."
msgstr ""
"Desde seminarios hasta actividades que fomenten el trabajo en equipo, "
"ofrecemos una amplia variedad de eventos para organizar."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full"
msgstr "Completo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full Screen"
msgstr "Pantalla completa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Full Width"
msgstr "Ancho completo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full screen"
msgstr "Pantalla completa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full-Width"
msgstr "Ancho completo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Furniture"
msgstr "Muebles"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "G-XXXXXXXXXX"
msgstr "G-XXXXXXXXXX"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "GPS &amp; navigation"
msgstr "GPS y navegación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Gaming"
msgstr "Videojuegos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Get Delivered"
msgstr "Entregar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr "Obtener acceso a todos los módulos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr "Obtener acceso a todos los módulos y características"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Get in touch"
msgstr "Contactar"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_github
msgid "GitHub Account"
msgstr "Cuenta de GitHub"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_forum
msgid "Give visitors the information they need"
msgstr "Proporcione a los visitantes la información que necesitan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Glasses"
msgstr "Lentes"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/redirect_field.xml:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Go to"
msgstr "Ir a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr "Ir al administrador de páginas"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.backend.xml:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Go to Website"
msgstr "Ir al sitio web"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to the Theme tab"
msgstr "Ir a la pestaña de temas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Go to your Odoo Apps"
msgstr "Ir a sus aplicaciones de Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Good copy starts with understanding how your product or service helps your "
"customers. Simple words communicate better than big words and pompous "
"language."
msgstr ""
"Para escribir un buen texto debe entender cómo su producto o servicio ayuda "
"a sus clientes. Las palabras simples dicen mucho más que un lenguaje pomposo"
" y rimbombante."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Good job! It's time to <b>Save</b> your work."
msgstr "¡Bien hecho! Es hora de <b>guardar</b> su trabajo."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr "Google Analytics"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_analytics_key
#: model:ir.model.fields,field_description:website.field_website__google_analytics_key
msgid "Google Analytics Key"
msgstr "Clave de Google Analytics"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Google Font address"
msgstr "Dirección de Google Fonts"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Google Map"
msgstr "Mapa de Google"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Google Map API Key"
msgstr "Clave de la API de Google Maps"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__google_maps_api_key
msgid "Google Maps API Key"
msgstr "Clave de la API de Google Maps"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,field_description:website.field_website__google_search_console
msgid "Google Search Console"
msgstr "Search Console de Google"

#. module: website
#. odoo-python
#: code:addons/website/models/res_config_settings.py:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid ""
"Google doesn't need to be pinged anymore. It will automatically fetch your "
"/sitemap.xml."
msgstr ""
"Ya no es necesario conectarse a Google, obtendrá su /sitemap.xml en "
"automático."

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,help:website.field_website__google_search_console
msgid "Google key, or Enable to access first reply"
msgstr "Clave de Google, o habilitar para acceder a la primera respuesta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Gray #{grayCode}"
msgstr "Gris #{grayCode}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grays"
msgstr "Grises"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Great Value"
msgstr "Gran valor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"Las grandes historias son <b>para todos</b> incluso si se escribieron "
"<b>para una sola persona</b>. Si trata de escribirla pensando en un público "
"amplio y general, su historia sonará falsa y no será emocionante. A nadie le"
" interesará. Escriba para una persona. Si es genuina para una persona, lo "
"será para las demás."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"Las grandes historias tienen <b>personalidad</b>. Considere contar una gran "
"historia llena de personalidad. Escribir una historia con personalidad para "
"clientes potenciales le ayudará a mantener relaciones y conexiones. Use "
"palabras o frases que lo demuestren y recuerde escribir desde su punto de "
"vista, no desde la experiencia de alguien más."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grid"
msgstr "Tabla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__groups_id
msgid "Groups"
msgstr "Grupos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "H1"
msgstr "H1"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "H2"
msgstr "H2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "H4 Card title"
msgstr "H4 título de tarjeta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "H5 Card subtitle"
msgstr "H5 subtítulo de tarjeta"

#. module: website
#: model:ir.ui.menu,name:website.menu_ace_editor
msgid "HTML / CSS Editor"
msgstr "Editor HTML / CSS"

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half Screen"
msgstr "Media pantalla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half screen"
msgstr "Media pantalla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Full"
msgstr "Hamburguesa completa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Type"
msgstr "Tipo de hamburguesa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger menu"
msgstr "Menú hamburguesa"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
#, python-format
msgid "Happy Odoo Anniversary!"
msgstr "¡Feliz aniversario de Odoo!"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__has_social_default_image
msgid "Has Social Default Image"
msgstr "Tiene una imagen social predeterminada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Header"
msgstr "Encabezado"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__header_color
#: model:ir.model.fields,field_description:website.field_website_page__header_color
msgid "Header Color"
msgstr "Color del encabezado"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__header_overlay
#: model:ir.model.fields,field_description:website.field_website_page__header_overlay
msgid "Header Overlay"
msgstr "Superposición del encabezado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Header Position"
msgstr "Posición del encabezado"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__header_visible
#: model:ir.model.fields,field_description:website.field_website_page__header_visible
msgid "Header Visible"
msgstr "Encabezado visible"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Headings"
msgstr "Títulos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 1"
msgstr "Título 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 2"
msgstr "Título 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 3"
msgstr "Título 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 4"
msgstr "Título 4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 5"
msgstr "Título 5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 6"
msgstr "Título 6"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Headline"
msgstr "Titular"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Height"
msgstr "Altura"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height (Scrolled)"
msgstr "Altura (Desplazada)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Help center"
msgstr "Centro de ayuda"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr ""
"Aquí están los visuales utilizados para ayudarle a traducir eficientemente:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Here is an overview of the cookies that may be stored on your device when "
"you visit our website:"
msgstr ""
"Aquí está una vista general de las cookies que se pueden almacenar en su "
"dispositivo cuando visite nuestro sitio web:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hidden"
msgstr "Oculto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Hidden for"
msgstr "Oculto para"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Hide"
msgstr "Ocultar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Hide For"
msgstr "Ocultar para"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_popup/000.js:0
#, python-format
msgid "Hide the cookies bar"
msgstr "Ocultar la barra de cookies"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Hide this page from search results"
msgstr "Ocultar esta página de los resultados de búsqueda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "High"
msgstr "Alta"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: How to use Google Map on your website (Contact Us page and as a "
"snippet)"
msgstr ""
"Consejo: Cómo usar Google Maps en su sitio web (en la página de contacto y "
"como snippet)"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: Type '/' to search an existing page and '#' to link to an anchor."
msgstr ""
"Consejo: escriba '/' para buscar en una página existente y '#' para vincular"
" a un ancla."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: model:website.menu,name:website.menu_home
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#, python-format
msgid "Home"
msgstr "Inicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Home <span class=\"visually-hidden\">(current)</span>"
msgstr "Inicio <span class=\"visually-hidden\">(actual)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Home audio"
msgstr "Audio para el hogar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
#, python-format
msgid "Home page of the current website"
msgstr "Página de inicio del sitio web actual"

#. module: website
#. odoo-javascript
#. odoo-python
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__is_homepage
#: model:ir.ui.menu,name:website.menu_website_preview
#, python-format
msgid "Homepage"
msgstr "Página de inicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Homepage URL"
msgstr "URL de la página de inicio"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_homepage_url
#: model:ir.model.fields,field_description:website.field_website__homepage_url
msgid "Homepage Url"
msgstr "URL de la página de inicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Hoodies"
msgstr "Sudaderas con capucha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Horizontal"
msgstr "Horizontal"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Hours"
msgstr "Horas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "How can we help?"
msgstr "¿Cómo podemos ayudar?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hue"
msgstr "Matiz"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Hybrid"
msgstr "Híbrido"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "I agree"
msgstr "Estoy de acuerdo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "I am sure about this."
msgstr "Estoy seguro."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "I want"
msgstr "Quiero"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__id
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__id
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__id
#: model:ir.model.fields,field_description:website.field_theme_website_menu__id
#: model:ir.model.fields,field_description:website.field_theme_website_page__id
#: model:ir.model.fields,field_description:website.field_website__id
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__id
#: model:ir.model.fields,field_description:website.field_website_menu__id
#: model:ir.model.fields,field_description:website.field_website_page__id
#: model:ir.model.fields,field_description:website.field_website_rewrite__id
#: model:ir.model.fields,field_description:website.field_website_robots__id
#: model:ir.model.fields,field_description:website.field_website_route__id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__id
#: model:ir.model.fields,field_description:website.field_website_track__id
#: model:ir.model.fields,field_description:website.field_website_visitor__id
msgid "ID"
msgstr "ID"

#. module: website
#: model:ir.model.fields,help:website.field_base_automation__xml_id
#: model:ir.model.fields,help:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,help:website.field_ir_cron__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "ID de la acción si está definida en el archivo XML"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__xml_id
msgid "ID of the view defined in xml file"
msgstr "ID de la vista definida en el archivo xml"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__iap_page_code
msgid "Iap Page Code"
msgstr "Código de página de compras dentro de la aplicación"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__icon
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Icon"
msgstr "Icono"

#. module: website
#: model:ir.model.fields,help:website.field_website__specific_user_account
msgid "If True, new accounts will be associated to the current website"
msgstr ""
"Si se establece como True, las nuevas cuentas se asociarán al sitio web "
"actual"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_sequence
msgid "If set, a website menu will be created for the feature."
msgstr "Si se establece, se creará un menú de sitio web para la función."

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_company
msgid ""
"If set, add the menu as a second level menu, as a child of \"Company\" menu."
msgstr ""
"Si se establece, agrega el menú como un menú de segundo nivel, como un hijo "
"del menú de \"Empresa\"."

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,help:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "If set, replaces the website logo as the default social share image."
msgstr ""
"Si se establece, remplaza el logo del sitio web como la imagen "
"predeterminada para compartir en redes sociales."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset the template to its <strong>factory settings</strong>."
msgstr ""
"Si este error es causado por un cambio suyo en las plantillas, tiene la "
"posibilidad de restablecer la plantilla a su <strong>configuración de "
"fábrica</strong>."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr ""
"Si este campo está vacío, la vista aplica a todos los usuarios. En caso "
"contrario, la vista se aplica solo a los usuarios de estos grupos."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""
"Si se hereda esta vista, \n"
"* Si es \"TRUE\", la vista siempre extiende a la vista principal \n"
"* Si es \"FALSE\", la vista actual no extiende la vista principal, pero se puede habilitar\n"
"             "

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"Todos los cambios sin guardar se perderán si descarta las ediciones "
"actuales. Puede cancelar para regresar al modo de edición."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#, python-format
msgid ""
"If you need to add analytics or marketing tags, inject code in your <head> "
"or <body> instead. The option is in the \"Theme\" tab."
msgstr ""
"Si necesita agregar etiquetas analíticas o de marketing, introduzca el "
"código en <head> o en <body>. La opción está disponible en la pestaña "
"\"Tema\"."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_image
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Image"
msgstr "Imagen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image - Text"
msgstr "Imagen - Texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Image Cover"
msgstr "Imagen de portada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image Gallery"
msgstr "Galería de imágenes "

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#, python-format
msgid "Image Gallery Dialog"
msgstr "Diálogo de la galería de imágenes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Image Menu"
msgstr "Menú de imagen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Image Size"
msgstr "Tamaño de la imagen "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Image Text Image"
msgstr "Imagen - Texto - Imagen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Images"
msgstr "Imágenes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Images Spacing"
msgstr "Espaciado de las imágenes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Images Subtitles"
msgstr "Subtítulos de las imágenes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Images Wall"
msgstr "Muro de imágenes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "In"
msgstr "Entrada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "In Main Menu"
msgstr "En el menú principal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "In Place"
msgstr "En su lugar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "In the meantime we invite you to visit our"
msgstr "Mientras tanto, le invitamos a visitar nuestro"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
#, python-format
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"            Each modification on the master page is automatically applied to all translated versions."
msgstr ""
"En este modo, solo puede traducir textos. Debe editar la página maestra para cambiar la estructura de la página.\n"
"            Cada modificación a la página maestra se aplica de forma automática a todas las versiones traducidas."

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__include
msgid "Include"
msgstr "Incluir"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Indexed"
msgstr "Indexado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Indicators"
msgstr "Indicadores"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Info"
msgstr "Información"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Info Page"
msgstr "Página de información"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_about_us
msgid "Info and stats about your company"
msgstr "Información y estadísticas sobre su empresa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr "Información sobre"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__inherit_id
msgid "Inherit"
msgstr "Heredar"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_id
msgid "Inherited View"
msgstr "Vista heredada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inline"
msgstr "En línea"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Inner"
msgstr "Interior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Inner content"
msgstr "Contenido interior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Aligned"
msgstr "Alineación de entrada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Type"
msgstr "Tipo de entrada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inputs"
msgstr "Entradas"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a badge snippet."
msgstr "Insertar un snippet de insignia."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a blockquote snippet."
msgstr "Insertar un snippet de bloque de cita."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a card snippet."
msgstr "Insertar un snippet de tarjeta."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a chart snippet."
msgstr "Insertar un snippet de gráfico."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a progress bar snippet."
msgstr "Insertar un snippet de barra de progreso."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a rating snippet."
msgstr "Insertar un snippet de calificación."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a share snippet."
msgstr "Insertar un snippet de compartir."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a text Highlight snippet."
msgstr "Insertar un snippet de texto destacado."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert an alert snippet."
msgstr "Insertar un snippet de alerta."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert an horizontal separator sippet."
msgstr "Insertar un snippet de separador horizontal."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Insert text styles like headers, bold, italic, lists, and fonts with\n"
"                            a simple WYSIWYG editor. Flexible and easy to use."
msgstr ""
"Inserte estilos de texto como encabezados, negritas, cursiva, listas y fuentes con un\n"
"                            simple editor WYSIWYG. Flexible y fácil de usar."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Inset"
msgstr "Recuadro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Instagram"
msgstr "Instagram"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_instagram
msgid "Instagram Account"
msgstr "Cuenta de Instagram"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Install"
msgstr "Instalar"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "Instalar idioma"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install languages"
msgstr "Instalar idiomas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "Aplicaciones instaladas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Localizations / Account Charts"
msgstr "Localizaciones/planes de cuentas instalados"

#. module: website
#: model:ir.model.fields,help:website.field_website__theme_id
msgid "Installed theme"
msgstr "Tema instalado"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Installing \"%s\""
msgstr "Instalando \"%s\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Intensity"
msgstr "Intensidad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Interaction History<br/>(optional)"
msgstr "Historial de interacciones<br/>(opcional)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Intuitive system"
msgstr "Sistema intuitivo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invalid API Key. The following error was returned by Google:"
msgstr "La clave API no es válida, Google muestra el siguiente error:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Iris cuenta con experiencia internacional y nos ayuda a entender fácilmente "
"los números y cómo mejorarlos. Uno de sus objetivos es impulsar el éxito de "
"nuestra empresa y llevarla al siguiente nivel."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Is In Main Menu"
msgstr "En el menú prinicipal"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__is_in_menu
msgid "Is In Menu"
msgstr "En el menú"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_indexed
msgid "Is Indexed"
msgstr "Indexado"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__is_installed_on_current_website
msgid "Is Installed On Current Website"
msgstr "Instalado en el sitio web actual"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_mega_menu
msgid "Is Mega Menu"
msgstr "Es megamenú"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__is_published
#: model:ir.model.fields,field_description:website.field_res_users__is_published
#: model:ir.model.fields,field_description:website.field_theme_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__is_published
msgid "Is Published"
msgstr "Está publicado"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_visible
#: model:ir.model.fields,field_description:website.field_website_page__is_visible
msgid "Is Visible"
msgstr "Es visible"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after"
msgstr "Es después de"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after or equal to"
msgstr "Es después o igual a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before"
msgstr "Es antes de "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before or equal to"
msgstr "Es antes o igual a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is between (included)"
msgstr "Es entre (incluyendo)"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__is_connected
msgid "Is connected ?"
msgstr "¿Está conectado?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is equal to"
msgstr "Es igual a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than"
msgstr "Es mayor que"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than or equal to"
msgstr "Es mayor o igual a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than"
msgstr "Es menor que"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than or equal to"
msgstr "Es menor o igual a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not between (excluded)"
msgstr "No está entre (excluido)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not equal to"
msgstr "No es igual a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not set"
msgstr "No está establecido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is set"
msgstr "Está establecido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid ""
"It appears your website is still using the old color system of\n"
"            Odoo 13.0 in some places. We made sure it is still working but\n"
"            we recommend you to try to use the new color system, which is\n"
"            still customizable."
msgstr ""
"Parece que su sitio web aún utiliza el antiguo sistema de colores de\n"
"            Odoo 13.0 para algunas cosas. Aunque todo funcione,\n"
"            le recomendamos que pruebe el nuevo sistema de colores, también\n"
"            es personalizable."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "It looks like your file is being called by"
msgstr "Parece que se necesita su archivo en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Item"
msgstr "Elemento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Item 1"
msgstr "Tema 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Item 2"
msgstr "Tema 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Items"
msgstr "Elementos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jacket"
msgstr "Chamarras"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jeans"
msgstr "Pantalones"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Join us and make your company a better place."
msgstr "Contáctenos y juntos mejoraremos su empresa."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Keep empty to use default value"
msgstr "Déjelo vacío para usar el valor predeterminado"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__key
#: model:ir.model.fields,field_description:website.field_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__key
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__key
#: model:ir.model.fields,field_description:website.field_website_page__key
msgid "Key"
msgstr "Clave"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Keyboards"
msgstr "Teclados"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Keyword"
msgstr "Palabra clave"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Keywords"
msgstr "Palabras clave"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Label"
msgstr "Etiqueta"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_label
msgid "Label for form action"
msgstr "Etiqueta de acción del formulario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Labels Width"
msgstr "Ancho de las etiquetas"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__lang_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Language"
msgstr "Idioma"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Language Selector"
msgstr "Selector de idioma"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__lang_id
msgid "Language from the website when visitor has been created"
msgstr "Idioma del sitio web cuando se ha creado el visitante"

#. module: website
#: model:ir.model,name:website.model_res_lang
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_ids
#: model:ir.model.fields,field_description:website.field_website__language_ids
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Languages"
msgstr "Idiomas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr "Idiomas disponibles en su sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Laptops"
msgstr "Laptops"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Large"
msgstr "Grande"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last 7 Days"
msgstr "Últimos 7 días"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Last Action"
msgstr "Última acción"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_connection_datetime
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last Connection"
msgstr "Última conexión"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Last Feature"
msgstr "Última característica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Last Menu"
msgstr "Último menú"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website____last_update
#: model:ir.model.fields,field_description:website.field_website_configurator_feature____last_update
#: model:ir.model.fields,field_description:website.field_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website_rewrite____last_update
#: model:ir.model.fields,field_description:website.field_website_robots____last_update
#: model:ir.model.fields,field_description:website.field_website_route____last_update
#: model:ir.model.fields,field_description:website.field_website_snippet_filter____last_update
#: model:ir.model.fields,field_description:website.field_website_track____last_update
#: model:ir.model.fields,field_description:website.field_website_visitor____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Month"
msgstr "Mes pasado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Last Page"
msgstr "Última página"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website__write_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_uid
#: model:ir.model.fields,field_description:website.field_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_uid
#: model:ir.model.fields,field_description:website.field_website_robots__write_uid
#: model:ir.model.fields,field_description:website.field_website_route__write_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website__write_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_date
#: model:ir.model.fields,field_description:website.field_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_date
#: model:ir.model.fields,field_description:website.field_website_robots__write_date
#: model:ir.model.fields,field_description:website.field_website_route__write_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_date
#: model:ir.model.fields,field_description:website.field_website_visitor__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_visited_page_id
msgid "Last Visited Page"
msgstr "Última página visitada"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Week"
msgstr "Semana anterior"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Year"
msgstr "Año anterior"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__time_since_last_action
msgid "Last action"
msgstr "Última acción"

#. module: website
#. odoo-python
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Last modified pages"
msgstr "Últimas páginas modificadas"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__last_connection_datetime
msgid "Last page view date"
msgstr "Fecha de la última visita a la página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Latests news and case studies"
msgstr "Las últimas noticias y casos de estudio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Layout"
msgstr "Diseño"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background"
msgstr "Diseño del fondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background Color"
msgstr "Color del diseño del fondo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Learn more"
msgstr "Más información"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "Izquierda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Left Menu"
msgstr "Menú izquierdo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Legal"
msgstr "Legal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Legal Notice"
msgstr "Aviso legal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Legend"
msgstr "Leyenda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Let your customers follow <br/>and understand your process."
msgstr "Permita que sus clientes sigan <br/> y entiendan su proceso."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Permita que sus clientes inicien sesión para ver sus documentos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Let's do it"
msgstr "¡Hagámoslo!"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Let's go!"
msgstr "¡Vamos!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Library"
msgstr "Biblioteca"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Light"
msgstr "Claro"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__limit
msgid "Limit"
msgstr "Límite"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr "Personalización limitada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Line"
msgstr "Línea"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Line Color"
msgstr "Color de línea"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Link"
msgstr "Enlace"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Link Anchor"
msgstr "Enlace de ancla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Link Style"
msgstr "Estilo del enlace"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Link button"
msgstr "Botón de enlace"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Link text"
msgstr "Texto del enlace"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_linkedin
msgid "LinkedIn Account"
msgstr "Cuenta de LinkedIn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Linkedin"
msgstr "LinkedIn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links"
msgstr "Enlaces"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links Style"
msgstr "Estilo de enlaces"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Links to other Websites"
msgstr "Enlaces a otros sitios web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Little Icons"
msgstr "Iconos pequeños"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_live_chat
msgid "Live Chat"
msgstr "Chat en vivo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Live Preview"
msgstr "Previsualización dinámica"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Livechat Widget"
msgstr "Widget de chat en vivo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: code:addons/website/static/src/xml/website.background.video.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Loading..."
msgstr "Cargando…"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Logo"
msgstr "Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Logo of MyCompany"
msgstr "Logo de MiEmpresa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logos"
msgstr "Logos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Long Text"
msgstr "Texto largo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Low"
msgstr "Baja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Magazine"
msgstr "Revista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Main Course"
msgstr "Plato fuerte"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__menu_id
msgid "Main Menu"
msgstr "Menú principal"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.xml:0
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#, python-format
msgid "Main actions"
msgstr "Acciones principales"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Main page of your website served to visitors"
msgstr "Página principal de su sitio web a los visitantes"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure billing is enabled"
msgstr "Asegúrese de habilitar la facturación"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Make sure to wait if errors keep being shown: sometimes enabling an API "
"allows to use it immediately but Google keeps triggering errors for a while"
msgstr ""
"Manténgase atento por si siguen apareciendo errores, algunas veces habilitar"
" una API permite su uso de inmediato, pero Google sigue mostrando errores "
"por un tiempo."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure your settings are properly configured:"
msgstr "Asegúrese de que sus ajustes se hayan configurado de forma adecuada:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Map"
msgstr "Mapa"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps JavaScript API"
msgstr "API de Maps JavaScript"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps Static API"
msgstr "API de Maps Static"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Mark Text"
msgstr "Marcar texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Marked Fields"
msgstr "Campos marcados"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Marker style"
msgstr "Estilo de marcador"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Marketplace"
msgstr "Mercado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Masonry"
msgstr "Cuadros"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Max Axis"
msgstr "Eje máximo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Measurement ID"
msgstr "ID de medición"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Media"
msgstr "Medios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Media List"
msgstr "Lista de medios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Media heading"
msgstr "Título del medio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "Medio"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Mega Menu"
msgstr "Mega menú"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__mega_menu_classes
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_classes
msgid "Mega Menu Classes"
msgstr "Clases de mega menú"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__mega_menu_content
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_content
msgid "Mega Menu Content"
msgstr "Contenido del mega menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Men"
msgstr "Hombres"

#. module: website
#: model:ir.model,name:website.model_ir_ui_menu
#: model:ir.model.fields,field_description:website.field_website_menu__name
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Menu"
msgstr "Menú"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_company
msgid "Menu Company"
msgstr "Menú de empresa"

#. module: website
#: model:ir.ui.menu,name:website.menu_edit_menu
msgid "Menu Editor"
msgstr "Editor de menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Menu Item %s"
msgstr "Elemento de menú %s"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_sequence
msgid "Menu Sequence"
msgstr "Secuencia del menú"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__copy_ids
msgid "Menu using a copy of me"
msgstr "Menú que utiliza una copia de mí"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_menu_list
msgid "Menus"
msgstr "Menús"

#. module: website
#: model:ir.model,name:website.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "Asistente de fusión de contactos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Messages"
msgstr "Mensajes"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Metadata"
msgstr "Metadatos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Mich cuenta con varios años de experiencia como director comercial en el "
"sector informático. Le encantan los desafíos y ha ayudado a la empresa a "
"llegar hasta donde está hoy. En definitiva, tiene una de las mejores mentes."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Middle"
msgstr "Medio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Min Axis"
msgstr "Eje mínimo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Min-Height"
msgstr "Altura mínima"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Minimalist"
msgstr "Minimalista"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Minutes"
msgstr "Minutos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_visitor__mobile
#, python-format
msgid "Mobile"
msgstr "Celular"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile Alignment"
msgstr "Alineación móvil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Mobile Preview"
msgstr "Vista previa en celular"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile menu"
msgstr "Menú para celular"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/mobile_preview.xml:0
#: code:addons/website/static/src/systray_items/mobile_preview.xml:0
#, python-format
msgid "Mobile preview"
msgstr "Vista previa en celular"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__mode
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Mode"
msgstr "Modo"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model
msgid "Model"
msgstr "Modelo"

#. module: website
#: model:ir.model,name:website.model_ir_model_data
#: model:ir.model.fields,field_description:website.field_website_page__model_data_id
msgid "Model Data"
msgstr "Datos del modelo"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__model_name
msgid "Model name"
msgstr "Nombre del modelo"

#. module: website
#: model:ir.model,name:website.model_ir_model
msgid "Models"
msgstr "Modelos"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_updated
msgid "Modified Architecture"
msgstr "Arquitectura modificada"

#. module: website
#: model:ir.model,name:website.model_ir_module_module
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__module_id
msgid "Module"
msgstr "Módulo"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__module_marketing_automation
msgid "Module Marketing Automation"
msgstr "Automatización del módulo de marketing"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__module_website_livechat
msgid "Module Website Livechat"
msgstr "Chat en vivo del módulo de Sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Monitor Google Search results data"
msgstr "Monitoree los datos de resultado de búsqueda de Google"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid ""
"Monitor your visitors while they are browsing your website with the Odoo "
"Social app. Engage with them in just a click using a live chat request or a "
"push notification. If they have completed one of your forms, you can send "
"them an SMS, or call them right away while they are browsing your website."
msgstr ""
"Monitoree a sus visitantes mientras navegan en su sitio web con la "
"aplicación Marketing social de Odoo. Interactúe con ellos con solo un clic "
"gracias al chat en vivo o con una notificación push. Si han completado uno "
"de sus formularios, también puede mandarles un SMS, o llamarlos mientras "
"están navegando su sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Monitors"
msgstr "Monitores"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "More Details"
msgstr "Más detalles"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid ""
"More than 90 shapes exist and their colors are picked to match your Theme."
msgstr ""
"Existen más de 90 figuras y sus colores se seleccionan para combinar con su "
"tema."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Mosaic"
msgstr "Mosaico"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Most searched topics related to your keyword, ordered by importance"
msgstr ""
"Temas más buscados relacionados con su palabra clave, ordenados por "
"importancia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Mouse"
msgstr "Mouse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Backward"
msgstr "Mover hacia atrás"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Forward"
msgstr "Mover hacia adelante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to first"
msgstr "Mover al principio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to last"
msgstr "Mover al final"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to next"
msgstr "Mover al siguiente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to previous"
msgstr "Mover al anterior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Multi Menus"
msgstr "Multimenús"

#. module: website
#: model:ir.model,name:website.model_website_multi_mixin
msgid "Multi Website Mixin"
msgstr "Mixin de sitio web múltiple"

#. module: website
#: model:ir.model,name:website.model_website_published_multi_mixin
msgid "Multi Website Published Mixin"
msgstr "Mixin publicado de sitio web múltiple"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__group_multi_website
#: model:res.groups,name:website.group_multi_website
msgid "Multi-website"
msgstr "Sitio web múltiple"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Multimedia"
msgstr "Multimedia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Multiple Checkboxes"
msgstr "Múltiples casillas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Multiple tree exists for this view"
msgstr "Existen varios árboles para esta vista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "My Company"
msgstr "Mi empresa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_brand_name
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "My Website"
msgstr "Mi sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "MyCompany"
msgstr "MiEmpresa"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__name
#: model:ir.model.fields,field_description:website.field_website_rewrite__name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__name
#: model:ir.model.fields,field_description:website.field_website_visitor__name
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Name"
msgstr "Nombre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (A-Z)"
msgstr "Nombre (A-Z)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Name, id or key"
msgstr "Nombre, ID o clave"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr "Estrecho"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Navbar"
msgstr "Barra de navegación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Need to pick up your order at one of our stores? Discover the nearest to "
"you."
msgstr ""
"¿Necesita recoger su orden en una de nuestras tiendas? Descubra la más "
"cercana a usted."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Network Advertising Initiative opt-out page"
msgstr "Página de exclusión de la Network Advertising Initiative"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Networks"
msgstr "Redes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.neutralize_ribbon
msgid "Neutralized"
msgstr "Se neutralizó"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.xml:0
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#, python-format
msgid "New Page"
msgstr "Nueva página"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__new_window
#: model:ir.model.fields,field_description:website.field_website_menu__new_window
msgid "New Window"
msgstr "Nueva ventana"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "New collection"
msgstr "Nueva colección"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "New customer"
msgstr "Nuevo cliente"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_news
msgid "News"
msgstr "Noticias"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr "Boletín informativo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Popup"
msgstr "Ventana emergente de boletín informativo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#, python-format
msgid "Next"
msgstr "Siguiente"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "No Slide Effect"
msgstr "Sin efecto de diapositiva"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/widget_iframe.xml:0
#, python-format
msgid "No Url"
msgstr "Sin URL"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid "No Visitors yet!"
msgstr "Todavía no hay visitantes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No condition"
msgstr "Sin condición"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr "Sin personalización"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "No matching record !"
msgstr "No hay registros que coincidan."

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_page_action
msgid "No page views yet for this visitor"
msgstr "Aún no hay visitas a esta página para este visitante"

#. module: website
#: model_terms:ir.actions.act_window,help:website.visitor_partner_action
msgid "No partner linked for this visitor"
msgstr "No hay ningún contacto vinculado para este visitante"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "No result found, broaden your search."
msgstr "No se encontraron resultados, amplíe su búsqueda."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "No results found for '"
msgstr "No se encontraron resultados para \""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "No se encontraron resultados. Intente otra búsqueda."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "Sin soporte"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/website_switcher.js:0
#, python-format
msgid "No website domain configured for this website."
msgstr "Este sitio web no tiene ningún dominio web configurado."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "None"
msgstr "Ninguno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Normal"
msgstr "Normal"

#. module: website
#: model:website,prevent_zero_price_sale_text:website.default_website
#: model:website,prevent_zero_price_sale_text:website.website2
msgid "Not Available For Sale"
msgstr "No está disponible para venta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "Not Published"
msgstr "Sin publicar"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid ""
"Not only can you search for royalty-free illustrations, their colors are "
"also converted so that they always fit your Theme."
msgstr ""
"No solo puede buscar ilustraciones libres de derechos de autor, se cambian "
"sus colores para que siempre combinen con su tema."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not published"
msgstr "Sin publicar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not tracked"
msgstr "Sin seguimiento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Note that some third-party services may install additional cookies on your "
"browser in order to identify you."
msgstr ""
"Tenga en cuenta que algunos servicios de terceros pueden instalar cookies "
"adicionales en su navegador con el fin de identificarle."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ""
"Note: To hide this page, uncheck it from the Customize tab in edit mode."
msgstr ""
"Nota: para ocultar esta página, desmárquela en la pestaña \"personalizar\" "
"en el modo de edición."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Nothing"
msgstr "Nada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Number"
msgstr "Número"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_language_count
#: model:ir.model.fields,field_description:website.field_website__language_count
msgid "Number of languages"
msgstr "Número de idiomas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Numbers"
msgstr "Números"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "OR"
msgstr "OR"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Information"
msgstr "Información de Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Menu"
msgstr "Menú de Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "Versión de Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Off-Canvas"
msgstr "Offcanvas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Off-Canvas Logo"
msgstr "Logo offcanvas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office audio"
msgstr "Audio de oficina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office screens"
msgstr "Pantallas de oficina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Offline"
msgstr "Desconectado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Offset (X, Y)"
msgstr "Desplazamiento (X, Y)"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "Ok"
msgstr "De acuerdo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "Ok, never show me this again"
msgstr "De acuerdo, no volver a mostrar esto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Appearance"
msgstr "Cuando aparece"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Click"
msgstr "Al hacer clic"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "On Exit"
msgstr "A la salida"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Hover"
msgstr "Al pasar el cursor por encima"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Scroll"
msgstr "Al deslizar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "On Success"
msgstr "Éxito"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "Por invitación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Once the user closes the popup, it won't be shown again for that period of "
"time."
msgstr ""
"Una vez que el usuario cierra la ventana emergente, no volverá a aparecer "
"otra vez por un periodo de tiempo."

#. module: website
#. odoo-python
#: code:addons/website/models/website_configurator_feature.py:0
#, python-format
msgid ""
"One and only one of the two fields 'page_view_id' and 'module_id' should be "
"set"
msgstr ""
"Se debe establecer uno y solo uno de los dos campos \"page_view_id\" y "
"\"module_id\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Online"
msgstr "En línea"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Only allow essential cookies"
msgstr "Permitir solo las cookies necesarias"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""
"Solo aplica si esta vista hereda de otra (inherit_id no es False/Null).\n"
"\n"
"* Si tiene extensión (predeterminada), cuando se solicita esta vista, se busca\n"
"la vista primaria más cercana (via inherit_id), y luego se aplica a todas las vistas\n"
"que hereden de ella\n"
"* Si es primaria, la vista primaria más cercana se resuelve completamente (incluso si utiliza un\n"
"modelo diferente a este), y más tarde se aplican las especificaciones de herencia\n"
"(<xpath/>) y el resultado se utiliza como si fuera la arquitectura real\n"
"de la vista.\n"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "Only essentials"
msgstr "Solo las necesarias"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "ERP de código abierto"

#. module: website
#: model:ir.actions.client,name:website.action_open_website_configurator
msgid "Open Website Configurator"
msgstr "Abrir configurador de sitio web"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#: model:ir.ui.menu,name:website.menu_optimize_seo
#, python-format
msgid "Optimize SEO"
msgstr "Optimizar SEO"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option"
msgstr "Opción"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 1"
msgstr "Opción 1"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 2"
msgstr "Opción 2"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 3"
msgstr "Opción 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Optional"
msgstr "Opcional"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Order by"
msgstr "Ordenar por"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "Ordenar ahora"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Other Information:"
msgstr "Otra información:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Our Company"
msgstr "Nuestra empresa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Our References"
msgstr "Nuestras referencias"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Our seminars and trainings for you"
msgstr "Nuestros seminarios y capacitaciones para usted"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Our team"
msgstr "Nuestro equipo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Our team will message you back as soon as possible."
msgstr "Nuestro equipo le responderá lo antes posible."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Out"
msgstr "Salida"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Outline"
msgstr "Contorno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Outset"
msgstr "Comienzo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Outstanding images"
msgstr "Imágenes sobresalientes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Over The Content"
msgstr "Sobre el contenido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Padding (Y, X)"
msgstr "Padding (Y, X)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Paddings"
msgstr "Paddings"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.xml:0
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view__page_ids
#: model:ir.model.fields,field_description:website.field_theme_website_menu__page_id
#: model:ir.model.fields,field_description:website.field_website_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_track__page_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#, python-format
msgid "Page"
msgstr "Página"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Page Anchor"
msgstr "Ancla de página"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__website_indexed
msgid "Page Indexed"
msgstr "Página indexada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Layout"
msgstr "Diseño de la página"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#, python-format
msgid "Page Name"
msgstr "Nombre de la página"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#, python-format
msgid "Page Properties"
msgstr "Propiedades de la página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Page Title"
msgstr "Título de la página"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__url
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Page URL"
msgstr "URL de la página"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__page_view_id
msgid "Page View"
msgstr "Vista de página"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_view_action
#: model:ir.model.fields,field_description:website.field_website_visitor__visitor_page_count
#: model:ir.ui.menu,name:website.menu_visitor_view_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Page Views"
msgstr "Vistas a la página"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_page_action
msgid "Page Views History"
msgstr "Historial de vistas a la página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Visibility"
msgstr "Visibilidad de página"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__iap_page_code
msgid ""
"Page code used to tell IAP website_service for which page a snippet list "
"should be generated"
msgstr ""
"Código de página que se utiliza para indicar el IAP website_service para el "
"que se genera una lista de snippets"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__copy_ids
msgid "Page using a copy of me"
msgstr "Página que utiliza una copia mía "

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: model:ir.ui.menu,name:website.menu_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Pages"
msgstr "Páginas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Pagination"
msgstr "Paginación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Pants"
msgstr "Pantalones"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid ""
"Paragraph text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."
msgstr ""
"Texto del párrafo. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing"
" elit. <i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"Paragraph with <strong>bold</strong>, <span class=\"text-"
"muted\">muted</span> and <em>italic</em> texts"
msgstr ""
"Párrafo con texto <strong>en negritas</strong>, <span class=\"text-"
"muted\">silenciado</span> y <em>en cursiva</em>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Paragraph."
msgstr "Párrafo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Parallax"
msgstr "Desplazamiento de paralaje"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__parent_id
msgid "Parent"
msgstr "Principal"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_id
msgid "Parent Menu"
msgstr "Menú principal"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_path
msgid "Parent Path"
msgstr "Ruta principal"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__partner_id
msgid "Partner of the last logged in user."
msgstr "Contacto del último usuario conectado."

#. module: website
#: model:ir.model.fields,help:website.field_website__partner_id
msgid "Partner-related data of the user"
msgstr "Datos relacionados al contacto del usuario"

#. module: website
#: model:ir.actions.act_window,name:website.visitor_partner_action
msgid "Partners"
msgstr "Contactos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Password"
msgstr "Contraseña"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__path
msgid "Path"
msgstr "Ruta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pattern"
msgstr "Patrón"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Pay"
msgstr "Pagar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Phone Number"
msgstr "Número de teléfono"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Phones"
msgstr "Teléfonos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#: model:ir.actions.act_window,name:website.theme_install_kanban_action
#, python-format
msgid "Pick a Theme"
msgstr "Elija un tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Picture"
msgstr "Foto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Pie"
msgstr "Circular"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Pill"
msgstr "Píldora"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pills"
msgstr "Píldoras"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Placeholder"
msgstr "Marcador de posición"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Places API"
msgstr "API de Places"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Plain"
msgstr "Simple"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_plausible_shared_key
msgid "Plausible Analytics"
msgstr "Plausible Analytics"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__plausible_shared_key
msgid "Plausible Shared Key"
msgstr "Clave compartida de Plausible"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__plausible_site
msgid "Plausible Site"
msgstr "Sitio de Plausible"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__plausible_site
msgid "Plausible Site (e.g. domain.com)"
msgstr "Sitio de Plausible (por ejemplo, dominio.com)"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__plausible_shared_key
msgid "Plausible auth Key"
msgstr "Clave de autenticación de Plausible"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Please fill in the form correctly."
msgstr "Complete el formulario correctamente."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Points of sale"
msgstr "Puntos de venta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Popup"
msgstr "Ventana emergente"

#. module: website
#: model:ir.model,name:website.model_portal_wizard_user
msgid "Portal User Config"
msgstr "Configuración del portal del usuario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Portfolio"
msgstr "Portafolio"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Position"
msgstr "Posición"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Post heading"
msgstr "Título de la publicación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Postcard"
msgstr "Postal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Preferences<br/>(essential)"
msgstr "Preferencias<br/>(esencial)"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__prepend
msgid "Prepend"
msgstr "Anteponer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Preset"
msgstr "Preestablecido"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fullscreen_indication/fullscreen_indication.js:0
#, python-format
msgid "Press %(key)s to exit full screen"
msgstr "Presione %(key)s para salir de la pantalla completa"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy.js:0
#, python-format
msgid "Press %s for next %s"
msgstr "Presione %s para el siguiente %s"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Preview"
msgstr "Vista previa"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#, python-format
msgid "Previous"
msgstr "Anterior"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_prev
msgid "Previous View Architecture"
msgstr "Arquitectura de la vista anterior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Pricelist"
msgstr "Lista de precios"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_pricing
#: model_terms:ir.ui.view,arch_db:website.pricing
msgid "Pricing"
msgstr "Precio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary"
msgstr "Primario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary Style"
msgstr "Estilo primario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Printers"
msgstr "Impresoras"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__priority
msgid "Priority"
msgstr "Prioridad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Privacy"
msgstr "Privacidad"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#: model:website.configurator.feature,name:website.feature_page_privacy_policy
#: model_terms:ir.ui.view,arch_db:website.privacy_policy
#, python-format
msgid "Privacy Policy"
msgstr "Política de privacidad"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
#, python-format
msgid "Product"
msgstr "Producto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Products"
msgstr "Productos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "Profesional"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Professional themes"
msgstr "Temas profesionales"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Profile"
msgstr "Perfil"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Progress Bar"
msgstr "Barra de progreso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Color"
msgstr "Color de la barra de progreso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Style"
msgstr "Estilo de la barra de progreso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Weight"
msgstr "Peso de la barra de progreso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Projectors"
msgstr "Proyectores"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Promotions"
msgstr "Promociones"

#. module: website
#: model:ir.ui.menu,name:website.menu_page_properties
msgid "Properties"
msgstr "Propiedades"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__partner_id
msgid "Public Partner"
msgstr "Partner público"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__user_id
msgid "Public User"
msgstr "Usuario público"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.js:0
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#, python-format
msgid "Publish"
msgstr "Publicar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.js:0
#, python-format
msgid "Publish Website Content"
msgstr "Publicar contenido de sitio web"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_career
msgid "Publish job offers and let people apply"
msgstr "Publique ofertas de trabajo y permita que la gente se postule"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_event
msgid "Publish on-site and online events"
msgstr "Publique eventos presenciales y en línea"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/redirect_field.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/systray_items/publish.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
#, python-format
msgid "Published"
msgstr "Publicado"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__date_publish
msgid "Publishing Date"
msgstr "Fecha de publicación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pulse"
msgstr "Pulse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Purpose"
msgstr "Propósito"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr "¡Enfóquese en lo que tiene que decir!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating
msgid "Quality"
msgstr "Calidad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Quotes"
msgstr "Citas"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Campo Qweb de contacto"

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr "Campo HTML Qweb"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Radar"
msgstr "Radar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Radio"
msgstr "Radio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Radio Buttons"
msgstr "Botones de opción"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Rating"
msgstr "Calificación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Re-order"
msgstr "Reordenar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Ready to build the"
msgstr "¿Listo para crear el"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Recipient Email"
msgstr "Correo electrónico del destinatario"

#. module: website
#: model:ir.model,name:website.model_ir_rule
msgid "Record Rule"
msgstr "Regla de registro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Redirect"
msgstr "Redireccionar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Redirect to URL in a new tab"
msgstr "Redireccionar al URL en una nueva pestaña"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.js:0
#, python-format
msgid "Redirecting..."
msgstr "Redirigiendo..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Redirection Type"
msgstr "Tipo de redirección"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_rewrite
msgid "Redirects"
msgstr "Redireccionamientos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "References"
msgstr "Referencias"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Refresh route's list"
msgstr "Actualizar la lista de rutas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Regular"
msgstr "Regular"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr "Elementos de menú relacionados"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__menu_ids
msgid "Related Menus"
msgstr "Menús relacionados"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__page_id
msgid "Related Page"
msgstr "Página relacionada"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Related keywords"
msgstr "Palabras claves relacionadas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Remember information about the preferred look or behavior of the website, "
"such as your preferred language or region."
msgstr ""
"Recuerde la información sobre la apariencia o el comportamiento preferidos "
"del sitio web, tales como su idioma o región preferidos."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__remove
#, python-format
msgid "Remove"
msgstr "Eliminar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Row"
msgstr "Eliminar fila"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Serie"
msgstr "Eliminar serie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Slide"
msgstr "Eliminar diapositiva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Remove Tab"
msgstr "Eliminar pestaña"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Remove all"
msgstr "Eliminar todo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Remove theme"
msgstr "Eliminar tema"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__replace
msgid "Replace"
msgstr "Reemplazar "

#. module: website
#: model:ir.ui.menu,name:website.menu_reporting
msgid "Reporting"
msgstr "Reportes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Required"
msgstr "Requerido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset templates"
msgstr "Restablecer las plantillas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset to initial version (hard reset)."
msgstr "Restablecer a la versión inicial (restablecimiento completo)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Resources"
msgstr "Recursos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Respecting your privacy is our priority."
msgstr "Nuestra prioridad es respetar su privacidad."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Restore previous version (soft reset)."
msgstr "Restablecer a la versión anterior (reinicio parcial)."

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_id
#: model:ir.model.fields,help:website.field_res_users__website_id
#: model:ir.model.fields,help:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_page__website_id
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_id
msgid "Restrict publishing to this website."
msgstr "Restringir publicaciones a este sitio web."

#. module: website
#: model:res.groups,name:website.group_website_restricted_editor
msgid "Restricted Editor"
msgstr "Editor restringido"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__restricted_group
msgid "Restricted Group"
msgstr "Grupo restringido"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_rewrite_list
msgid "Rewrite"
msgstr "Reescritura de URL"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr "Derecha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Right Menu"
msgstr "Menú derecho"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Ripple Effect"
msgstr "Efecto de ondulación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Road"
msgstr "Camino"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "RoadMap"
msgstr "Mapa de ruta"

#. module: website
#. odoo-python
#: code:addons/website/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:website.field_website__robots_txt
#, python-format
msgid "Robots.txt"
msgstr "Robots.txt"

#. module: website
#: model:ir.model,name:website.model_website_robots
msgid "Robots.txt Editor"
msgstr "Editor de Robots.txt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Robots.txt: This file tells to search engine crawlers which pages or files "
"they can or can't request from your site.<br/>"
msgstr ""
"Robot.txt: este archivo indica a los rastreadores de motores de búsqueda qué"
" páginas o archivos pueden o no solicitar de su sitio.<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate"
msgstr "Rotar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
msgid "Round Corners"
msgstr "Esquinas redondeadas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded"
msgstr "Redondeado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Rounded Miniatures"
msgstr "Miniaturas redondeadas"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__route_id
#: model:ir.model.fields,field_description:website.field_website_route__path
msgid "Route"
msgstr "Ruta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "SEO"
msgstr "SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "SEO Optimized"
msgstr "Optimizado para SEO"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr "Metadatos para SEO"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__is_seo_optimized
msgid "SEO optimized"
msgstr "Optimizado para SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Same as desktop"
msgstr "Igual que en la computadora de escritorio"

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Sample %s"
msgstr "Muestra %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Sample Icons"
msgstr "Iconos de muestra"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Satellite"
msgstr "Satélite"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Saturation"
msgstr "Saturación"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.js:0
#: code:addons/website/static/src/components/dialog/seo.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & Reload"
msgstr "Guardar y actualizar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & copy"
msgstr "Guardar y copiar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Save the block to use it elsewhere"
msgstr "Guardar el bloque para usarlo en otra parte."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Score"
msgstr "Puntuación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Screens"
msgstr "Pantallas"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__image_ids
msgid "Screenshots"
msgstr "Capturas de pantalla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll"
msgstr "Desplazamiento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Effect"
msgstr "Efecto de desplazamiento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_footer_scrolltop
msgid "Scroll To Top"
msgstr "Desplazar hacia arriba"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Top Button"
msgstr "Botón superior de desplazamiento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Zone"
msgstr "Zona de deslizamiento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll down button"
msgstr "Botón inferior de desplazamiento"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Scroll down to next section"
msgstr "Desplazarse hacia abajo a la siguiente sección"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippets
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search"
msgstr "Búsqueda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr "Menús de búsqueda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Search Redirect"
msgstr "Buscar redirección"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Search Results"
msgstr "Resultados de la búsqueda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Search Visitor"
msgstr "Buscar visitante"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid ""
"Search in the media dialogue when you need photos to illustrate your "
"website. Odoo's integration with Unsplash, featuring millions of royalty "
"free and high quality photos, makes it possible for you to get the perfect "
"picture, in just a few clicks."
msgstr ""
"Busque en el diálogo de medios cuando necesite fotos para ilustrar su sitio "
"web. La integración de Odoo con Unsplash, con millones de fotos de calidad "
"ylibres de derechos de autor, hace que sea posible obtener la imagen "
"perfecta en tan solo unos clics."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "Search on our website"
msgstr "Buscar en nuestro sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Search within"
msgstr "Buscar en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "Buscar…"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "Segunda característica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Second Menu"
msgstr "Segundo menú"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Second feature"
msgstr "Segunda característica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Second list of Features"
msgstr "Segunda lista de características"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary"
msgstr "Secundario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary Style"
msgstr "Estilo secundario"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Seconds"
msgstr "Segundos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Section Subtitle"
msgstr "Subtítulo de la sección"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Select an image for social share"
msgstr "Seleccione una imagen para compartir en redes sociales"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Select and delete blocks <br/>to remove some steps."
msgstr "Seleccione y elimine bloques <br/>para eliminar algunos pasos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr "Seleccione y elimine bloques para eliminar características."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Select one font on"
msgstr "Seleccione una fuente en"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Selection"
msgstr "Selección"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_shop
msgid "Sell more with an eCommerce"
msgstr "Venda más con Comercio electrónico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Send Email"
msgstr "Enviar correo electrónico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Send us a message"
msgstr "Envíenos un mensaje"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__seo_name
#: model:ir.model.fields,field_description:website.field_website_page__seo_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__seo_name
msgid "Seo name"
msgstr "Nombre SEO"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Separate email addresses with a comma."
msgstr "Separe las direcciones de correo electrónico con una coma."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Separated link"
msgstr "Enlace separado"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Separator"
msgstr "Separador"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__sequence
#: model:ir.model.fields,field_description:website.field_theme_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website__sequence
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__sequence
#: model:ir.model.fields,field_description:website.field_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website_page__priority
#: model:ir.model.fields,field_description:website.field_website_rewrite__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Serve font from Google servers"
msgstr "Almacenar la fuente en los servidores de Google"

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__action_server_id
msgid "Server Action"
msgstr "Acción de servidor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Service"
msgstr "Servicio"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_our_services
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.our_services
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Services"
msgstr "Servicios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Session &amp; Security<br/>(essential)"
msgstr "Sesión y seguridad<br/>(esencial)"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Settings"
msgstr "Ajustes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings of Website"
msgstr "Ajustes de Sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings on this page will apply to this website"
msgstr "La configuración de esta página se aplicará a este sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Shadow"
msgstr "Sombra"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Shadows"
msgstr "Sombras"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shake"
msgstr "Agitar"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Share"
msgstr "Compartir"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_elearning
msgid "Share knowledge publicly or for a fee"
msgstr "Comparta información de forma pública o establezca una tarifa"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_success_stories
msgid "Share your best case studies"
msgstr "Comparta sus mejores casos de estudio"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__shared_user_account
msgid "Shared Customer Accounts"
msgstr "Cuentas de cliente compartidas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Shared Link Auth"
msgstr "Autenticación de enlace compartido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Shoes"
msgstr "Zapatos"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_shop
msgid "Shop"
msgstr "Tienda"

#. module: website
#: model:ir.model.fields,help:website.field_website__auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr "¿Se debe redireccionar a los usuarios al idioma de su navegador?"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__customize_show
msgid "Show As Optional Inherit"
msgstr "Mostrar como herencia opcional"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Header"
msgstr "Mostrar encabezado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show Message"
msgstr "Mostrar mensaje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and hide countdown"
msgstr "Mostrar mensaje y ocultar contador"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and keep countdown"
msgstr "Mostrar mensaje y mantener contador"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Sign In"
msgstr "Mostrar inicio de sesión"

#. module: website
#: model:ir.actions.act_window,name:website.action_show_viewhierarchy
msgid "Show View Hierarchy"
msgstr "Mostrar Ver jerarquía"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Show in Top Menu"
msgstr "Mostrar en el menú superior"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Show inactive views"
msgstr "Mostrar vistas inactivas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Show on"
msgstr "Mostrar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show reCaptcha Policy"
msgstr "Mostrar la política de reCAPTCHA"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Show site map"
msgstr "Mostrar sitemap"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_popup/000.js:0
#, python-format
msgid "Show the cookies bar"
msgstr "Mostrar la barra de cookies"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/Hide on Desktop"
msgstr "Mostrar/ocultar en el escritorio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/Hide on Mobile"
msgstr "Mostrar/ocultar en celular"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Showcase"
msgstr "Exhibición"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sidebar"
msgstr "Barra lateral"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Sign in"
msgstr "Iniciar sesión"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__connected
msgid "Signed In"
msgstr "Inició sesión"

#. module: website
#: model:ir.ui.menu,name:website.menu_site
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Site"
msgstr "Sitio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Sitemap.xml: Help search engine crawlers to find out what pages are present "
"and which have recently changed, and to crawl your site accordingly. This "
"file is automatically generated by Odoo."
msgstr ""
"Sitemap.xml: Ayuda a los rastreadores de motores de búsqueda a descubrir qué"
" páginas están presentes y cuáles han cambiado recientemente, para así "
"rastrear su sitio adecuadamente. Odoo genera automáticamente este archivo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Size"
msgstr "Tamaño"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Skip and start from scratch"
msgstr "Saltar y empezar desde cero"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide"
msgstr "Deslizar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Down"
msgstr "Deslizar hacia abajo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide Hover"
msgstr "Deslizar al deslizar el ratón"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Left"
msgstr "Deslizar a la izquierda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Right"
msgstr "Deslizar a la derecha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Up"
msgstr "Deslizar hacia arriba"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideout Effect"
msgstr "Efecto de salida al desplazar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Slider Speed"
msgstr "Velocidad del interruptor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Slideshow"
msgstr "Presentación de diapositivas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slogan"
msgstr "Eslogan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Small"
msgstr "Pequeño"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Small Header"
msgstr "Encabezado pequeño"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid ""
"Small text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."
msgstr ""
"Texto pequeño. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Smartphones"
msgstr "Smartphones"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Social Media"
msgstr "Redes sociales"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_social_media_options
msgid "Social Networks"
msgstr "Redes sociales"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Social Preview"
msgstr "Vista previa social"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Solid"
msgstr "Sólido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Something else here"
msgstr "Algo más aquí"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Something went wrong."
msgstr "Algo salió mal."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Sound"
msgstr "Sonido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Spacing"
msgstr "Espaciado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""
"Ponentes de todo el mundo se unirán a nuestros expertos para dar pláticas "
"inspiradoras sobre varios temas. Manténgase al tanto de las últimas "
"tecnologías y tendencias en la gestión empresarial. "

#. module: website
#: model:ir.model.fields,field_description:website.field_website__specific_user_account
msgid "Specific User Account"
msgstr "Cuenta de usuario específica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Specify a search term."
msgstr "Especifique un término de búsqueda."

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr ""
"Especifique el campo que contendrá los datos de los campos meta y "
"personalizados del formulario."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Speed"
msgstr "Velocidad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Spread"
msgstr "Extender"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Spring collection has arrived !"
msgstr "¡La colección de primavera ya está aquí!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Square"
msgstr "Cuadrado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Squared Miniatures"
msgstr "Miniaturas cuadradas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Stacked"
msgstr "Apilado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Standard"
msgstr "Estándar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Start After"
msgstr "Iniciar después de"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Start Button"
msgstr "Botón de inicio"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#, python-format
msgid "Start Now"
msgstr "Empezar ahora"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Start now"
msgstr "Empezar ahora"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Empiece con el cliente – descubra lo que quiere y déselo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Start your journey"
msgstr "Empiece su travesía"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Starter"
msgstr "Arranque"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Status Colors"
msgstr "Colores de estado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Stay informed of our latest news and discover what will happen in the next "
"weeks."
msgstr ""
"Manténgase al día de todas nuestras novedades y descubra qué pasará en las "
"próximas semanas."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Steps"
msgstr "Pasos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Sticky"
msgstr "Pegajoso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Storage"
msgstr "Almacenamiento"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_stores_locator
msgid "Stores Locator"
msgstr "Ubicación de tiendas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Storytelling is powerful.<br/> It draws readers in and engages them."
msgstr ""
"Contar una historia es algo poderoso.<br/> Atrae a los lectores y los "
"involucra."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Straight arrow"
msgstr "Flecha recta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stretch to Equal Height"
msgstr "Estirar para igualar altura"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Striped"
msgstr "Rayado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Structure"
msgstr "Estructura"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Style"
msgstr "Estilo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Styling"
msgstr "Estilo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sub Menus"
msgstr "Submenús"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Subject"
msgstr "Asunto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid "Submit"
msgstr "Enviar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Submit sitemap to Google"
msgstr "Enviar mapa de sitio a Google"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Success"
msgstr "Éxito"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_success_stories
msgid "Success Stories"
msgstr "Historias de éxito"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Suggestions"
msgstr "Sugerencias"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Surrounded"
msgstr "Rodeado"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCAPTCHA detectó actividad sospechosa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Switch Theme"
msgstr "Cambiar tema"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "System Fonts"
msgstr "Fuentes del sistema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "T-shirts"
msgstr "Playeras"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
#, python-format
msgid "TIP: Once loaded, follow the"
msgstr "CONSEJO: Una vez cargado, siga el"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/translate_website.xml:0
#, python-format
msgid "TRANSLATE"
msgstr "TRADUCIR"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Table of Content"
msgstr "Índice"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Tablets"
msgstr "Tabletas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Tabs"
msgstr "Pestañas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs color"
msgstr "Color de las pestañas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Tada"
msgstr "Tada"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__target
msgid "Target"
msgstr "Objetivo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Team"
msgstr "Equipo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "Nombre técnico:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Telephone"
msgstr "Teléfono"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Televisions"
msgstr "Televisiones"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr "Mencione el valor de esta característica para el <br/>cliente."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Template"
msgstr "Plantilla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Template fallback"
msgstr "Alternativa a la plantilla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Terms of Services"
msgstr "Términos de servicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Terms of service"
msgstr "Términos de servicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Terrain"
msgstr "Terreno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid "Test your robots.txt with Google Search Console"
msgstr "Pruebe sus Robots.txt con la consola de búsqueda de Google"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Text"
msgstr "Texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Text - Image"
msgstr "Texto - Imagen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Alignment"
msgstr "Alineación del texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Color"
msgstr "Color del texto"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Text Highlight"
msgstr "Resaltar texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Text Image Text"
msgstr "Texto - Imagen - Texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Inline"
msgstr "Texto en línea"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Text Position"
msgstr "Posición del texto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Text muted. Lorem <b>ipsum dolor sit amet</b>, consectetur."
msgstr "Texto silenciado. Lorem <b>ipsum dolor sit amet</b>, consectetur."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Thank You For Your Feedback"
msgstr "Gracias por sus comentarios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "Thank You!"
msgstr "¡Gracias!"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "The chosen name already exists"
msgstr "El nombre que eligió ya existe"

#. module: website
#. odoo-python
#: code:addons/website/models/res_company.py:0
#, python-format
msgid ""
"The company %(company_name)r cannot be archived because it has a linked website %(website_name)r.\n"
"Change that website's company first."
msgstr ""
"La empresa %(company_name)r no se puede archivar ya que el sitio web %(website_name)r está vinculado a ella.\n"
"Primero cambie la empresa de ese sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "The company this website belongs to"
msgstr "La empresa a la que pertenece este sitio web"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid ""
"The current text selection cannot be animated. Try clearing the format and "
"try again."
msgstr ""
"No se puede animar la selección de texto actual. Intente vaciar el formato y"
" vuelva a intentarlo."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#, python-format
msgid ""
"The description will be generated by search engines based on page content "
"unless you specify one."
msgstr ""
"La descripción se generará por los motores de búsqueda en función del "
"contenido de la página a menos que especifique uno."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#, python-format
msgid ""
"The description will be generated by social media based on page content "
"unless you specify one."
msgstr ""
"La descripción se generará por las redes sociales en función del contenido "
"de la página a menos que especifique una."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form.xml:0
#, python-format
msgid "The form has been sent successfully."
msgstr "Se envió el formulario con éxito."

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "The form's specified model does not exist"
msgstr "El modelo especificado de formulario no existe"

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_url
#: model:ir.model.fields,help:website.field_res_users__website_url
#: model:ir.model.fields,help:website.field_website_page__website_url
#: model:ir.model.fields,help:website.field_website_published_mixin__website_url
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: website
#: model:ir.model.fields,help:website.field_base_automation__website_url
#: model:ir.model.fields,help:website.field_ir_actions_server__website_url
#: model:ir.model.fields,help:website.field_ir_cron__website_url
msgid "The full URL to access the server action through the website."
msgstr ""
"La URL completa para acceder a la acción de servidor a través del sitio web."

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#, python-format
msgid "The homepage URL should be relative and start with '/'."
msgstr "La URL de la página de inicio debe ser relativa e iniciar con '/'."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "The installation of an App is already in progress."
msgstr "La instalación de una aplicación ya está en curso."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "The language of the keyword and related keywords."
msgstr "El idioma de la palabra clave y las palabras clave relacionadas."

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__limit
msgid "The limit is the maximum number of records retrieved"
msgstr "El límite es el número máximo de registros recuperados"

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "The limit must be between 1 and 16."
msgstr "El límite debe estar entre 1 y 16."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "The message will be visible once the countdown ends"
msgstr "El mensaje será visible cuando el contador termine"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "The selected templates will be reset to their factory settings."
msgstr ""
"Las plantillas seleccionadas se restablecerán a sus ajustes predeterminados."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "The team"
msgstr "El equipo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "The title will take a default value unless you specify one."
msgstr "El título tomará un valor predeterminado a menos que especifique uno."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"The website will not work properly if you reject or discard those cookies."
msgstr ""
"El sitio web no funcionará de forma apropiada si rechaza o descarta estas "
"cookies."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "The website will still work if you reject or discard those cookies."
msgstr "El sitio web aún funcionará si rechaza o descarta estas cookies."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: model:ir.model.fields,field_description:website.field_website__theme_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
#, python-format
msgid "Theme"
msgstr "Tema"

#. module: website
#: model:ir.model,name:website.model_theme_ir_asset
msgid "Theme Asset"
msgstr "Activo del tema"

#. module: website
#: model:ir.model,name:website.model_theme_ir_attachment
msgid "Theme Attachments"
msgstr "Archivos adjuntos del tema"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#, python-format
msgid "Theme Colors"
msgstr "Colores del tema"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Theme Options"
msgstr "Opciones del tema"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_menu__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_page__theme_template_id
msgid "Theme Template"
msgstr "Plantilla del tema"

#. module: website
#: model:ir.model,name:website.model_theme_ir_ui_view
msgid "Theme UI View"
msgstr "Vista de interfaz del tema"

#. module: website
#: model:ir.model,name:website.model_theme_utils
msgid "Theme Utils"
msgstr "Herramientas del tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "There are currently no pages for this website."
msgstr "Actualmente no hay páginas para este sitio web."

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "There are no contact and/or no email linked to this visitor."
msgstr ""
"No hay contacto o dirección de correo electrónico vinculado a este "
"visitante."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "There is no field available for this option."
msgstr "No hay ningún campo disponible para esta opción."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"Estos términos de servicio (\"Términos\", \"Acuerdo\") son un acuerdo entre "
"el sitio web (\"Operador del sitio web\", \"nosotros\", \"nosotros\" o "
"\"nuestro\") y usted (\"Usuario\", \"usted\" o \"su \"). Este acuerdo "
"establece los términos y condiciones generales de su uso de este sitio web y"
" cualquiera de sus productos o servicios (colectivamente, \"Sitio web\" o "
"\"Servicios\")."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "They trust us since years"
msgstr "Confían en nosotros desde hace años"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thick"
msgstr "Grueso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thin"
msgstr "Delgado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr "Tercera característica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Third Menu"
msgstr "Tercer menú"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "This URL is contained in the “%(field)s” of the following “%(model)s”"
msgstr "Esta URL está contenida en %(field)s de %(model)s"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__favicon
#: model:ir.model.fields,help:website.field_website__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr ""
"Este campo contiene la imagen que se utilizará como favicon en su sitio web."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_base
msgid "This field is the same as `arch` field without translations"
msgstr "Este campo es el mismo que el campo `arch` sin traducciones"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr ""
"Este campo se utiliza para establecer/obtener las ubicaciones para el "
"usuario"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch
msgid ""
"This field should be used when accessing view arch. It will use translation.\n"
"                               Note that it will read `arch_db` or `arch_fs` if in dev-xml mode."
msgstr ""
"Este campo debe usarse al acceder a la arquitectura de la vista y utilizará la traducción.\n"
"                               Tenga en cuenta que leerá `arch_db` o `arch_fs` si está en modo dev-xml."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_db
msgid "This field stores the view arch."
msgstr "Este campo almacena la arquitectura de la vista."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_prev
msgid ""
"This field will save the current `arch_db` before writing on it.\n"
"                                                                         Useful to (soft) reset a broken view."
msgstr ""
"Este campo almacenará la `arch_db` actual antes de escribir en ella.\n"
"                                                                         Es útil para restablecer (de forma parcial) una vista con errores."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"This font already exists, you can only add it as a local font to replace the"
" server version."
msgstr ""
"Esta fuente ya existe, solo puede agregarla como una fuente local para "
"reemplazar la versión del servidor. "

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "This font is hosted and served to your visitors by Google servers"
msgstr ""
"Esta fuente se aloja y se envía a sus visitantes mediante los servidores de "
"Google"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "This is a \""
msgstr "Esto es un \""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr ""
"Se trata de una sencilla unidad de héroe, un componente simple de estilo "
"jumbotrón para llamar la atención sobre el contenido o la información "
"destacados."

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "This message has been posted on your website!"
msgstr "Se publicó este mensaje en su sitio web."

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "This operator is not supported"
msgstr "Este operador no es compatible"

#. module: website
#: model:ir.ui.menu,name:website.menu_current_page
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "This page"
msgstr "Esta página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are editor of this "
"site."
msgstr ""
"Esta página no existe, pero puede crearla ya que es editor de este sitio."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "This translation is not editable."
msgstr "No se puede editar esta traducción."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#, python-format
msgid ""
"This value will be escaped to be compliant with all major browsers and used "
"in url. Keep it empty to use the default name of the record."
msgstr ""
"Este valor se escapará para que sea compatible con la mayoría de navegadores"
" y se utilizará en la URL. Déjelo vacío para usar el nombre predeterminado "
"del registro."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "This view arch has been modified"
msgstr "Se modificó esta arquitectura de vista"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/website_switcher.js:0
#, python-format
msgid "This website does not have a domain configured."
msgstr "Este sitio web no tiene un dominio configurado."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/website_switcher.js:0
#, python-format
msgid ""
"This website does not have a domain configured. To avoid unexpected behaviours during website edition, we recommend closing (or refreshing) other browser tabs.\n"
"To remove this message please set a domain in your website settings"
msgstr ""
"Este sitio web no tiene un dominio configurado. Para evitar comportamientos inesperados durante la edición del sitio web, recomendamos cerrar (o actualizar) otras pestañas del navegador.\n"
"Para eliminar este mensaje, establezca un dominio en los ajustes de su sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr "Miniaturas"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__time_since_last_action
msgid "Time since last page view. E.g.: 2 minutes ago"
msgstr ""
"Tiempo desde la última visita a la página. Por ejemplo: hace 2 minutos"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Time's up! You can now visit"
msgstr "¡Se acabó el tiempo! Ahora puede visitar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Timeline"
msgstr "Línea de tiempo"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__timezone
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Timezone"
msgstr "Zona horaria"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_4
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid "Tip: Add shapes to energize your Website"
msgstr "Consejo: agregue formas para energizar su sitio web"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_0
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid "Tip: Engage with visitors to convert them into leads"
msgstr "Consejo: interactúe con sus visitantes para convertirlos en leads"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_2
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid "Tip: Search Engine Optimization (SEO)"
msgstr "Consejo: optimización para motores de búsqueda (SEO)"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_3
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid "Tip: Use illustrations to spice up your website"
msgstr "Consejo: utilice ilustraciones para darle vida a su sitio web"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_1
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid "Tip: Use royalty-free photos"
msgstr "Consejo: utilice fotos sin derechos de autor"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Title"
msgstr "Título"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Title Position"
msgstr "Posición del título"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Para agregar una cuarta columna, reduzca el tamaño de estas tres columnas "
"con el icono correspondiente de cada bloque y luego duplique una para crear "
"una nueva como copia."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "To be successful your content needs to be useful to your readers."
msgstr "Para tener éxito, su contenido debe ser útil para sus lectores."

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid ""
"To get more visitors, you should target keywords that are often searched in "
"Google. With the built-in SEO tool, once you define a few keywords, Odoo "
"will recommend you the best keywords to target. Then adapt your title and "
"description accordingly to boost your traffic."
msgstr ""
"Para obtener más visitantes, debe utilizar palabras claves que sean buscadas"
" con frecuencia en Google. Con la herramienta de SEO integrada, Odoo le "
"recomendará las mejores palabras clave una vez que defina algunas. Luego "
"adapte su título y descripción de acuerdo a ellas para impulsar su tráfico."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Para enviar invitaciones en modo B2B, abra un contacto o seleccione varios "
"en la vista de lista y haga clic en la opción 'Gestión de acceso al Portal'"
"  del menú desplegable *Acción*."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Toggle"
msgstr "Alternar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "Toggle navigation"
msgstr "Alternar navegación"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Tooltip"
msgstr "Información sobre herramientas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Top"
msgstr "Arriba"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#, python-format
msgid "Top Menu for Website %s"
msgstr "Menú superior para el sitio web %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Top to Bottom"
msgstr "De arriba hacia abajo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Tops"
msgstr "Blusas"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__page_count
msgid "Total number of tracked page visited"
msgstr "Número total de páginas rastreadas visitadas"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visitor_page_count
msgid "Total number of visits on tracked pages"
msgstr "Número total de visitas en páginas rastreadas"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__track
#: model:ir.model.fields,field_description:website.field_website_page__track
msgid "Track"
msgstr "Rastrear"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits using Google Analytics"
msgstr "Lleve el seguimiento de visitas con Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Tracked"
msgstr "Rastreado"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Transition"
msgstr "Transición"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "Translate Attribute"
msgstr "Traducir atributo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "Translate Selection Option"
msgstr "Traducir la opción de selección"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "Translate header in the text. Menu is generated automatically."
msgstr ""
"Traducir el encabezado del texto. El menú se genera de forma automática."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
#, python-format
msgid "Translated content"
msgstr "Contenido traducido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Trigger"
msgstr "Activar"

#. module: website
#: model:ir.model.fields,help:website.field_website__configurator_done
msgid "True if configurator has been completed or ignored"
msgstr "Es True si se completó o ignoró el configurador"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Tuna and Salmon Burger"
msgstr "Hamburguesa de atún y salmón"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr "Convierta cada característica en un beneficio para su lector."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Twitter"
msgstr "Twitter"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_twitter
msgid "Twitter Account"
msgstr "Cuenta de Twitter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Twitter Scroller"
msgstr "Panel de Twitter"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/fields.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__type
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Type"
msgstr "Tipo"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Type '"
msgstr "Escriba \""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""
"Escriba '<i class=\"confirm_word\">sí</i>' a continuación si desea "
"confirmar."

#. module: website
#: model:ir.model.fields,help:website.field_website_rewrite__redirect_type
msgid ""
"Type of redirect/Rewrite:\n"
"\n"
"        301 Moved permanently: The browser will keep in cache the new url.\n"
"        302 Moved temporarily: The browser will not keep in cache the new url and ask again the next time the new url.\n"
"        404 Not Found: If you want remove a specific page/controller (e.g. Ecommerce is installed, but you don't want /shop on a specific website)\n"
"        308 Redirect / Rewrite: If you want rename a controller with a new url. (Eg: /shop -> /garden - Both url will be accessible but /shop will automatically be redirected to /garden)\n"
"    "
msgstr ""
"Tipo de redireccionamiento/reescritura:\n"
"\n"
"        301 Movido permanentemente: el navegador mantendrá el nuevo URL en el caché.\n"
"        302 Movido temporalmente: el navegador no mantendrá el nuevo URL en el caché y volverá a preguntar por el nuevo URL la próxima vez.\n"
"        404 no encontrado: si desea eliminar una página/controlador específico (por ejemplo, el comercio electrónico está instalado, pero no desea /tienda en un sitio web específico)\n"
"        308 Redirigir/Reescribir: si desea cambiar el nombre de un controlador con un URL nuevo. (Por ejemplo: /tienda -> /jardín - Ambos URL serán accesibles pero /tienda será redirigido automáticamente a /jardín)\n"
"    "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "URL"
msgstr "URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_from
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "URL from"
msgstr "URL de"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,help:website.field_website__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr ""
"La URL que coincida con esos filtros se reescribirá con la URL base CDN"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_to
msgid "URL to"
msgstr "URL a"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "UTM Campaign"
msgstr "Campaña de UTM"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "UTM Medium"
msgstr "Medio UTM"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "UTM Source"
msgstr "Fuente UTM"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Unalterable unique identifier"
msgstr "Identificador único inalterable"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Underline On Hover"
msgstr "Subrayar al pasar el cursor por encima"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Understand how visitors engage with our website, via Google Analytics.\n"
"                                                Learn more about"
msgstr ""
"Entienda cómo los visitantes interactúan con nuestro sitio web a través de Google Analytics.\n"
"                                                Leer más"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr "Un poderoso CRM ilimitado y soporte"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr "Personalización ilimitada"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.js:0
#, python-format
msgid "Unpublish"
msgstr "Anular publicación"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/redirect_field.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/systray_items/publish.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#, python-format
msgid "Unpublished"
msgstr "Sin publicar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Unregistered"
msgstr "Sin registrar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Update theme"
msgstr "Actualizar tema"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Upload"
msgstr "Subir"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Uploaded file is too large."
msgstr "El archivo subido es muy grande."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__url
#: model:ir.model.fields,field_description:website.field_theme_website_menu__url
#: model:ir.model.fields,field_description:website.field_theme_website_page__url
#: model:ir.model.fields,field_description:website.field_website_menu__url
#: model:ir.model.fields,field_description:website.field_website_track__url
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Url"
msgstr "URL"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__country_flag
msgid "Url of static flag image"
msgstr "URL de la imagen estática de la bandera"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Url or Email"
msgstr "URL o correo electrónico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Urls & Pages"
msgstr "URLs y páginas"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Use Google Map on your website (Contact Us page, snippets, etc)."
msgstr ""
"Utilice Google Maps en su sitio web (página de Contáctenos, snippets, etc)."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__use_main_menu_as_parent
msgid "Use Main Menu As Parent"
msgstr "Utilizar el menú principal como padre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Use Plausible.io, Simple and privacy-friendly Google Analytics alternative"
msgstr ""
"Utilice Plausible.io, es una alternativa a Google Analytics fácil de usar y "
"con buena privacidad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr ""
"Utilice una CDN para optimizar la disponibilidad del contenido de su sitio "
"web"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_default_share_image
msgid "Use a image by default for sharing"
msgstr "Utilice una imagen predeterminada al compartir"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Use as Homepage"
msgstr "Usar como página de inicio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Use of Cookies"
msgstr "Uso de cookies"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""
"Use este componente para crear una lista de elementos destacados en los "
"cuales desea centrar la atención."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""
"Utilice este snippet para crear varios tipos de componentes que incluyan una"
" imagen alineada a la izquierda o a la derecha y texto. Duplique los "
"elementos para crear una lista que se adapte a sus necesidades."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Use this snippet to presents your content in a slideshow-like format. Don't "
"write about products or services here, write about solutions."
msgstr ""
"Utilice este snippet para presentar su contenido en un formato tipo "
"presentación de diapositivas. No escriba sobre productos o servicios aquí, "
"escriba sobre soluciones."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Use this theme"
msgstr "Utilizar este tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"Use this timeline as a part of your resume, to show your visitors what "
"you've done in the past."
msgstr ""
"Utilice esta línea de tiempo como parte de su currículum, para mostrar a los"
" visitantes lo que ha hecho en el pasado."

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr "Se utilizó en el registro de FormBuilder"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Used in page content"
msgstr "Se utiliza en el contenido de la página"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Used in page description"
msgstr "Se utiliza en la descripción de la página"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Used in page first level heading"
msgstr "Se utiliza en el título de primer nivel de página"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Used in page second level heading"
msgstr "Se utiliza en el título de segundo nivel de página"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Used in page title"
msgstr "Se utiliza en el título de la página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to collect information about your interactions with the website, the pages you've seen,\n"
"                                                and any specific marketing campaign that brought you to the website."
msgstr ""
"Se utiliza para recopilar información sobre sus interacciones con el sitio web, las páginas que ha visto\n"
"                                                y la campaña de marketing que le haya traído al sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to make advertising more engaging to users and more valuable to publishers and advertisers,\n"
"                                                such as providing more relevant ads when you visit other websites that display ads or to improve reporting on ad campaign performance."
msgstr ""
"Se utiliza para que la publicidad sea más atractiva para los usuarios y más valiosa para los editores y anunciantes,\n"
"                                                tales como proporcionar publicidad más relevante cuando visita otros sitios web que muestren publicidad o mejorar los reportes de rendimiento de campañas."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Useful Links"
msgstr "Enlaces útiles"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Useful options"
msgstr "Opciones útiles"

#. module: website
#: model:ir.model,name:website.model_res_users
msgid "User"
msgstr "Usuario"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Users"
msgstr "Usuarios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Utilities &amp; Typography"
msgstr "Utilidades y tipografía"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Value"
msgstr "Valor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vert. Alignment"
msgstr "Alineación vert."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical"
msgstr "Vertical"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical Alignment"
msgstr "Alineación vertical"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Video"
msgstr "Video"

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
#: model:ir.model.fields,field_description:website.field_theme_website_page__view_id
#: model:ir.model.fields,field_description:website.field_website_page__view_id
msgid "View"
msgstr "Ver"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch
msgid "View Architecture"
msgstr "Arquitectura de vista"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__name
msgid "View Name"
msgstr "Ver nombre"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__type
msgid "View Type"
msgstr "Tipo de vista"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__mode
msgid "View inheritance mode"
msgstr "Ver modo de herencia"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__copy_ids
msgid "Views using a copy of me"
msgstr "Vistas que usan una copia de mí"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_children_ids
msgid "Views which inherit from this one"
msgstr "Vistas que heredan de esta"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility
#: model:ir.model.fields,field_description:website.field_website_page__visibility
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visibility"
msgstr "Visibilidad"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Visibility Password"
msgstr "Visibilidad de la contraseña"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password_display
msgid "Visibility Password Display"
msgstr "Pantalla de visibilidad de contraseña"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Visible for"
msgstr "Visible para"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Everyone"
msgstr "Visible para todos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged In"
msgstr "Visible para usuarios con sesión iniciada"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged Out"
msgstr "Visible para todos los usuarios"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_published
#: model:ir.model.fields,field_description:website.field_res_users__website_published
#: model:ir.model.fields,field_description:website.field_website_page__website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_published
msgid "Visible on current website"
msgstr "Visible en el sitio web actual"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Visible only if"
msgstr "Visible solo si"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visit_datetime
msgid "Visit Date"
msgstr "Fecha de visita"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
#, python-format
msgid "Visit our Facebook page to know if you are one of the lucky winners."
msgstr ""
"Visite nuestra página de Facebook para saber si es uno de los afortunados "
"ganadores."

#. module: website
#: model:ir.model,name:website.model_website_track
#: model:ir.model.fields,field_description:website.field_website_visitor__page_ids
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visited Pages"
msgstr "Páginas visitadas"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__website_track_ids
msgid "Visited Pages History"
msgstr "Historial de páginas visitadas"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visitor_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visitor"
msgstr "Visitante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_graph
msgid "Visitor Page Views"
msgstr "Vistas de la página de visitante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_tree
msgid "Visitor Page Views History"
msgstr "Historial de vistas de la página de visitante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_graph
msgid "Visitor Views"
msgstr "Vistas del visitante"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_tree
msgid "Visitor Views History"
msgstr "Historial de vistas del visitante"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitors_action
#: model:ir.model.fields,field_description:website.field_res_partner__visitor_ids
#: model:ir.model.fields,field_description:website.field_res_users__visitor_ids
#: model:ir.ui.menu,name:website.website_visitor_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_graph
msgid "Visitors"
msgstr "Visitantes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visits"
msgstr "Visitas"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_view_action
msgid ""
"Wait for visitors to come to your website to see the pages they viewed."
msgstr ""
"Espere a que los visitantes visiten su sitio web para ver las páginas que "
"vieron."

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid ""
"Wait for visitors to come to your website to see their history and engage "
"with them."
msgstr ""
"Espere a que los visitantes entren a su sitio web para ver su historial e "
"interactúe con ellos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Warning"
msgstr "Advertencia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Watches"
msgstr "Relojes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products. We build great products to solve your business problems.\n"
"                            <br/><br/>Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""
"Somos un equipo de personas apasionadas cuyo objetivo es mejorar la vida de todos a través de productos revolucionarios. Creamos grandes productos para resolver sus problemas empresariales.\n"
"                            <br/><br/>Nuestros productos están diseñados para pequeñas y medianas empresas dispuestas a optimizar su rendimiento."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems. Our products are designed for small to medium size companies "
"willing to optimize their performance."
msgstr ""
"Somos un equipo de personas apasionadas cuyo objetivo es mejorar la vida de "
"todos a través de productos revolucionarios. Creamos grandes productos para "
"resolver sus problemas empresariales. Nuestros productos están diseñados "
"para pequeñas y medianas empresas dispuestas a optimizar su rendimiento."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid ""
"We are a team of passionate people whose goal is to improve everyone's "
"life.<br/>Our services are designed for small to medium size companies."
msgstr ""
"Somos un equipo de personas apasionadas cuyo objetivo es mejorar la vida de "
"todos.<br/>Nuestros servicios están diseñados para pequeñas y medianas "
"empresas."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "We are almost done!"
msgstr "¡Ya casi terminamos!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "We are in good company."
msgstr "Está en las mejores manos."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_facebook_page/options.js:0
#, python-format
msgid "We couldn't find the Facebook page"
msgstr "No pudimos encontrar la página de Facebook"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We do not currently support Do Not Track signals, as there is no industry "
"standard for compliance."
msgstr ""
"Actualmente no somos compatibles con señales de Do Not Track, ya que no "
"existe un estándar en el sector para el cumplimiento."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "We found these ones:"
msgstr "Encontramos esto:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_slogan_oe_structure_header_slogan_1
msgid "We help <b>you</b> grow your business"
msgstr "<b>Le ayudamos</b> a hacer crecer su empresa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We may not be able to provide the best service to you if you reject those "
"cookies, but the website will work."
msgstr ""
"Si rechaza las cookies es probable que no podamos proporcionarle el mejor "
"servicio, pero el sitio web funcionará."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "We offer tailor-made products according to your needs and your budget."
msgstr ""
"Ofrecemos productos hechos a la medida según sus necesidades y presupuesto."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid ""
"We use cookies to provide improved experience on this website. You can learn"
" more about our cookies and how we use them in our"
msgstr ""
"Utilizamos cookies para proporcionar una mejor experiencia en este sitio "
"web. Puede aprender más sobre nuestras cookies y su uso en"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid ""
"We use cookies to provide you a better user experience on this website."
msgstr ""
"Utilizamos cookies para proporcionarle una mejor experiencia de usuario en "
"este sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "We will get back to you shortly."
msgstr "Nos pondremos en contacto con usted lo antes posible."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "We'll set you up and running in"
msgstr "Le ayudaremos a iniciar el proceso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Web Visitors"
msgstr "Visitantes de la web"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/redirect_field.xml:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_asset__website_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_id
#: model:ir.model.fields,field_description:website.field_res_company__website_id
#: model:ir.model.fields,field_description:website.field_res_partner__website_id
#: model:ir.model.fields,field_description:website.field_res_users__website_id
#: model:ir.model.fields,field_description:website.field_website_menu__website_id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_page__website_id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_rewrite__website_id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_id
#: model:ir.model.fields,field_description:website.field_website_visitor__website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
#, python-format
msgid "Website"
msgstr "Sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_company_id
msgid "Website Company"
msgstr "Empresa del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__website_config_preselection
msgid "Website Config Preselection"
msgstr "Preselección de configuración del sitio web"

#. module: website
#: model:ir.actions.client,name:website.website_configurator
msgid "Website Configurator"
msgstr "Configurador de sitio web"

#. module: website
#: model:ir.model,name:website.model_website_configurator_feature
msgid "Website Configurator Feature"
msgstr "Función del configurador de sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_domain
#: model:ir.model.fields,field_description:website.field_website__domain
msgid "Website Domain"
msgstr "Dominio del sitio web"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_domain_unique
msgid "Website Domain should be unique."
msgstr "El dominio del sitio web debe ser único."

#. module: website
#: model:ir.model.fields,field_description:website.field_website__favicon
msgid "Website Favicon"
msgstr "Favicon del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr "Clave del formulario del sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.ir_model_view
msgid "Website Forms"
msgstr "Formularios del sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Info"
msgstr "Información del sitio web"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_logo
#: model:ir.model.fields,field_description:website.field_website__logo
#, python-format
msgid "Website Logo"
msgstr "Logo del sitio web"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "Menú del sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Website Menus Settings"
msgstr "Ajustes de menús del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_name
#: model:ir.model.fields,field_description:website.field_website__name
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Name"
msgstr "Nombre del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,field_description:website.field_website_page__first_page_id
msgid "Website Page"
msgstr "Página del sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr "Ajustes de página del sitio web"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Website Pages"
msgstr "Páginas web"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_automation__website_path
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_path
#: model:ir.model.fields,field_description:website.field_ir_cron__website_path
msgid "Website Path"
msgstr "Ruta del sitio web"

#. module: website
#: model:ir.actions.client,name:website.website_preview
msgid "Website Preview"
msgstr "Vista previa del sitio web"

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "Website Published Mixin"
msgstr "Mixin publicado del sitio web"

#. module: website
#: model:ir.model,name:website.model_website_searchable_mixin
msgid "Website Searchable Mixin"
msgstr "Mixin buscable sitio web"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form
#, python-format
msgid "Website Settings"
msgstr "Ajustes del sitio web"

#. module: website
#: model:ir.model,name:website.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtro de snippets del sitio web"

#. module: website
#: model:ir.model,name:website.model_theme_website_menu
msgid "Website Theme Menu"
msgstr "Menú del tema del sitio web"

#. module: website
#: model:ir.model,name:website.model_theme_website_page
msgid "Website Theme Page"
msgstr "Página del tema del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_url
#: model:ir.model.fields,field_description:website.field_res_users__website_url
#: model:ir.model.fields,field_description:website.field_website_page__website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_automation__website_url
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_url
#: model:ir.model.fields,field_description:website.field_ir_cron__website_url
msgid "Website Url"
msgstr "URL del sitio web"

#. module: website
#: model:ir.model,name:website.model_website_visitor
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Website Visitor"
msgstr "Visitante del sitio web"

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Website Visitor #%s"
msgstr "Visitante del sitio web #%s"

#. module: website
#: model:ir.actions.server,name:website.website_visitor_cron_ir_actions_server
#: model:ir.cron,cron_name:website.website_visitor_cron
msgid "Website Visitor : clean inactive visitors"
msgstr "Visitante del sitio web: eliminar visitantes inactivos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"Es posible que el sitio web use cookies para personalizar y facilitar al "
"máximo la navegación del usuario. El usuario puede configurar su navegador "
"para notificar y rechazar la instalación de las cookies que enviamos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "Menú del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_description
msgid "Website meta description"
msgstr "Descripción meta del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_keywords
msgid "Website meta keywords"
msgstr "Palabras clave meta del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_title
msgid "Website meta title"
msgstr "Título meta del sitio web"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imagen Open Graph del sitio"

#. module: website
#: model:ir.model,name:website.model_website_rewrite
msgid "Website rewrite"
msgstr "Reescritura del sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Website rewrite Settings"
msgstr "Ajustes de reescritura del sitio web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.action_website_rewrite_tree
msgid "Website rewrites"
msgstr "Reescrituras del sitio web"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_analytics
msgid "Website: Analytics"
msgstr "Sitio web: análisis"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
msgid "Website: Dashboard"
msgstr "Sitio web: tablero"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_list
#: model:ir.ui.menu,name:website.menu_website_websites_list
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "Sitios web"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install__website_ids
msgid "Websites to translate"
msgstr "Sitios web a traducir"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#, python-format
msgid "Welcome to your"
msgstr "Le damos la bienvenida a su"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "What you see is what you get"
msgstr "Lo que ve es lo que hay"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Width"
msgstr "Ancho"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__password
msgid "With Password"
msgstr "Con contraseña"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Women"
msgstr "Mujeres"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid ""
"Would you like to save before being redirected? Unsaved changes will be "
"discarded."
msgstr ""
"¿Le gustaría guardar antes de ser redireccionado? Se descartarán todos los "
"cambios no guardados."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""
"Escriba aquí una cita de uno de sus clientes. Las citas son una excelente "
"manera de generar confianza en sus productos o servicios."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Write one or two paragraphs describing your product or services."
msgstr "Escriba uno o dos párrafos que describan su producto o servicios."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Escriba uno o dos párrafos que describan sus productos o servicios. Para "
"tener éxito, su contenido debe ser útil para sus lectores."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"Escriba uno o dos párrafos describiendo su producto, servicio o función "
"especifica.<br/> Para tener éxito su contenido debe ser útil para sus "
"lectores."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr ""
"Escriba lo que desee que el cliente sepa, <br/>no lo que desea mostrar."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Year"
msgstr "Año"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.js:0
#, python-format
msgid ""
"You are about to be redirected to the domain configured for your website ( "
"%s ). This is necessary to edit or view your website from the Website app. "
"You might need to log back in."
msgstr ""
"Está a punto de ser redirigido al dominio configurado para su sitio web ( %s"
" ). Esto es necesario para editar o ver su sitio web desde la aplicación "
"Sitio web. Es posible que tenga que volver a iniciar sesión."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
#, python-format
msgid "You are about to enter the translation mode."
msgstr "Está a punto de entrar en el modo de traducción."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"You can choose to have your computer warn you each time a cookie is being sent, or you can choose to turn off all cookies.\n"
"                            Each browser is a little different, so look at your browser's Help menu to learn the correct way to modify your cookies."
msgstr ""
"Puede elegir que su computadora le advierta cada vez que se envía una cookie, o puede elegir desactivar todas las cookies.\n"
"                            Cada navegador es un poco diferente, así que eche un vistazo al menú de Ayuda de su navegador para aprender la forma correcta de modificar sus cookies."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr "Puede editar colores y fondos para resaltar características."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "You can edit, duplicate..."
msgstr "Puede editar, duplicar..."

#. module: website
#. odoo-python
#: code:addons/website/models/res_users.py:0
#: model:ir.model.constraint,message:website.constraint_res_users_login_key
#, python-format
msgid "You can not have two users with the same login!"
msgstr ""
"¡No puede tener dos usuarios con la misma información de inicio de sesión!"

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "You can only use template prefixed by dynamic_filter_template_ "
msgstr ""
"Solo puede utilizar plantillas con el prefijo dynamic_filter_template_"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate a model field."
msgstr "No se puede duplicar un campo del modelo."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate the submit button of the form."
msgstr "No se puede duplicar el botón de envío del formulario."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove a field that is required by the model itself."
msgstr ""
"No se puede eliminar un campo que sea necesario para el propio modelo."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove the submit button of the form"
msgstr "No se puede eliminar el botón de envío del formulario"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#, python-format
msgid ""
"You cannot delete default website %s. Try to change its settings instead"
msgstr ""
"No puede eliminar un sitio web predeterminado %s. En su lugar, intente "
"cambiar sus ajustes"

#. module: website
#. odoo-python
#: code:addons/website/models/website_menu.py:0
#, python-format
msgid ""
"You cannot delete this website menu as this serves as the default parent "
"menu for new websites (e.g., /shop, /event, ...)."
msgstr ""
"No puede eliminar este menú del sitio web, funciona como el menú principal "
"predeterminado para nuevos sitios web (por ejemplo, /tienda, /evento...)."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_facebook_page/options.js:0
#, python-format
msgid "You didn't provide a valid Facebook link"
msgstr "El enlace de Facebook que proporcionó no es válido"

#. module: website
#. odoo-python
#: code:addons/website/models/mixins.py:0
#, python-format
msgid "You do not have the rights to publish/unpublish"
msgstr "No tiene los derechos para publicar o anular publicaciones"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid ""
"You have hidden this page from search results. It won't be indexed by search"
" engines."
msgstr ""
"Ocultó esta página de los resultados de búsqueda. No será indexada por los "
"motores de búsqueda."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "You may opt-out of a third-party's use of cookies by visiting the"
msgstr ""
"Puede optar por no participar en el uso de cookies por parte de terceros al "
"visitar el"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#, python-format
msgid "You must keep at least one website."
msgstr "Debe mantener al menos un sitio web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"Debe revisar cuidadosamente las cláusulas legales y otras condiciones de uso"
" de las páginas web a las que acceda mediante enlaces de este Sitio web. Su "
"vinculación con otros portales o páginas web externas queda bajo su propio "
"riesgo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "You will get results from blog posts, products, etc"
msgstr "Obtendrá resultados de publicaciones de blog, productos, etc"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "You'll be able to create your pages later on."
msgstr "Podrá crear sus páginas más tarde."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Company"
msgstr "Su empresa "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid ""
"Your Dynamic Snippet will be displayed here... This message is displayed "
"because you did not provided both a filter and a template to use.<br/>"
msgstr ""
"Su snippet dinámico aparecerá aquí... Este mensaje aparece porque no "
"proporcionó ni el filtro ni la plantilla a usar.<br/>"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Email"
msgstr "Su correo electrónico"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_embed_code/000.js:0
#, python-format
msgid ""
"Your Embed Code snippet doesn't have anything to display. Click on Edit to "
"modify it."
msgstr ""
"El fragmento de código incrustado no tiene nada para mostrar, haga clic en "
"Editar para modificarlo."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Name"
msgstr "Su nombre"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Question"
msgstr "Su pregunta"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#, python-format
msgid "Your description looks too long."
msgstr "Su descripción es demasiado larga."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#, python-format
msgid "Your description looks too short."
msgstr "Su descripción es demasiado corta."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Your experience may be degraded if you discard those cookies, but the "
"website will still work."
msgstr ""
"Su experiencia puede verse afectada si descarta estas cookies, pero el sitio"
" web seguirá funcionando."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/editor/editor.js:0
#, python-format
msgid "Your modifications were saved to apply this option."
msgstr "Se guardaron sus modificaciones para aplicar esta opción."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "Your search '"
msgstr "Su búsqueda \""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Your title"
msgstr "Su título"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_youtube
msgid "Youtube Account"
msgstr "Cuenta de YouTube"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Zoom"
msgstr "Zoom"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In"
msgstr "Ampliar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom Out"
msgstr "Reducir"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"
msgstr ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"
msgstr ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "a blog"
msgstr "un blog"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "a business website"
msgstr "un sitio web empresarial"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "a new image"
msgstr "una nueva imagen"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "a pre-made Palette"
msgstr "una paleta creada previamente"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "an elearning platform"
msgstr "una plataforma de elearning"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "an event website"
msgstr "un sitio web de evento"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "an online store"
msgstr "una tienda en línea"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "and"
msgstr "y"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "and copy paste the address of the font page here."
msgstr "y copie y pegue aquí la dirección de página frontal."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "big"
msgstr "grande"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "breadcrumb"
msgstr "migas de pan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-outline-primary"
msgstr "btn-outline-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-outline-secondary"
msgstr "btn-outline-secondary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-primary"
msgstr "btn-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-secondary"
msgstr "btn-secondary"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "business"
msgstr "empresa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "celebration, launch"
msgstr "celebración, lanzamiento"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "chart, table, diagram, pie"
msgstr "gráfico, tabla, diagrama, círculo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "cite"
msgstr "citar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "columns, description"
msgstr "columnas, descripción"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "common answers, common questions"
msgstr "respuestas comunes, preguntas comunes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "content"
msgstr "contenido"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "customers, clients"
msgstr "clientes, clientes "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "days"
msgstr "días"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "develop the brand"
msgstr "impulsar la marca"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "e.g. /my-awesome-page"
msgstr "Por ejemplo, /mi-asombrosa-página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "e.g. De Brouckere, Brussels, Belgium"
msgstr "Por ejemplo, De Brouckere, Bruselas, Bélgica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "e.g. Home Page"
msgstr "Por ejemplo, página de incio"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_dashboard
msgid "eCommerce"
msgstr "Comercio electrónico"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_elearning
msgid "eLearning"
msgstr "eLearning"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "email"
msgstr "correo electrónico"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "evolution, growth"
msgstr "evolución, crecimiento"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "fonts.google.com"
msgstr "fonts.google.com"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "for my"
msgstr "para mi"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "found(s)"
msgstr "encontrado(s)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "found)"
msgstr "encontrado)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "free website"
msgstr "sitio web gratuito"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "from Logo"
msgstr "del logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "frontend_lang (Odoo)"
msgstr "frontend_lang (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "gallery, carousel"
msgstr "galería, carrusel"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "get leads"
msgstr "obtener leads"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "google1234567890123456.html"
msgstr "google1234567890123456.html"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "heading, h1"
msgstr "título, h1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "hero, jumbotron"
msgstr "héroe, jumbotrón"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "https://fonts.google.com/specimen/Roboto"
msgstr "https://fonts.google.com/specimen/Roboto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "https://www.odoo.com"
msgstr "https://www.odoo.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "iPhone"
msgstr "iPhone"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"
msgstr ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "image, media, illustration"
msgstr "imagen, medios, ilustración"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#, python-format
msgid "in the top right corner to start designing."
msgstr "en la esquina superior derecha para empezar a diseñar."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "inform customers"
msgstr "informar clientes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "instancia de Odoo, el"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "link"
msgstr "enlace"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "masonry, grid"
msgstr "cuadros, tabla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "menu, pricing"
msgstr "menú, precio"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "no value"
msgstr "sin valor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "o-color-"
msgstr "o-color-"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/edit_website.js:0
#, python-format
msgid "or edit master"
msgstr "o edite el maestro"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "organization, structure"
msgstr "organización, estructura"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "perfect website?"
msgstr "sitio web perfecto?"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: code:addons/website/static/src/components/fields/widget_iframe.xml:0
#, python-format
msgid "phone"
msgstr "teléfono"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "placeholder"
msgstr "marcador de posición"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
#, python-format
msgid "pointer to build the perfect page in 7 steps."
msgstr "indicador para crear la página perfecta en 7 pasos."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "pricing"
msgstr "precio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "promotion, characteristic, quality"
msgstr "promoción, características, calidad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "results"
msgstr "resultados"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "rows"
msgstr "filas"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "schedule appointments"
msgstr "programar citas"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "sell more"
msgstr "venda más"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "separator, divider"
msgstr "separador, divisor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "session_id (Odoo)<br/>"
msgstr "session_id (Odoo)<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "statistics, stats, KPI"
msgstr "estadísticas, KPI"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "testimonials"
msgstr "testimonios"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "text link"
msgstr "enlace del texto"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__theme_ir_ui_view
msgid "theme.ir.ui.view"
msgstr "theme.ir.ui.view"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "this page"
msgstr "esta página"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "valuation, rank"
msgstr "valoración, rango"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_id
#, python-format
msgid "website"
msgstr "sitio web"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "with the main objective to"
msgstr "con el objetivo principal de"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "yes"
msgstr "sí"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Active"
msgstr "⌙ Activo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "⌙ Delay"
msgstr "⌙ Retraso"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Inactive"
msgstr "⌙ Inactivo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "⌙ Separator"
msgstr "⌙ Separador"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "⌙ Style"
msgstr "⌙ Estilo"
