<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <!-- Providers -->

    <record id="payment_provider_company_rule" model="ir.rule">
        <field name="name">Access providers in own companies only</field>
        <field name="model_id" ref="payment.model_payment_provider"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <!-- Transactions -->

    <record id="payment_transaction_user_rule" model="ir.rule">
        <field name="name">Access own transactions only</field>
        <field name="model_id" ref="payment.model_payment_transaction"/>
        <field name="domain_force">['|', ('partner_id', '=', False), ('partner_id', '=', user.partner_id.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_user')), (4, ref('base.group_portal'))]"/>
    </record>

    <record id="transaction_company_rule" model="ir.rule">
        <field name="name">Access transactions in own companies only</field>
        <field name="model_id" ref="payment.model_payment_transaction"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <!-- Tokens -->

    <record id="payment_token_user_rule" model="ir.rule">
        <field name="name">Access only tokens belonging to commercial partner</field>
        <field name="model_id" ref="payment.model_payment_token"/>
        <field name="domain_force">[('partner_id', 'child_of', user.partner_id.commercial_partner_id.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_user')), (4, ref('base.group_portal')), (4, ref('base.group_public'))]"/>
    </record>

    <record id="payment_token_company_rule" model="ir.rule">
        <field name="name">Access tokens in own companies only</field>
        <field name="model_id" ref="payment.model_payment_token"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

</odoo>
