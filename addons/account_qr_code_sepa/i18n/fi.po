# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_qr_code_sepa
# 
# Translators:
# <PERSON><PERSON> <eino.maki<PERSON><PERSON>@netitbe.fi>, 2022
# <PERSON><PERSON> <ossi.manty<PERSON><PERSON>@obs-solutions.fi>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:49+0000\n"
"PO-Revision-Date: 2022-12-16 09:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_qr_code_sepa
#: model:ir.model,name:account_qr_code_sepa.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Pankkitilit"

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
#, python-format
msgid "SEPA Credit Transfer QR"
msgstr "SEPA-tilisiirto (QR-koodi)"

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
#, python-format
msgid ""
"The account receiving the payment must have an account holder name or "
"partner name set."
msgstr ""
"Vastaanottavalla tilillä tulee olla asetettuna tilin haltijan nimi tai "
"partnerin nimi."
