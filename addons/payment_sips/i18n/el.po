# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_sips
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:164
#, python-format
msgid "; multiple order found"
msgstr ", βρέθηκαν πολλαπλές παραγγελίες"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:162
#, python-format
msgid "; no order found"
msgstr ", δεν βρέθηκε παραγγελία"

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr "Adyen"

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:75
#, python-format
msgid "Currency not supported by Wordline"
msgstr "Το νόμισμα δεν υποστηρίζεται από την Wordline"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:56
#, python-format
msgid "Incorrect payment acquirer provider"
msgstr "Λανθασμένος πάροχος αποδέκτη πληρωμής"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_version
msgid "Interface Version"
msgstr "Έκδοση Διεπαφής"

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr "Μη αυτόματη διαμόρφωση"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Merchant ID"
msgstr "Αναγνωριστικό Εμπόρου"

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr "Ogone"

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Αποδέκτης Πληρωμής"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "Συναλλαγή Πληρωμής"

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr "Paypal"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_prod_url
msgid "Production url"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__provider
msgid "Provider"
msgstr "Πάροχος"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_secret
msgid "Secret Key"
msgstr "Κρυφό Κλειδί"

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr "Sips"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:160
#, python-format
msgid "Sips: received data for reference %s"
msgstr "Sips: λήφθηκαν δεδομένα αναφοράς %s"

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr "Stripe"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_test_url
msgid "Test url"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Used for production only"
msgstr "Χρησιμοποιείται μόνο για παραγωγή"

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr "Έμβασμα"
