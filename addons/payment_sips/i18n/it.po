# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON>, 2019\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; trovato ordine multiplo"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr "; nessun ordine trovato"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Currency not supported by Wordline"
msgstr "Valuta non gestita da Worldline"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Incorrect payment acquirer provider"
msgstr "Fornitore sistema di pagamento errato"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_version
msgid "Interface Version"
msgstr "Versione interfaccia"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Merchant ID"
msgstr "ID commerciante"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Sistema di pagamento"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transazione di pagamento"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_prod_url
msgid "Production url"
msgstr "URL di produzione"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__provider
msgid "Provider"
msgstr "Fornitore"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_secret
msgid "Secret Key"
msgstr "Chiave segreta"

#. module: payment_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_acquirer__provider__sips
msgid "Sips"
msgstr "Sips"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Sips: received data for reference %s"
msgstr "Sips: ricevuti dati con riferimento %s"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_test_url
msgid "Test url"
msgstr "URL di test"

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Used for production only"
msgstr "Usato solo in produzione"
