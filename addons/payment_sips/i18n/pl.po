# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Polish (https://www.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; znaleziono wielokrotne zamówienie"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr "; nie znaleziono zamówienia"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Currency not supported by Wordline"
msgstr "Waluta nieobsługiwana przez Wordline"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Incorrect payment acquirer provider"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_version
msgid "Interface Version"
msgstr "Wersja interfejsu"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Merchant ID"
msgstr "Merchant ID"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Beneficjent płatności"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcja płatności"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_prod_url
msgid "Production url"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__provider
msgid "Provider"
msgstr "Dostawca"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_secret
msgid "Secret Key"
msgstr "Sekretny klucz"

#. module: payment_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_acquirer__provider__sips
msgid "Sips"
msgstr "Sips"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:0
#, python-format
msgid "Sips: received data for reference %s"
msgstr "Sips: otrzymane dane dla odniesienia %s"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_test_url
msgid "Test url"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Used for production only"
msgstr "Tylko do produkcji"
