# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <PERSON>, 2022
# Wil <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 18:36+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Abe Manyo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Tidak ada milestone yang tersedia. Ayo buat milestone!\n"
"                </p><p>\n"
"                    Lacak proses poin yang harus diraih untuk mencapai kesuksesan.\n"
"                </p>\n"
"            "

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "%(name)s's Sales Order"
msgstr "Sales Order %(name)s"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "All items have been loaded"
msgstr "Semua item sudah dimuat"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Based on Delivered Quantity (Manual)"
msgstr "Berdasarkan Kuantitas Terkirim (Manual)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Based on Milestones"
msgstr "Berdasarkan Milestone"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_project__allow_billable
msgid "Billable"
msgstr "Billable"

#. module: sale_project
#: model:ir.model,name:sale_project.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Create Invoice"
msgstr "Buat Faktur"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Buat pada ORder"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__project_partner_id
msgid "Customer"
msgstr "Pelanggan"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Delivered"
msgstr "Sudah Terkirim"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_name
msgid "Description"
msgstr "Deskripsi"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__display_sale_order_button
msgid "Display Sales Order"
msgstr "Tampilkan Sales Order"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr "Tampilkan project"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr "Project yang Dibuat"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr "Task yang Dibuat"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr "Memiliki SO untuk Difaktur"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_with_nothing_to_invoice
msgid "Has a SO with an invoice status of No"
msgstr "Memiliki SO dengan status faktur Tidak"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__invoice_count
msgid "Invoice Count"
msgstr "Jumlah Faktur"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr "Faktur memesan kuantitas langsung setelah layanan dijual."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create a project"
" for the order with a task for each sales order line to track the time "
"spent."
msgstr ""
"Faktur memesan kuantitas langsung setelah layanan dijual. Buat project untuk"
" order dengan task untuk setiap baris sales order untuk melacak waktu yang "
"digunakan."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create a task in"
" an existing project to track the time spent."
msgstr ""
"Faktur memesan kuantitas langsung setelah layanan dijual. BUat task dalam "
"project yang tersedia untuk melacak waktu yang digunakan."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create an empty "
"project for the order to track the time spent."
msgstr ""
"Faktur memesan kuantitas langsung setelah layanan dijual. Buat project "
"kosong untuk order untuk melacak waktu yang digunakan."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). "
msgstr ""
"Faktur layanan ini saat dikirim (tetapkan kuantitas secara manual pada baris"
" sales order Anda)."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create a project for the order with a task for each "
"sales order line to track the time spent."
msgstr ""
"Faktur layanan ini saat dikirim (tetapkan kuantitas secara manual pada baris"
" sales order Anda). Buat project untuk order dengan task untuk setiap baris "
"sales order untuk melacak waktu yang digunakan"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create a task in an existing project to track the time "
"spent."
msgstr ""
"Faktur layanan ini saat dikirim (tetapkan kuantitas secara manual pada baris"
" sales order Anda). Buat task pada project yang tersedia saat ini untuk "
"melacak waktu yang digunakan."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create an empty project for the order to track the time "
"spent."
msgstr ""
"Faktur layanan ini saat dikirim (tetapkan kuantitas secara manual pada baris"
" sales order). Buat project kosong untuk order untuk melacak waktu yang "
"digunakan."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice your milestones when they are reached."
msgstr "Faktur milestone Anda saat teraih."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create a project for the "
"order with a task for each sales order line to track the time spent."
msgstr ""
"Faktur milestone Anda saat teraih. Buat project untuk order dengan task "
"untuk setiap baris sales order untuk melacak waktu yang digunakan."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create a task in an existing "
"project to track the time spent."
msgstr ""
"Faktur milestone Anda saat mereka tercapai. Buat task pada project yang "
"tersedia untuk melacak waktu yang digunakan."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create an empty project for "
"the order to track the time spent."
msgstr ""
"Faktur milestone Anda saat teraih. Buat project kosong untuk order untuk "
"melacak waktu yang digunakan."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Invoice your time and material to customers"
msgstr "Faktur waktu dan materi Anda untuk pelanggan"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Invoiced"
msgstr "Telah Difakturkan"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Invoices"
msgstr "Faktur"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Invoicing Policy"
msgstr "Kebijakan Penagihan"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__is_product_milestone
msgid "Is Product Milestone"
msgstr "Adalah Milestone Produ"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__is_service
msgid "Is a Service"
msgstr "Apakah adalah Layanan"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move_line
msgid "Journal Item"
msgstr "Item Jurnal"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Load more"
msgstr "Muat lebih"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_type
#: model:ir.model.fields,help:sale_project.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Stel manual jumlah pada order: Fakturkan berdasarkan input jumlah manual tanpa membuat akun analitik.\n"
"Absensi pada kontrak: Fakturkan berdasarkan jumlah jam kehadiran pada absensi.\n"
"Buat tugas dan telusuri jumlah jam: Buat tugas pada validasi order penjualan dan telusuri jam kerja."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Materials"
msgstr "Material"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Metode utnuk mengupdate kuantitas yang dikirim"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__milestone_count
msgid "Milestone Count"
msgstr "Jumlah Milestone"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_project.selection__sale_order_line__qty_delivered_method__milestones
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Milestones"
msgstr "Milestone"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "New"
msgstr "Baru"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "No Sales Order"
msgstr "Tidak ada Sales Order"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "No Sales Order Item"
msgstr "Tidak ada Item Sales Order"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "Non-billable"
msgstr "Non-billable"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "Tidak ada"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr "Jumlah Project"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""
"Pada konfirmasi sales order, produk ini dapat membuat project dan/atau task.         Dari situ, Anda dapat melacak layanan yang Anda tawarkan.\n"
"         'Di project sale order': Akan menggunakan project sale order yang dikonfigurasi atau membuat         project baru berdasarkan templat yang dipilih."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operation not supported"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Other Revenues"
msgstr "Pendapatan Lain"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Other Services"
msgstr "Layanan Lain"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__quantity_percentage
msgid ""
"Percentage of the ordered quantity that will automatically be delivered once"
" the milestone is reached."
msgstr ""
"Persentase kuantitas yang dipesan yang akan secara otomatis dikirimkan "
"setelah milestone diraih."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Portal Sale Order"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Prepaid/Fixed Price"
msgstr "Harga Prabayar/Tetap"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product"
msgstr "Produk"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product Variant"
msgstr "Varian Produk"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "Project"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "Project & Task"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_milestone
msgid "Project Milestone"
msgstr "Milestone Project"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_type__milestones
msgid "Project Milestones"
msgstr "Milestone Project"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "Templat Project"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "Sale Order project"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Projects"
msgstr "Project"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "Project yang digunakan di sales order ini"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__quantity_percentage
msgid "Quantity"
msgstr "Kuantitas"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Baris Templat Quotation"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__reached_milestones_ids
msgid "Reached Milestones"
msgstr "Milestone Teraih"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
msgid "Sale Order"
msgstr "Order Penjualan"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_count
msgid "Sale Order Count"
msgstr "Jumlah Sale Order"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sales"
msgstr "Penjualan"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Sales & Invoicing"
msgstr "Sales & Pemfakturan"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order"
msgstr "Order Penjualan"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
msgid "Sales Order Id"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_task_project_user_search_inherited
#, python-format
msgid "Sales Order Item"
msgstr "Item Sales Order"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__sale_line_id
msgid "Sales Order Item that will be updated once the milestone is reached."
msgstr "Item Sales Order yang akan diupdate setelah milestone teraih."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added in order to be invoiced to your customer.\n"
"By default the sales order item set on the project will be selected. In the absence of one, the last prepaid sales order item that has time remaining will be used.\n"
"Remove the sales order item in order to make this task non billable. You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"Item Sales Order yang mana waktu telah dihabiskan pada task ini akan ditambahkan agar bisa difakturkan ke pelanggan Anda.\n"
"Secara default item sales order yang ditetapkan pada project akan dipilih. Apabila tidak ada, item sales order prabayar terakhir yang memiliki waktu tersisa akan digunakan.\n"
"Hapus item sales order untuk membuat task ini menjadi non-billable. Anda dapat juga mengganti atau menghapus item sales order untuk setiap entri timesheet secara individu."

#. module: sale_project
#. odoo-javascript
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sales Order Items"
msgstr "Item Sales Order"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "Detail Order Penjualan"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Sales Orders"
msgstr "Order Penjualan"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"Item sales order yang akan secara default dipilih pada task dan timesheet project ini, kecuali bila karyawan menetapkan pada timesheet secara eksplisit di-link ke item sales orde rlain pada project.\n"
"Dapat dimodifikasi pada setiap task dan entri timesheet secara individu bila diperlukan.  "

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "Sales order untuk mana project ini di-link."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Sales order untuk mana task di-link."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "Cari di Faktur"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "Cari di Sales Order"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr "Pilih project non billable pada mana task ini dapat dibuat."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "Kebijakan Pemfakturan Layanan"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sold"
msgstr "Dijual"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "Kegiatan"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "Task Created (%s): %s"
msgstr "Task Dibuat (%s): %s"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Pengulangan Task"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "Kegiatan"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Kegiatan analisis"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr "Task terkait sale ini"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr ""
"Produk %s seharusnya tidak memiliki project global karena akan membuat "
"project sendiri."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""
"Produk %s seharusnya tidak memiliki project atau templat project karena akan"
" membuatnya sendiri."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""
"Produk %s seharusnya tidak memiliki templat project karena akan membuat task"
" di project global."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "This task has been created from: %s (%s)"
msgstr "Task telah dibuat dari: %s (%s)"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "Untuk difaktur"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_type
msgid "Track Service"
msgstr "Telusuri Jasa"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__vendor_bill_count
msgid "Vendor Bill Count"
msgstr "Jumlah Tagihan Vendor"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Vendor Bills"
msgstr "Tagihan Pemasok"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""
"Anda tidak dapat melink order item %(order_id)s - %(product_id)s untuk task "
"ini karena ini merupakan pengeluaran yang difaktur ulang."
