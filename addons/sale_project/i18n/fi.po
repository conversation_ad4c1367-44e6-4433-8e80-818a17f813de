# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <ka<PERSON><PERSON>l<PERSON><PERSON>@emsystems.fi>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# J<PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Tuomas Lyyra <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Virstanpylväitä ei löytynyt. Luodaan sellainen!\n"
"                </p><p>\n"
"                    Seuraa tärkeimpiä edistymiskohtia, jotka on saavutettava menestyksen saavuttamiseksi.\n"
"                </p>\n"
"            "

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "%(name)s's Sales Order"
msgstr "%(name)s:n myyntitilaus"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Tuotteen kokoonpanon mukaan toimitettu määrä voidaan laskea automaattisesti mekanismilla:\n"
"   - Manuaalinen: määrä asetetaan manuaalisesti riville\n"
"   - Analytic Kustannuksista: määrä on lähetettyjen kulujen määrä\n"
"   - Aikataulu: määrä on tämän myyntilinjan tehtäviin kirjattujen tuntien summa\n"
"   - Varastoliikkeet: määrä tulee vahvistetuista keräyksistä\n"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "All items have been loaded"
msgstr "Kaikki kohteet on ladattu"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Based on Delivered Quantity (Manual)"
msgstr "Toimitetun määrän perusteella (manuaalinen)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Based on Milestones"
msgstr "Virstanpylväiden perusteella"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_project__allow_billable
msgid "Billable"
msgstr "Laskutettava"

#. module: sale_project
#: model:ir.model,name:sale_project.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Create Invoice"
msgstr "Luo lasku"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Luo tilauksesta"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__project_partner_id
msgid "Customer"
msgstr "Asiakas"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Delivered"
msgstr "Toimitettu"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_name
msgid "Description"
msgstr "Kuvaus"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__display_sale_order_button
msgid "Display Sales Order"
msgstr "Näytä myyntitilaus"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr "Näytä projekti"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr "Generoitu projekti"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr "Generoitu tehtävä"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr "Kohteella on myyntitilaus, johon lasku kohdistetaan"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_with_nothing_to_invoice
msgid "Has a SO with an invoice status of No"
msgstr "Kohteella on myyntitilaus ja laskun status N:ro"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__invoice_count
msgid "Invoice Count"
msgstr "Laskujen lukumäärä"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr "Laskuta tilatut määrät heti, kun palvelu on myyty."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create a project"
" for the order with a task for each sales order line to track the time "
"spent."
msgstr ""
"Laskuta tilatut määrät heti, kun palvelu on myyty. Luo tilausta varten "
"projekti, jossa jokaiselle myyntitilausriville on oma tehtävänsä, jotta voit"
" seurata käytettyä aikaa."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create a task in"
" an existing project to track the time spent."
msgstr ""
"Laskuta tilatut määrät heti, kun palvelu on myyty. Luo tehtävä olemassa "
"olevaan projektiin kuluneen ajan seuraamiseksi."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create an empty "
"project for the order to track the time spent."
msgstr ""
"Laskuta tilatut määrät heti, kun palvelu on myyty. Luo tilaukselle tyhjä "
"projekti, jotta voit seurata käytettyä aikaa."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). "
msgstr ""
"Laskuta tämä palvelu, kun se toimitetaan (määritä määrä käsin "
"myyntitilausrivillä)."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create a project for the order with a task for each "
"sales order line to track the time spent."
msgstr ""
"Laskuta tämä palvelu, kun se toimitetaan (määritä määrä käsin "
"myyntitilausrivillä). Luo tilaukselle projekti, jossa on tehtävä jokaiselle "
"myyntitilausriville, jotta voit seurata käytettyä aikaa."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create a task in an existing project to track the time "
"spent."
msgstr ""
"Laskuta tämä palvelu, kun se toimitetaan (määritä määrä käsin "
"myyntitilausrivillä). Luo tehtävä olemassa olevaan projektiin kuluneen ajan "
"seuraamiseksi."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create an empty project for the order to track the time "
"spent."
msgstr ""
"Laskuta tämä palvelu, kun se toimitetaan (määritä määrä käsin "
"myyntitilausrivillä). Luo tilaukselle tyhjä projekti, jotta voit seurata "
"käytettyä aikaa."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice your milestones when they are reached."
msgstr "Laskuta virstanpylväitä, kun ne on saavutettu."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create a project for the "
"order with a task for each sales order line to track the time spent."
msgstr ""
"Laskuta virstanpylväitä, kun ne on saavutettu. Luo tilausta varten projekti,"
" jossa on tehtävä jokaiselle myyntitilausriville, jotta voit seurata "
"käytettyä aikaa."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create a task in an existing "
"project to track the time spent."
msgstr ""
"Laskuta virstanpylväitä, kun ne on saavutettu. Luo tehtävä olemassa olevaan "
"projektiin, jotta voit seurata kulunutta aikaa."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create an empty project for "
"the order to track the time spent."
msgstr ""
"Laskuta virstanpylväitä, kun ne on saavutettu. Luo tilausta varten tyhjä "
"projekti, jotta voit seurata käytettyä aikaa."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Invoice your time and material to customers"
msgstr ""
"Laskuta asiakasta käytetyn työajan ja kustannusten perusteella (time & "
"material)"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Invoiced"
msgstr "Laskutettu"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Invoices"
msgstr "Laskut"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Invoicing Policy"
msgstr "Laskutus perustuu"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__is_product_milestone
msgid "Is Product Milestone"
msgstr "Onko tuote virstanpylväs"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__is_service
msgid "Is a Service"
msgstr "On palvelu"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move_line
msgid "Journal Item"
msgstr "Päiväkirjatapahtuma"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Load more"
msgstr "Näytä lisää"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_type
#: model:ir.model.fields,help:sale_project.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Aseta määrät manuaalisesti tilauksesta: Lasku perustuu manuaalisesti syötettyyn määrään luomatta analyyttistä tiliä.\n"
"Sopimuksen aikataulut: Lasku perustuu siihen liittyvän aikataulun seurattuihin tunteihin.\n"
"Luo tehtävä ja seuraa tunteja: Luo tehtävä myyntitilauksen vahvistukseen ja seuraa työtuntia. "

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Materials"
msgstr "Materiaalit"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Menetelmä päivittääkseen toimitettuja määriä"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__milestone_count
msgid "Milestone Count"
msgstr "Virstanpylväiden määrä"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_project.selection__sale_order_line__qty_delivered_method__milestones
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Milestones"
msgstr "Virstanpylväät"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "New"
msgstr "Uusi"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "No Sales Order"
msgstr "Ei myyntitilaus"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "No Sales Order Item"
msgstr "Ei myyntitilausriviä"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "Non-billable"
msgstr "Ei-laskutettava"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "Ei mitään"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr "Projektien lukumäärä"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""
"Myyntitilauksen vahvistuksen yhteydessä tämä tuote voi luoda projektin ja/tai tehtävän.         Niistä voit seurata myytävää palvelua.\n"
"         'Myyntitilauksen projektissa': Käyttää myyntitilauksen määritettyä projektia, jos sellainen on määritetty, tai luo uuden projektin valitun mallin perusteella."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid "Operation not supported"
msgstr "Toiminto ei ole tuettu"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Other Revenues"
msgstr "Muut tulot"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Other Services"
msgstr "Muut palvelut"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__quantity_percentage
msgid ""
"Percentage of the ordered quantity that will automatically be delivered once"
" the milestone is reached."
msgstr ""
"Prosenttiosuus tilatusta määrästä, joka toimitetaan automaattisesti, kun "
"virstanpylväs on saavutettu."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Prepaid/Fixed Price"
msgstr "Ennakkomaksettu / kiinteä hinta"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product"
msgstr "Tuote"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product Variant"
msgstr "Tuotevariaatio"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "Projektit"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "Projekti ja tehtävä"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_milestone
msgid "Project Milestone"
msgstr "Projektin virstanpylväs"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_type__milestones
msgid "Project Milestones"
msgstr "Projektin virstanpylväät"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "Projektipohja"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "Projektin myyntitilaus"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Projects"
msgstr "Projektit"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "Projekti jota käytetään tässä myyntitilauksessa."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__quantity_percentage
msgid "Quantity"
msgstr "Määrä"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Tarjouspohjarivi"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__reached_milestones_ids
msgid "Reached Milestones"
msgstr "Saavutetut virstanpylväät"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
msgid "Sale Order"
msgstr "Myyntitilaus"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_count
msgid "Sale Order Count"
msgstr "Myyntitilausten lukumäärä"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sales"
msgstr "Myynti"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Sales & Invoicing"
msgstr "Myynti ja laskutus"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order"
msgstr "Myyntitilaus"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
msgid "Sales Order Id"
msgstr "Myyntitilauksen tunnus"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_task_project_user_search_inherited
#, python-format
msgid "Sales Order Item"
msgstr "Myyntitilausrivi"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__sale_line_id
msgid "Sales Order Item that will be updated once the milestone is reached."
msgstr "Myyntitilauserä, joka päivitetään, kun virstanpylväs on saavutettu."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added in order to be invoiced to your customer.\n"
"By default the sales order item set on the project will be selected. In the absence of one, the last prepaid sales order item that has time remaining will be used.\n"
"Remove the sales order item in order to make this task non billable. You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"Myyntitilauksen kohde, johon tähän tehtävään käytetty aika lisätään, jotta se voidaan laskuttaa asiakkaalta.\n"
"Oletusarvoisesti valitaan projektille asetettu myyntitilauserä. Jos sellaista ei ole, käytetään viimeisintä ennakkoon maksettua myyntitilauserää, jolla on aikaa jäljellä.\n"
"Poista myyntitilauserä, jotta tämä tehtävä ei ole laskutettava. Voit myös muuttaa tai poistaa myyntitilauserän kustakin työaikakirjanpidon merkinnästä erikseen."

#. module: sale_project
#. odoo-javascript
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sales Order Items"
msgstr "Myyntitilauksen nimikkeet"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "Myyntitilausrivi"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Sales Orders"
msgstr "Myyntitilaukset"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"Myyntitilauserä, joka valitaan oletusarvoisesti tämän projektin tehtäviin ja työaikatauluihin, paitsi jos työaikataulujen työntekijä on nimenomaisesti linkitetty toiseen projektin myyntitilauserään.\n"
"Sitä voidaan tarvittaessa muuttaa jokaisessa tehtävässä ja työaikataulussa erikseen."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "Myyntitilaus, johon projekti liittyy."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Myyntitilaus, johon tehtävä on liitetty."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "Haku laskussa"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "Hae myyntitilausta"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr "Valitse ei-laskutettava projekti, johon voidaan luoda tehtäviä."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "Palvelun laskutustapa"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sold"
msgstr "Myyty"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "Tehtävä"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "Task Created (%s): %s"
msgstr "Tehtävä luotu (%s): %s"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Tehtävän toistuvuus"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "Tehtävät"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Tehtäväanalyysi"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr "Tähän myyntiin liittyvät tehtävät"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr ""
"Tuotteella %s ei pitäisi olla yleistä projektia, koska se luo projektin."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""
"Tuotteella %s ei pitäisi olla projektia eikä projektimallia, koska se ei luo"
" projektia."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""
"Tuotteella %s ei pitäisi olla projektimallia, koska se luo tehtävän "
"yleisessä projektissa."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "This task has been created from: %s (%s)"
msgstr "Tämä tehtävä on luotu: %s (%s)"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "Laskuta"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_type
msgid "Track Service"
msgstr "Palvelun seuranta"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__vendor_bill_count
msgid "Vendor Bill Count"
msgstr "Myyjän laskujen määrä"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Vendor Bills"
msgstr "Ostolaskut"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""
"Et voi yhdistää tilauserää %(order_id)s - %(product_id)s tähän tehtävään, "
"koska se on asiakkaalta laskutettava kulu."
