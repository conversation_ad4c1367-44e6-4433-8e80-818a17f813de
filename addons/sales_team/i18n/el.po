# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sales_team
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "<span>New</span>"
msgstr "<span>Nέο</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Αναφορά</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "<span>View</span>"
msgstr "<span>Προβολή</span>"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction
msgid "Action Needed"
msgstr "Απαιτείται ενέργεια"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__active
msgid "Active"
msgstr "Σε Ισχύ"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.mail_activity_type_action_config_sales
msgid "Activity Types"
msgstr "Τύποι Δραστηριότητας"

#. module: sales_team
#: model:crm.team,name:sales_team.crm_team_1
msgid "America"
msgstr "Αμερική"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Archived"
msgstr "Αρχειοθετημένα"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Avatar"
msgstr "Άβαταρ"

#. module: sales_team
#: selection:crm.team,dashboard_graph_type:0
msgid "Bar"
msgstr "Μπάρα"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:274
#, python-format
msgid "Big Pretty Button :)"
msgstr "Μεγάλο Ωραίο Κουμπί :)"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_ids
msgid "Channel Members"
msgstr "Μέλη Καναλιού"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__color
msgid "Color Index"
msgstr "Χρωματισμός Ευρετήριου"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__company_id
msgid "Company"
msgstr "Εταιρία"

#. module: sales_team
#: model:ir.ui.menu,name:sales_team.menu_sale_config
msgid "Configuration"
msgstr "Διαμόρφωση"

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_partner
msgid "Contact"
msgstr "Επαφή"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_model
msgid "Content"
msgstr "Περιεχόμενα"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__currency_id
msgid "Currency"
msgstr "Νόμισμα"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Dashboard"
msgstr "Ταμπλό"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_button_name
msgid "Dashboard Button"
msgstr "Κουμπί Ταμπλό"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_data
msgid "Dashboard Graph Data"
msgstr "Γραφικά Δεδομένα Ταμπλό"

#. module: sales_team
#: selection:crm.team,dashboard_graph_group:0
msgid "Day"
msgstr "Ημέρα"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_salesteams_act
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_config_action
msgid "Define a new Sales Team"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: sales_team
#: model:crm.team,name:sales_team.team_sales_department
msgid "Europe"
msgstr "Ευρώπη"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__favorite_user_ids
msgid "Favorite Members"
msgstr "Αγαπημένα Μέλη"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_favorite
msgid ""
"Favorite teams to display them in the dashboard and access them easily."
msgstr ""
"Αγαπημένες ομάδες για προβολή στο Ταμπλό και εύκολη πρόσβαση σε αυτές."

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid ""
"Follow this salesteam to automatically track the events associated to users "
"of this team."
msgstr ""
"Ακολουθήστε αυτή την ομάδα πωλήσεων για να εντοπίζετε αυτοματοποιημένα τα "
"συμβάντα που σχετίζονται με τους χρήστες της ομάδας. "

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_follower_ids
msgid "Followers"
msgstr "Ακόλουθοι"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_channel_ids
msgid "Followers (Channels)"
msgstr "Ακόλουθοι (Κανάλια)"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_partner_ids
msgid "Followers (Partners)"
msgstr "Ακόλουθοι (Συνεργάτες)"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Graph"
msgstr "Γράφημα"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Group By..."
msgstr "Ομαδοποίηση κατά..."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_group
msgid "Group by"
msgstr "Ομαδοποίηση κατά"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__dashboard_graph_group
msgid "How this channel's dashboard graph will group the results."
msgstr "Πώς θα ομαδοποιήσει αυτό το γράφημα καναλιού τα αποτελέσματα."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__id
msgid "ID"
msgstr "Κωδικός"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_unread
msgid "If checked new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_partner__team_id
msgid ""
"If set, this Sales Team will be used for sales and assignations related to "
"this partner"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__active
msgid ""
"If the active field is set to false, it will allow you to hide the Sales "
"Team without removing it."
msgstr ""

#. module: sales_team
#: selection:crm.team,dashboard_graph_model:0
msgid "Invoices"
msgstr "Τιμολόγια"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_is_follower
msgid "Is Follower"
msgstr "Είναι Ακόλουθος"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: sales_team
#: selection:crm.team,dashboard_graph_period:0
msgid "Last Month"
msgstr "Τελευταίος Μήνας"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: sales_team
#: selection:crm.team,dashboard_graph_period:0
msgid "Last Week"
msgstr "Προηγ. Εβδομάδα"

#. module: sales_team
#: selection:crm.team,dashboard_graph_period:0
msgid "Last Year"
msgstr "Τελευταίο Έτος"

#. module: sales_team
#: selection:crm.team,dashboard_graph_type:0
msgid "Line"
msgstr "Γραμμή"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_manager
msgid "Manager"
msgstr "Διευθυντής"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_ids
msgid "Messages"
msgstr "Μηνύματα"

#. module: sales_team
#: selection:crm.team,dashboard_graph_group:0
msgid "Month"
msgstr "Μήνας"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "My Favorites"
msgstr "Τα Αγαπημένα μου"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:260
#, python-format
msgid "Not Defined"
msgstr "Δεν Ορίστηκε"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction_counter
msgid "Number of Actions"
msgstr "Πλήθος ενεργειών"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Πλήθος μηνυμάτων που απαιτούν ενέργεια"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_unread_counter
msgid "Number of unread messages"
msgstr "Πλήθος μη αναγνωσμένων μηνυμάτων"

#. module: sales_team
#: selection:crm.team,dashboard_graph_model:0
msgid "Pipeline"
msgstr "Ροή Πληροφοριών"

#. module: sales_team
#: selection:crm.team,team_type:0
msgid "Point of Sale"
msgstr "Εντατική Λιανική"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__reply_to
msgid "Reply-To"
msgstr "Απάντηση στο"

#. module: sales_team
#: selection:crm.team,dashboard_graph_model:0 selection:crm.team,team_type:0
msgid "Sales"
msgstr "Πωλήσεις"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team
msgid "Sales Channels"
msgstr "Κανάλια Πωλήσεων"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__name
#: model:ir.model.fields,field_description:sales_team.field_res_partner__team_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_tree
msgid "Sales Team"
msgstr "Ομάδα Πώλησης"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_users__sale_team_id
msgid ""
"Sales Team the user is member of. Used to compute the members of a Sales "
"Team through the inverse one2many"
msgstr ""

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.sales_team_config_action
msgid "Sales Teams"
msgstr "Ομάδες Πωλήσεων"

#. module: sales_team
#: selection:crm.team,dashboard_graph_group:0
msgid "Salesperson"
msgstr "Πωλητής"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Salesteam name..."
msgstr "Όνομα Ομάδας Πωλήσεων..."

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Salesteams Search"
msgstr "Αναζήτηση Ομάδων Πωλήσεων"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_period
msgid "Scale"
msgstr "Κλίμακα"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "Settings"
msgstr "Ρυθμίσεις"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_favorite
msgid "Show on dashboard"
msgstr "Εμφάνιση στο Ταμπλό"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__user_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Team Leader"
msgstr "Αρχηγός Ομάδας"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Team Members"
msgstr "Μέλη Ομάδας"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_salesteams_act
msgid "Team Pipelines"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__team_type
msgid "Team Type"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__color
msgid "The color of the channel"
msgstr "Το χρώμα του καναλιού"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__reply_to
msgid ""
"The email address put in the 'Reply-To' of all emails sent by Odoo about "
"cases in this Sales Team"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__dashboard_graph_model
msgid "The graph this channel will display in the Dashboard.\n"
msgstr "Το γράφημα για αυτό το κανάλι θα εμφανιστεί στο Ταμπλό.\n"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__dashboard_graph_period
msgid "The time period this channel's dashboard graph will consider."
msgstr ""
"Η χρονική περίοδος που θα λάβει υπ όψιν το γράφημα ταμπλό του καναλιού."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__dashboard_graph_type
msgid "The type of graph this channel will display in the dashboard."
msgstr "Ο τύπος γραφήματος αυτού του καναλιού θα εμφανιστεί στο Ταμπλό."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__team_type
msgid ""
"The type of this channel, it will define the resources this channel uses."
msgstr ""
"Ο τύπος αυτού του καναλιού θα καθορίσει τους πόρους που χρησιμοποιεί το "
"κανάλι αυτό."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_type
msgid "Type"
msgstr "Τύπος"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:151
#: code:addons/sales_team/models/crm_team.py:178
#, python-format
msgid "Undefined graph model for Sales Team: %s"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_unread
msgid "Unread Messages"
msgstr "Μη αναγνωσμένα μηνύματα"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Μετρητής μη αναγνωσμένων μηνυμάτων"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_salesteams_act
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_config_action
msgid ""
"Use Sales Teams to organize your sales departments.\n"
"                    Each channel will work with a separate pipeline."
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_res_users__sale_team_id
msgid "User's Sales Team"
msgstr ""

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman_all_leads
msgid "User: All Documents"
msgstr "Χρήστης: Όλα τα Έγγραφα"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman
msgid "User: Own Documents Only"
msgstr "Χρήστης: Μόνο τα Δικά του Έγγραφα"

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_users
msgid "Users"
msgstr "Χρήστες"

#. module: sales_team
#: model:crm.team,name:sales_team.salesteam_website_sales
#: selection:crm.team,team_type:0
msgid "Website"
msgstr "Ιστότοπος"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__website_message_ids
msgid "Website Messages"
msgstr "Μηνύματα Ιστότοπου"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__website_message_ids
msgid "Website communication history"
msgstr "Ιστορικό επικοινωνίας ιστότοπου"

#. module: sales_team
#: selection:crm.team,dashboard_graph_group:0
msgid "Week"
msgstr "Εβδομάδα"

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman_all_leads
msgid ""
"the user will have access to all records of everyone in the sales "
"application."
msgstr ""
"ο χρήστης θα έχει πρόσβαση σε όλα τα αρχεία όλων στην εφαρμογή πωλήσεις."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman
msgid "the user will have access to his own data in the sales application."
msgstr ""
"ο χρήστης θα έχει πρόσβαση στα δικά του δεδομένα στην εφαρμογή πωλήσεις."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_manager
msgid ""
"the user will have an access to the sales configuration as well as statistic"
" reports."
msgstr ""
"Ο χρήστης θα έχει πρόσβαση στις ρυθμίσεις πωλήσεων και στις αναφορές "
"στατιστικών."
