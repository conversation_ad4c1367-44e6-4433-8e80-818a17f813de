# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_id_efaktur_coretax
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-06 02:23+0000\n"
"PO-Revision-Date: 2025-02-06 02:23+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_08__td_00501
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_08__td_01101
msgid ""
"1 - PPN Dibebaskan Sesuai PP Nomor 146 Tahun 2000 Sebagaimana Telah Diubah "
"Dengan PP Nomor 38 Tahun 2003"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00501
msgid ""
"1 - Pajak Pertambahan Nilai Tidak Dipungut berdasarkan PP Nomor 10 Tahun "
"2012"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01101
msgid "1 - untuk Kawasan Bebas"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_08__td_00510
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_08__td_01110
msgid "10 - PPN Dibebaskan berdasarkan PP Nomor 49 Tahun 2022"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00510
msgid "10 - PPN Tidak Dipungut Berdasarkan PP Nomor 106 Tahun 2015"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01110
msgid "10 - untuk BKP tertentu yang bersifat strategis berupa anode slime"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_kode_transaksi__10
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__res_partner__l10n_id_kode_transaksi__10
msgid "10 Other deliveries"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00511
msgid "11 - PPN Tidak Dipungut Sesuai PP Nomor 50 Tahun 2019"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01111
msgid ""
"11 - untuk Penyerahan alat angkutan tertentu dan/atau Jasa Kena Pajak "
"terkait alat angkutan tertentu"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00512
msgid ""
"12 - PPN atau PPN dan PPnBM Tidak Dipungut Sesuai Dengan PP Nomor 27 Tahun "
"2017"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01112
msgid ""
"12 - untuk Penyerahan kepada Kontraktor Kerja Sama Migas yang mengikuti "
"ketentuan Peraturan Pemerintah Nomor 27 Tahun 2017"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00513
msgid "13 - PPN ditanggung PEMERINTAH EX PMK 21/PMK.010/21"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01113
msgid ""
"13 - Penyerahan Rumah Tapak dan Satuan Rumah Susun Rumah Susun Ditanggung "
"Pemerintah Tahun Anggaran 2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00514
msgid "14 - PPN DITANGGUNG PEMERINTAH EKS PMK 102/PMK.010/2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01114
msgid ""
"14 - Penyerahan Jasa Sewa Ruangan atau Bangunan Kepada Pedagang Eceran yang "
"Ditanggung Pemerintah Tahun Anggaran 2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00515
msgid "15 - PPN DITANGGUNG PEMERINTAH EKS PMK 239/PMK.03/2020"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01115
msgid ""
"15 - Penyerahan Barang dan Jasa Dalam Rangka Penanganan Pandemi COVID-19 "
"(PMK 239/PMK. 03/2020)"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01116
msgid ""
"16 - Insentif PMK-103/PMK.010/2021 berupa PPN atas Penyerahan Rumah Tapak "
"dan Unit Hunian Rumah Susun yang Ditanggung Pemerintah Tahun Anggaran 2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00516
msgid ""
"16 - Insentif PPN DITANGGUNG PEMERINTAH EKSEKUSI PMK NOMOR 103/PMK.010/2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01117
msgid "17 - Kawasan Ekonomi Khusus PP nomor 40 Tahun 2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00517
msgid ""
"17 - PAJAK PERTAMBAHAN NILAI TIDAK DIPUNGUT BERDASARKAN PP NOMOR 40 TAHUN "
"2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01118
msgid "18 - Kawasan Bebas PP nomor 41 Tahun 2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00518
msgid ""
"18 - PAJAK PERTAMBAHAN NILAI TIDAK DIPUNGUT BERDASARKAN PP NOMOR 41 TAHUN "
"2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00519
msgid "19 - PPN DITANGGUNG PEMERINTAH EKS PMK 6/PMK.010/2022"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01119
msgid ""
"19 - Penyerahan Rumah Tapak dan Unit Hunian Rumah Susun yang Ditanggung "
"Pemerintah Tahun Anggaran 2022"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_08__td_00502
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_08__td_01102
msgid ""
"2 - PPN Dibebaskan Sesuai PP Nomor 12 Tahun 2001 Sebagaimana Telah Beberapa "
"Kali Diubah Terakhir Dengan PP Nomor 31 Tahun 2007"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00502
msgid ""
"2 - Pajak Pertambahan Nilai atau Pajak Pertambahan Nilai dan Pajak Penjualan"
" atas Barang Mewah tidak dipungut"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01102
msgid "2 - untuk Tempat Penimbunan Berikat"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00520
msgid "20 - PPN DITANGGUNG PEMERINTAH EKSEKUSI PMK NOMOR 226/PMK.03/2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01120
msgid ""
"20 - PPN Ditanggung Pemerintah dalam rangka Penanganan Pandemi Corona Virus"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00521
msgid ""
"21 - PPN ATAU PPN DAN PPnBM TIDAK DIPUNGUT SESUAI DENGAN PP NOMOR 53 TAHUN "
"2017"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01121
msgid ""
"21 - Penyerahan kepada Kontraktor Kerja Sama Migas yang mengikuti ketentuan "
"Peraturan Pemerintah Nomor 53 Tahun 2017"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01122
msgid "22 - BKP strategis tertentu dalam bentuk anode slime dan emas butiran"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00522
msgid "22 - PPN tidak dipungut berdasarkan PP Nomor 70 Tahun 2021"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00523
msgid "23 - PPN ditanggung Pemerintah Ex PMK-125/PMK.01/2020"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01123
msgid "23 - untuk penyerahan kertas koran dan/atau majalah"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00524
msgid "24 - (Tidak ada Cap)"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01124
msgid "24 - PPN tidak dipungut oleh Pemerintah lainnya"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01125
msgid "25 - BKP dan JKP tertentu"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00525
msgid "25 - PPN tidak dipungut berdasarkan PP Nomor 49 Tahun 2022"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00526
msgid "26 - PPN tidak dipungut berdasarkan PP Nomor 12 Tahun 2023"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01126
msgid "26 - Penyerahan BKP dan JKP di Ibu Kota Negara baru"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00527
msgid "27 - PPN ditanggung Pemerintah berdasarkan PMK Nomor 38 Tahun 2023"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01127
msgid "27 - Penyerahan kendaraan listrik berbasis baterai"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_08__td_00503
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_08__td_01103
msgid ""
"3 - PPN dibebaskan berdasarkan Peraturan Pemerintah Nomor 28 Tahun 2009"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00503
msgid ""
"3 - Pajak Pertambahan Nilai dan Pajak Penjualan atas Barang Mewah Tidak "
"Dipungut"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01103
msgid "3 - untuk Hibah dan Bantuan Luar Negeri"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_08__td_00504
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_08__td_01104
msgid "4 - (Tidak ada cap)"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00504
msgid ""
"4 - Pajak Pertambahan Nilai Tidak Dipungut Sesuai PP Nomor 71 Tahun 2012"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01104
msgid "4 - untuk Avtur"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00505
msgid "5 - (Tidak ada Cap)"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_08__td_00505
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_08__td_01105
msgid "5 - PPN Dibebaskan Sesuai Dengan PP Nomor 81 Tahun 2015"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01105
msgid "5 - untuk Lainnya"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_08__td_00506
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_08__td_01106
msgid "6 - PPN Dibebaskan Berdasarkan PP Nomor 74 Tahun 2015"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00506
msgid ""
"6 - PPN dan/atau PPnBM tidak dipungut berdasarkan PMK No. 194/PMK.03/2012"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01106
msgid ""
"6 - untuk Kontraktor Perjanjian Karya Pengusahaan Pertambangan Batubara "
"Generasi I"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_08__td_00507
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_08__td_01107
msgid "7 - (tanpa cap)"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00507
msgid "7 - PPN Tidak Dipungut Berdasarkan PP Nomor 15 Tahun 2015"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01107
msgid ""
"7 - untuk Penyerahan bahan bakar minyak untuk Kapal Angkutan Laut Luar "
"Negeri"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_08__td_00508
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_08__td_01108
msgid ""
"8 - PPN DIBEBASKAN SESUAI PP NOMOR 81 TAHUN 2015 SEBAGAIMANA TELAH DIUBAH "
"DENGAN PP 48 TAHUN 2020"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00508
msgid "8 - PPN Tidak Dipungut Berdasarkan PP Nomor 69 Tahun 2015"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01108
msgid "8 - untuk Penyerahan jasa kena pajak terkait alat angkutan tertentu"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_08__td_00509
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_08__td_01109
msgid "9 - PPN DIBEBASKAN BERDASARKAN PP NOMOR 47 TAHUN 2020"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_add_info_07__td_00509
msgid "9 - PPN Tidak Dipungut Berdasarkan PP Nomor 96 Tahun 2015"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__account_move__l10n_id_coretax_facility_info_07__td_01109
msgid "9 - untuk Penyerahan BKP Tertentu di KEK"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_form_view
msgid "Accounting Date"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_needaction
msgid "Action Needed"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__active
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_filter_view
msgid "Active"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_ids
msgid "Activities"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_state
msgid "Activity State"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.account_move_efaktur_form_view
msgid "Additional Information"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__attachment_id
msgid "Attachment"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_res_partner__l10n_id_tku
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_res_users__l10n_id_tku
msgid "Branch Number of your company, 000000 is the default for headquarters"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_product_code__code
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_uom_code__code
msgid "Code"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__company_id
msgid "Company"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model,name:l10n_id_efaktur_coretax.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__create_uid
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_product_code__create_uid
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_uom_code__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__create_date
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_product_code__create_date
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_uom_code__create_date
msgid "Created on"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "Customer %s is not taxable, tick ID PKP if necessary"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_product_code__description
msgid "Description"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__display_name
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_product_code__display_name
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_uom_code__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "Display Related Documents"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "Document Mismatch"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_res_partner__l10n_id_buyer_document_number
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_res_users__l10n_id_buyer_document_number
msgid "Document Number"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_res_partner__l10n_id_buyer_document_type
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_res_users__l10n_id_buyer_document_type
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.res_partner_tax_form_view
msgid "Document Type"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "Document number for customer %s hasn't been filled in"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_form_view
msgid "Download"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.actions.server,name:l10n_id_efaktur_coretax.download_efaktur
msgid "Download E-Faktur"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_form_view
msgid "Draft"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_account_bank_statement_line__l10n_id_kode_transaksi
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_account_move__l10n_id_kode_transaksi
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_account_payment__l10n_id_kode_transaksi
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_res_partner__l10n_id_kode_transaksi
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_res_users__l10n_id_kode_transaksi
msgid "Dua digit pertama nomor pajak"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model,name:l10n_id_efaktur_coretax.model_l10n_id_efaktur_coretax_document
msgid "E-Faktur Document"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_product_product__l10n_id_product_code
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_product_template__l10n_id_product_code
msgid "E-Faktur Product Code"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_uom_uom__l10n_id_uom_code
msgid "E-Faktur UoM code"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_form_view
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_list_view
msgid "E-faktur Document"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "Entry %s is not an invoice"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.account_move_efaktur_form_view
msgid "Facility Stamp"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_follower_ids
msgid "Followers"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__has_message
msgid "Has Message"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__id
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_product_code__id
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_uom_code__id
msgid "ID"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_has_error
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_filter_view
msgid "Inactive"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.product_template_inherit
msgid "Indonesian Localization"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__invoice_ids
msgid "Invoice"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "Invoice %s does not contain any taxes"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid ""
"Invoice %s doesn't contain the Additional info and Facility Stamp yet (Kode "
"07)"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid ""
"Invoice %s doesn't contain the Additional info and Facility Stamp yet (Kode "
"08)"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "Invoice %s is in draft state"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "Invoice %s is not under Indonesian company"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_form_view
msgid "Invoices"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model,name:l10n_id_efaktur_coretax.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model,name:l10n_id_efaktur_coretax.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_bank_statement_line__l10n_id_kode_transaksi
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_move__l10n_id_kode_transaksi
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_payment__l10n_id_kode_transaksi
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_res_partner__l10n_id_kode_transaksi
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_res_users__l10n_id_kode_transaksi
msgid "Kode Transaksi"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_bank_statement_line__l10n_id_coretax_add_info_07
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_move__l10n_id_coretax_add_info_07
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_payment__l10n_id_coretax_add_info_07
msgid "L10N Id Coretax Add Info 07"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_bank_statement_line__l10n_id_coretax_add_info_08
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_move__l10n_id_coretax_add_info_08
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_payment__l10n_id_coretax_add_info_08
msgid "L10N Id Coretax Add Info 08"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_bank_statement_line__l10n_id_coretax_efaktur_available
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_move__l10n_id_coretax_efaktur_available
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_payment__l10n_id_coretax_efaktur_available
msgid "L10N Id Coretax Efaktur Available"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_bank_statement_line__l10n_id_coretax_facility_info_07
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_move__l10n_id_coretax_facility_info_07
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_payment__l10n_id_coretax_facility_info_07
msgid "L10N Id Coretax Facility Info 07"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_bank_statement_line__l10n_id_coretax_facility_info_08
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_move__l10n_id_coretax_facility_info_08
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_payment__l10n_id_coretax_facility_info_08
msgid "L10N Id Coretax Facility Info 08"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document____last_update
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_product_code____last_update
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_uom_code____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__write_uid
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_product_code__write_uid
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_uom_code__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__write_date
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_product_code__write_date
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_uom_code__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_ids
msgid "Messages"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__res_partner__l10n_id_buyer_document_type__nik
msgid "NIK"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "NPWP for customer %s hasn't been filled in yet"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__name
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_uom_code__name
msgid "Name"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__res_partner__l10n_id_buyer_document_type__other
msgid "Others"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__res_partner__l10n_id_buyer_document_type__passport
msgid "Passport"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model,name:l10n_id_efaktur_coretax.model_product_template
msgid "Product"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.produc_code_list
msgid "Product Codes"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model,name:l10n_id_efaktur_coretax.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model,name:l10n_id_efaktur_coretax.model_l10n_id_efaktur_coretax_product_code
msgid "Product categorization according to E-Faktur"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_form_view
msgid "Regenerate File"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_filter_view
msgid "Search Document"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/efaktur_document.py:0
#, python-format
msgid "Some documents are not Customer Invoices: %s"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/efaktur_document.py:0
#, python-format
msgid "Some documents don't have a transaction code: %s"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields.selection,name:l10n_id_efaktur_coretax.selection__res_partner__l10n_id_buyer_document_type__tin
msgid "TIN"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_res_partner__l10n_id_tku
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_res_users__l10n_id_tku
msgid "TKU"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_form_view
msgid "Tax"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/efaktur_document.py:0
#, python-format
msgid "The e-Faktur report has been generated"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/efaktur_document.py:0
#, python-format
msgid "The e-Faktur report has been re-generated"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid ""
"The selected invoices are partially part of one or more e-faktur documents.\n"
"Please download them from the e-faktur documents directly."
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.l10n_id_efaktur_document_form_view
msgid "Total"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model,name:l10n_id_efaktur_coretax.model_l10n_id_efaktur_coretax_uom_code
msgid "UOM categorization according to E-Faktur"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "Unable to download E-faktur fot he following reasons(s):"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,help:l10n_id_efaktur_coretax.field_l10n_id_efaktur_coretax_document__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid ""
"You are not allowed to generate e-Faktur document from invoices coming from "
"different companies"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/models/account_move.py:0
#, python-format
msgid "Your company's VAT hasn't been configured yet"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_bank_statement_line__l10n_id_coretax_document
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_move__l10n_id_coretax_document
#: model:ir.model.fields,field_description:l10n_id_efaktur_coretax.field_account_payment__l10n_id_coretax_document
msgid "e-Faktur Document"
msgstr ""

#. module: l10n_id_efaktur_coretax
#: model_terms:ir.ui.view,arch_db:l10n_id_efaktur_coretax.res_partner_tax_form_view
msgid "e.g. 0123456"
msgstr ""

#. module: l10n_id_efaktur_coretax
#. odoo-python
#: code:addons/l10n_id_efaktur_coretax/controllers/download_efaktur.py:0
#, python-format
msgid "efaktur"
msgstr ""
