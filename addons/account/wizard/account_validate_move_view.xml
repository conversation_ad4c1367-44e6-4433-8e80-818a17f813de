<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!--Account Move lines-->
        <record id="validate_account_move_view" model="ir.ui.view">
            <field name="name">Post Journal Entries</field>
            <field name="model">validate.account.move</field>
            <field name="arch" type="xml">
                <form string="Post Journal Entries">
                    <group>
                        <field name="force_post"/>
                    </group>
                    <span class="o_form_label">All selected journal entries will be validated and posted. You won't be able to modify them afterwards.</span>
                    <footer>
                        <button string="Post Journal Entries"
                                name="validate_move"
                                type="object"
                                default_focus="1"
                                class="btn-primary"
                                data-hotkey="q"
                                context="{'validate_analytic': True}"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_validate_account_move" model="ir.actions.act_window">
            <field name="name">Post entries</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">validate.account.move</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="validate_account_move_view"/>
            <field name="context">{}</field>
            <field name="target">new</field>
            <field name="help">This wizard will validate all journal entries selected. Once journal entries are validated, you can not update them anymore.</field>
            <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="binding_model_id" ref="account.model_account_move" />
            <field name="binding_view_types">list</field>
        </record>

    </data>
</odoo>
