<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- TAGS FOR RETRIEVING THE DEMO ACCOUNTS -->

        <record id="demo_capital_account" model="account.account.tag">
            <field name="name">Demo Capital Account</field>
        </record>
        <record id="demo_stock_account" model="account.account.tag">
            <field name="name">Demo Stock Account</field>
        </record>
        <record id="demo_sale_of_land_account" model="account.account.tag">
            <field name="name">Demo Sale of Land Account</field>
        </record>
        <record id="demo_ceo_wages_account" model="account.account.tag">
            <field name="name">Demo CEO Wages Account</field>
        </record>
        <record id="demo_office_furniture_account" model="account.account.tag">
            <field name="name">Office Furniture</field>
        </record>
        <!-- Payment Terms -->

        <record id="account_payment_term_advance" model="account.payment.term">
            <field name="name">30% Advance End of Following Month</field>
            <field name="note">Payment terms: 30% Advance End of Following Month</field>
            <field name="line_ids" eval="[
                Command.clear(),
                Command.create({'value': 'percent', 'value_amount': 30.0, 'days': 0}),
                Command.create({'value': 'balance', 'value_amount': 0.0, 'months': 1, 'end_month': True})]"/>
        </record>

        <record id="base.user_demo" model="res.users">
            <field name="groups_id" eval="[(4,ref('account.group_account_invoice'))]"/>
        </record>

        <!-- Add Payment terms on some demo partners -->
        <record id="base.res_partner_2" model="res.partner">
            <field name="property_payment_term_id" ref="account.account_payment_term_30days"/>
        </record>
        <record id="base.res_partner_12" model="res.partner">
            <field name="property_payment_term_id" ref="account.account_payment_term_end_following_month"/>
            <field name="property_supplier_payment_term_id" ref="account.account_payment_term_end_following_month"/>
        </record>
        <record id="base.res_partner_4" model="res.partner">
            <field name="property_supplier_payment_term_id" ref="account.account_payment_term_30days"/>
        </record>
        <record id="base.res_partner_1" model="res.partner">
            <field name="property_supplier_payment_term_id" ref="account.account_payment_term_end_following_month"/>
        </record>

        <!-- Analytic -->
        <record id="analytic.analytic_plan_internal" model="account.analytic.plan">
            <field name="applicability_ids" eval="[
                Command.create({
                    'business_domain': 'invoice',
                    'account_prefix': '450000',
                    'applicability': 'mandatory'})]"/>
        </record>
    </data>
</odoo>
