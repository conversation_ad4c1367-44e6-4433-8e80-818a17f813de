<odoo>
    <record id="account_journal_dashboard_kanban_view" model="ir.ui.view">
        <field name="name">account.journal.dashboard.kanban</field>
        <field name="model">account.journal</field>
        <field name="arch" type="xml">
            <kanban create="false" class="oe_background_grey o_kanban_dashboard o_account_kanban" banner_route="/account/account_dashboard_onboarding" js_class="account_dashboard_kanban">
                <field name="id"/>
                <field name="name"/>
                <field name="type"/>
                <field name="color"/>
                <field name="show_on_dashboard"/>
                <field name="kanban_dashboard"/>
                <field name="activity_ids"/>
                <field name="activity_state"/>
                <field name="alias_domain"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="d-flex flex-column flex-fill">
                            <t t-value="JSON.parse(record.kanban_dashboard.raw_value)" t-set="dashboard"/>
                            <t t-value="record.type.raw_value" t-set="journal_type"/>
                            <t t-call="JournalTop"/>
                            <div t-att-class="'container o_kanban_card_content' + (dashboard.is_sample_data ? ' o_sample_data' : '')">
                                <div class="row">
                                    <t t-if="(journal_type == 'bank' || journal_type == 'cash')" t-call="JournalBodyBankCash"/>
                                    <t t-if="journal_type == 'sale' || journal_type == 'purchase'" t-call="JournalBodySalePurchase"/>
                                    <t t-if="journal_type == 'general'" t-call="JournalMiscelaneous"/>
                                    <div class="col-12 col-sm-5 mb-3 mb-sm-0 o_kanban_primary_left"/>
                                    <div class="col-12 col-sm-7 o_kanban_primary_right">
                                        <t t-call="HasSequenceHoles"/>
                                    </div>
                                </div>
                                <div class="row mt-auto">
                                    <t t-if="['bank', 'cash', 'sale', 'purchase'].includes(journal_type)" t-call="JournalBodyGraph"/>
                                </div>
                            </div>
                            <div class="container o_kanban_card_manage_pane dropdown-menu" role="menu">
                                <t t-call="JournalManage"/>
                            </div>
                        </div>
                    </t>

                    <t t-name="JournalTop">
                        <div t-attf-class="o_kanban_card_header">
                            <div class="o_kanban_card_header_title">
                                <div class="o_primary">
                                    <a type="object" name="open_action"><field name="name"/></a>
                                    <t t-if="dashboard.company_count > 1">
                                        <span groups="base.group_multi_company" class="small">- <field name="company_id"/></span>
                                    </t>
                                </div>
                                <div class="o_secondary" t-att-title="dashboard.title" t-if="journal_type == 'purchase' &amp;&amp; record.alias_domain.raw_value">
                                    <field name="alias_id"/>
                                </div>
                            </div>
                            <div class="o_kanban_manage_button_section">
                                <a class="o_kanban_manage_toggle_button" href="#"><i class="fa fa-ellipsis-v" aria-label="Selection" role="img" title="Selection"/></a>
                            </div>
                        </div>
                    </t>

                    <t t-name="HasSequenceHoles">
                        <a t-if="dashboard.has_sequence_holes"
                           name="show_sequence_holes"
                           type="object"
                           class="text-warning"
                           title="Gaps due to canceled invoices, deleted entries or manual errors in open period.">
                            Gaps in the sequence
                        </a>
                    </t>

                    <t t-name="JournalManage">

                        <!-- For bank and cash -->
                        <div t-if="journal_type == 'bank' || journal_type == 'cash'" class="row">
                             <div class="col-4 o_kanban_card_manage_section o_kanban_manage_view">
                                <div id="card_action_view_menus" class="o_kanban_card_manage_title">
                                    <span role="separator">View</span>
                                </div>
                                <div id="action_card_statements">
                                    <a t-if="journal_type == 'bank'" role="menuitem" type="object" name="open_action_with_context" context="{'action_name': 'action_bank_statement_tree', 'search_default_journal': True}">Statements</a>
                                    <a t-if="journal_type == 'cash'" role="menuitem" type="object" name="open_action_with_context" context="{'action_name': 'action_view_bank_statement_tree', 'search_default_journal': True}">Statements</a>
                                </div>
                                <div>
                                    <a role="menuitem" type="object" name="open_collect_money">Cust. Payments</a>
                                </div>
                                <div>
                                    <a role="menuitem" type="object" name="open_spend_money">Vendor Payments</a>
                                </div>
                                <div>
                                    <a role="menuitem" type="object" name="open_transfer_money">Internal Transfers</a>
                                </div>
                                <div>
                                    <a role="menuitem" type="object" name="open_action" context="{'action_name': 'action_account_moves_all_a'}">Journal Items</a>
                                </div>
                            </div>

                             <div class="col-4 o_kanban_card_manage_section o_kanban_manage_new" groups="account.group_account_user">
                                <div class="o_kanban_card_manage_title">
                                    <span role="separator">New</span>
                                </div>
                                <div name="bank_customer_payment">
                                    <a role="menuitem" type="object" name="create_customer_payment">Cust. Payment</a>
                                </div>
                                <div>
                                    <a role="menuitem" type="object" name="create_supplier_payment">Vendor Payment</a>
                                </div>
                                <div>
                                    <a role="menuitem" type="object" name="create_internal_transfer">Internal Transfer</a>
                                </div>
                            </div>

                             <div class="col-4 o_kanban_card_manage_section o_kanban_manage_reconciliation">
                                <div class="o_kanban_card_manage_title">
                                    <span role="separator">Reconciliation</span>
                                </div>
                                <div>
                                    <a role="menuitem" type="object" name="open_action_with_context" context="{'action_name': 'action_account_reconcile_model', 'use_domain': ['|', ('match_journal_ids', '=', False), ('match_journal_ids', 'in', active_id)]}" groups="account.group_account_manager">Reconciliation Models</a>
                                </div>
                            </div>
                        </div>

                        <!-- For purchase and sale -->
                        <div t-if="journal_type == 'purchase' || journal_type == 'sale'" class="row">
                             <div class="col-4 o_kanban_card_manage_section o_kanban_manage_view">
                                <div class="o_kanban_card_manage_title">
                                    <span>View</span>
                                </div>
                                <div>
                                    <a t-if="journal_type == 'sale'" type="object" name="open_action" context="{'action_name': 'action_move_out_invoice_type'}">Invoices</a>
                                    <a t-if="journal_type == 'purchase'" type="object" name="open_action" context="{'action_name': 'action_move_in_invoice_type'}">Bills</a>
                                </div>
                                <div id="sale_purchase_refund">
                                    <a t-if="journal_type == 'sale'" type="object" name="open_action" context="{'action_name': 'action_move_out_refund_type'}">Credit Notes</a>
                                    <a t-if="journal_type == 'purchase'" type="object" name="open_action" context="{'action_name': 'action_move_in_refund_type'}">Refund</a>
                                </div>
                                <div>
                                    <a type="object" name="open_action" context="{'action_name': 'action_account_moves_all_a'}" groups="base.group_no_one">Journal Items</a>
                                </div>
                            </div>

                             <div class="col-4 o_kanban_card_manage_section o_kanban_manage_new" groups="account.group_account_invoice">
                                <div class="o_kanban_card_manage_title">
                                    <span>New</span>
                                </div>
                                <div>
                                    <a type="object" name="action_create_new">
                                        <span t-if="journal_type == 'sale'">Invoice</span>
                                        <span t-if="journal_type == 'purchase'">Bill</span>
                                    </a>
                                </div>
                                <div>
                                    <a type="object" name="action_create_new"  context="{'refund':True}">
                                        <span t-if="journal_type == 'sale'">Credit Note</span>
                                        <span t-if="journal_type == 'purchase'">Refund</span>
                                    </a>
                                </div>
                                <div t-if="journal_type == 'sale'">
                                    <widget name="account_file_uploader" title="Upload Invoices" btnClass="file_upload_kanban_action_a"/>
                                </div>
                            </div>

                             <div class="col-4 o_kanban_card_manage_section o_kanban_manage_reports">
                                <div class="o_kanban_card_manage_title">
                                    <span>Reporting</span>
                                </div>
                                <div>
                                    <a t-if="journal_type == 'sale'" type="action" name="%(action_account_invoice_report_all)d" groups="account.group_account_readonly">Invoices Analysis</a>
                                    <a t-if="journal_type == 'purchase'" type="action" name="%(action_account_invoice_report_all_supp)d" groups="account.group_account_readonly">Bills Analysis</a>
                                </div>
                            </div>
                        </div>

                        <!-- For general and situation -->
                        <div t-if="journal_type == 'general' || journal_type == 'situation'" class="row">
                             <div class="col-4 o_kanban_card_manage_section o_kanban_manage_view">
                                <div class="o_kanban_card_manage_title">
                                    <span>View</span>
                                </div>
                                <div>
                                    <a type="object" name="open_action" context="{'action_name': 'action_move_journal_line'}">Journal Entries</a>
                                </div>
                                <div>
                                    <a type="object" name="open_action" context="{'action_name': 'action_move_journal_line', 'search_default_unposted': 1}">Entries to Review</a>
                                </div>
                                <div>
                                    <a type="object" name="open_action" context="{'action_name': 'action_account_moves_all_a'}" groups="base.group_no_one">Journal Items</a>
                                </div>
                            </div>

                             <div class="col-4 o_kanban_card_manage_section o_kanban_manage_new" groups="account.group_account_user">
                                <div class="o_kanban_card_manage_title">
                                    <span>New</span>
                                </div>
                                <div>
                                    <a type="object" name="action_create_new">Journal Entry</a>
                                </div>
                            </div>

                             <div class="col-4 o_kanban_card_manage_section o_kanban_manage_operations">
                                <div class="o_kanban_card_manage_title">
                                    <span>Operations</span>
                                </div>
                                <div>
                                    <a type="object" name="open_action_with_context" context="{'action_name': 'action_validate_account_move', 'search_default_journal': True}"  groups="account.group_account_user">Post All Entries</a>
                                </div>
                            </div>
                        </div>

                        <div t-if="widget.editable" class="o_kanban_card_manage_settings row">
                            <div class="col-8">
                                <ul class="oe_kanban_colorpicker" data-field="color"/>
                            </div>
                        </div>

                        <div groups="account.group_account_manager" class="row o_kanban_card_manage_settings">
                            <div class="col-6 position-relative">
                                <field name="show_on_dashboard" widget="boolean_favorite" />
                            </div>
                            <div class="col-6 text-end mt-1">
                                <a class="dropdown-item" t-if="widget.editable" type="edit">Configuration</a>
                            </div>
                        </div>
                    </t>

                    <t t-name="JournalMiscelaneous">
                        <div class="col-12 col-sm-4 mb-3 mb-sm-0 o_kanban_primary_left">
                            <button id="new_misc_entry_button" type="object" name="action_create_new" class="btn btn-primary" groups="account.group_account_invoice">
                                <span>New Entry</span>
                            </button>
                        </div>
                        <div class="col-12 col-sm-8 o_kanban_primary_right">
                            <field name="json_activity_data" widget="kanban_vat_activity"/>
                            <t t-if="dashboard.number_to_check > 0">
                                <div class="row">
                                    <div class="col overflow-hidden text-start">
                                        <a type="object" name="open_action" context="{'action_name': 'action_move_journal_line', 'search_default_to_check': True}"><t t-esc="dashboard.number_to_check"/> to check</a>
                                    </div>
                                    <div class="col-auto text-end">
                                        <span class="o_kanban_monetary"><t t-esc="dashboard.to_check_balance"/></span>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </t>

                    <t t-name="JournalBodyBankCash">
                        <!-- On the left, display :
                            - A button corresponding to the bank_statements_source, if it wasn't configured, a button for each of them
                            - If there are statements to reconcile, a link to reconcile them -->
                        <div id="dashboard_bank_cash_left" class="col-12 col-sm-5 mb-3 mb-sm-0 o_kanban_primary_left">
                            <t t-if="journal_type == 'bank'">
                                <t t-if="dashboard.bank_statements_source == 'undefined'">
                                    <a t-if="dashboard.number_to_reconcile > 0" name="action_configure_bank_journal" type="object" class="oe_inline" groups="account.group_account_invoice">Connect</a>
                                    <button t-if="dashboard.number_to_reconcile == 0" name="action_configure_bank_journal" type="object" class="btn btn-primary" groups="account.group_account_invoice">Connect</button>
                                </t>
                                <div name="bank_journal_cta" class="mt-3 mt-sm-0"/>
                            </t>
                            <t t-if="journal_type == 'cash'">
                                <a t-if="dashboard.number_to_reconcile > 0" type="object" name="create_cash_statement" class="oe_inline" groups="account.group_account_invoice">New Transaction</a>
                                <button t-if="dashboard.number_to_reconcile == 0" type="object" name="create_cash_statement" class="btn btn-primary" groups="account.group_account_invoice">New Transaction</button>
                            </t>
                        </div>
                        <!-- On the right, show other common informations/actions -->
                        <div id="dashboard_bank_cash_right" class="col-12 col-sm-7 o_kanban_primary_right">
                            <div class="row" t-if="dashboard.nb_lines_bank_account_balance > 0">
                                <div id="dashboard_bank_cash_balance" class="col overflow-hidden text-start">
                                    <span>Running Balance</span>
                                </div>
                                <div class="col-auto text-end">
                                    <span class="o_kanban_monetary"><t t-esc="dashboard.account_balance"/></span>
                                </div>
                            </div>
                            <div class="row" t-if="dashboard.nb_lines_outstanding_pay_account_balance > 0">
                                <div id="dashboard_bank_cash_outstanding_balance" class="col overflow-hidden text-start">
                                    <span title="Outstanding Payments/Receipts">Outstanding Payments/Receipts</span>
                                </div>
                                <div class="col-auto text-end">
                                    <span class="o_kanban_monetary"><t t-esc="dashboard.outstanding_pay_account_balance"/></span>
                                </div>
                            </div>
                            <t t-if="dashboard.has_at_least_one_statement and dashboard.account_balance != dashboard.last_balance">
                                <div class="row" name="latest_statement">
                                    <div class="col overflow-hidden text-start">
                                        <span title="Latest Statement">Latest Statement</span>
                                    </div>
                                    <div class="col-auto text-end">
                                        <span class="o_kanban_monetary"><t t-esc="dashboard.last_balance"/></span>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </t>
                    <t t-name="JournalBodySalePurchase" id="account.JournalBodySalePurchase">
                        <div class="col-12 col-sm-5 mb-3 mb-sm-0 o_kanban_primary_left">
                            <t t-if="journal_type == 'sale'">
                                <button type="object" name="action_create_new" class="btn btn-primary o_invoice_new" groups="account.group_account_invoice">
                                    <span>New Invoice</span>
                                </button>
                            </t>
                            <t t-if="journal_type == 'purchase'">
                                <t t-if="dashboard.entries_count > 0">
                                    <widget name="account_file_uploader" btnClass="btn btn-primary oe_kanban_action_button"/>
                                </t>
                                <t t-else="">
                                    <button type="object" name="action_create_vendor_bill" class="btn btn-primary d-block" journal_type="purchase" groups="account.group_account_invoice">
                                        <span>Upload</span>
                                    </button>
                                </t>
                                <a type="object" name="action_create_new" class="o_invoice_new" groups="account.group_account_invoice">Create Manually</a>
                            </t>
                        </div>
                        <div class="col-12 col-sm-7 o_kanban_primary_right">
                            <div class="row" t-if="dashboard.number_draft">
                                <div class="col overflow-hidden text-start">
                                    <a type="object" name="open_action" context="{'search_default_draft': '1'}">
                                        <span t-if="journal_type == 'sale'" title="Invoices to Validate"><t t-esc="dashboard.number_draft"/> Invoices to Validate</span>
                                        <span t-if="journal_type == 'purchase'" title="Bills to Validate"><t t-esc="dashboard.number_draft"/> Bills to Validate</span>
                                    </a>
                                </div>
                                <div class="col-auto text-end">
                                    <span class="o_kanban_monetary"><t t-esc="dashboard.sum_draft"/></span>
                                </div>
                            </div>
                            <div class="row" t-if="dashboard.number_waiting">
                                <div class="col overflow-hidden text-start">
                                    <a type="object" t-if="journal_type == 'sale'" name="open_action"
                                    context="{'search_default_open':1, 'search_default_posted':1, 'search_default_partial': 1}" id="account_dashboard_sale_pay_link">
                                        <t t-esc="dashboard.number_waiting"/> Unpaid Invoices
                                    </a>

                                    <a type="object" t-if="journal_type == 'purchase'" name="open_action"
                                    context="{'search_default_open':1, 'search_default_posted':1, 'search_default_partial': 1}" id="account_dashboard_purchase_pay_link">
                                        <t t-esc="dashboard.number_waiting"/> Bills to Pay
                                    </a>
                                </div>
                                <div class="col-auto text-end">
                                    <span class="o_kanban_monetary"><t t-esc="dashboard.sum_waiting"/></span>
                                </div>
                            </div>
                            <div class="row" t-if="dashboard.number_late">
                                <div class="col overflow-hidden text-start">
                                    <a type="object" name="open_action" context="{'search_default_late': '1'}">
                                        <span t-if="journal_type == 'sale'" title="Late Invoices"><t t-esc="dashboard.number_late"/> Late Invoices</span>
                                        <span t-if="journal_type == 'purchase'" title="Late Bills"><t t-esc="dashboard.number_late"/> Late Bills</span>
                                    </a>
                                </div>
                                <div class="col-auto text-end">
                                    <span class="o_kanban_monetary"><t t-esc="dashboard.sum_late"/></span>
                                </div>
                            </div>
                            <t t-if="dashboard.number_to_check > 0">
                                <div class="row">
                                    <div class="col overflow-hidden text-start">
                                        <a type="object" name="open_action" context="{'search_default_to_check': True}"><t t-esc="dashboard.number_to_check"/> to check</a>
                                    </div>
                                    <div class="col-auto text-end">
                                        <span class="o_kanban_monetary"><t t-esc="dashboard.to_check_balance"/></span>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </t>
                    <t t-name="JournalBodyGraph">
                        <field name="kanban_dashboard_graph" t-att-graph_type="['cash','bank'].includes(journal_type) ? 'line' : 'bar'" widget="dashboard_graph"/>
                    </t>
            </templates>
            </kanban>
        </field>
    </record>

    <record id="open_account_journal_dashboard_kanban" model="ir.actions.act_window">
        <field name="name">Accounting Dashboard</field>
        <field name="res_model">account.journal</field>
        <field name="view_mode">kanban,form</field>
        <field name="view_id" ref="account_journal_dashboard_kanban_view"/>
        <field name="usage">menu</field>
        <field name="context">{'search_default_dashboard':1}</field>
        <field name="domain">[]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_neutral_face">
                This is the accounting dashboard
            </p><p>
                If you have not installed a chart of account, please install one first.<br/>
               <a class="btn-link" type="action" name="%(open_account_charts_modules)d" tabindex="-1">Browse available countries.</a>
            </p>
        </field>
    </record>

</odoo>
