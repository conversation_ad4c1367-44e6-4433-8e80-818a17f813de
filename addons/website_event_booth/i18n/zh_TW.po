# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_booth
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<i class=\"fa fa-exclamation-triangle me-2\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                    <span class=\"o_wbooth_registration_error_message\"/>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle me-2\" role=\"img\" aria-label=\"錯誤\" title=\"錯誤\"/>\n"
"                    <span class=\"o_wbooth_registration_error_message\"/>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                    <span>Sorry, several booths are now sold out. Please change your choices before validating again.</span>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                    <span>對不起，現在攤位都賣完了。請於再次驗證之前更改您的選擇。</span></i>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid ""
"<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event booths\"/><em>Configure Booths</em>"
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid ""
"<span class=\"alert alert-info\">This event is finished. It's no longer "
"possible to book a booth.</span>"
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span class=\"text-nowrap\">Sold Out</span>"
msgstr "<span class=\"text-nowrap\">售完</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span>Book my Booths</span>"
msgstr "<span>預訂我的攤位</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Email</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>電子郵件</span>\n"
"                            <span>*</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Name</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>姓名</span>\n"
"                            <span>*</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "<strong>Contact Details</strong>"
msgstr "<strong>聯絡資料</strong>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Book my Booths"
msgstr "預訂我的攤位"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_type_view_form
msgid "Booth Menu Item"
msgstr "攤位清單項目"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu
msgid "Booth Register"
msgstr "攤位註冊"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/xml/event_booth_registration_templates.xml:0
#, python-format
msgid "Booth Registration completed!"
msgstr "攤位註冊完成！"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
#, python-format
msgid "Booth registration failed."
msgstr "展位註冊失敗。"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Booths"
msgstr "攤位"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_type__booth_menu
msgid "Booths on Website"
msgstr "網站攤位"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Contact Us"
msgstr "聯絡我們"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_event
msgid "Event"
msgstr "活動"

#. module: website_event_booth
#: model:ir.model.fields.selection,name:website_event_booth.selection__website_event_menu__menu_type__booth
msgid "Event Booth Menus"
msgstr "活動攤位清單"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu_ids
msgid "Event Booths Menus"
msgstr "活動攤位清單"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_type
msgid "Event Template"
msgstr "活動模板"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__exhibition_map
msgid "Exhibition Map"
msgstr "展覽地圖"

#. module: website_event_booth
#. odoo-python
#: code:addons/website_event_booth/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
#, python-format
msgid "Get A Booth"
msgstr "預定攤位"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
#, python-format
msgid "It looks like your email is linked to an existing account."
msgstr "你的電郵地址似乎已有現有帳戶使用。"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "選單類型"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Mobile"
msgstr "手機"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Phone"
msgstr "電話"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Please Sign In."
msgstr "請登入。"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
#, python-format
msgid "Please fill out the form correctly."
msgstr "請正確填寫表格。"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Sorry, all the booths are sold out."
msgstr "對不起，所有的攤位都賣完了。"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
#, python-format
msgid "The booth category doesn't exist."
msgstr "沒有這個展位類別。"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "This event is not open to exhibitors registration,"
msgstr "本次活動不接受參展商註冊，"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "View Floor Plan"
msgstr "查看平面圖"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_website_event_menu
msgid "Website Event Menu"
msgstr "網站活動功能表"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "check our"
msgstr "查看我們的"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "for this event."
msgstr "此活動"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "if you have any question."
msgstr "如果你有任何問題。"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "list of future events"
msgstr "未來活動列表"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "you can"
msgstr ""
