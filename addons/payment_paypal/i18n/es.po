# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_paypal
# 
# Translators:
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Spanish (https://www.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"<br/><br/>\n"
"                Thanks,<br/>\n"
"                <b>The Odoo Team</b>"
msgstr ""

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment.py:0
#, python-format
msgid "Add your Paypal account to Odoo"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_email_account
msgid "Email"
msgstr "Correo electrónico"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__fees_dom_fixed
msgid "Fixed domestic fees"
msgstr "Comisiones nacionales fijas"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__fees_int_fixed
msgid "Fixed international fees"
msgstr "Comisiones internacionales fijas"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"Hello,\n"
"                <br/><br/>\n"
"                You have received a payment through PayPal.<br/>\n"
"                Kindly follow the instructions given by PayPal to create your account.<br/>\n"
"                Then, help us complete your Paypal credentials in Odoo.<br/><br/>"
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.acquirer_form_paypal
msgid "How to configure your paypal account?"
msgstr "¿Cómo configurar su cuenta de Paypal?"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_seller_account
msgid "Merchant Account ID"
msgstr "ID de la cuenta del comerciante"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "Token de identidad PDT"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Método de pago"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_pdt_token
msgid ""
"Payment Data Transfer allows you to receive notification of successful "
"payments as they are made."
msgstr ""
"La transferencia de datos de pago le permite recibir notificación de los "
"pagos realizados cuando estos se han hecho."

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_paypal
#: model:ir.model.fields.selection,name:payment_paypal.selection__payment_acquirer__provider__paypal
msgid "Paypal"
msgstr "Paypal"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Paypal Instant Payment Notification"
msgstr "Notificación de pago instantáneo de Paypal"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment.py:0
#, python-format
msgid "Paypal: received data with missing reference (%s) or txn_id (%s)"
msgstr "Paypal: received data with missing reference (%s) or txn_id (%s)"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__provider
msgid "Provider"
msgstr "Proveedor"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid "Set Paypal credentials"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_seller_account
msgid ""
"The Merchant ID is used to ensure communications coming from Paypal are "
"valid and secured."
msgstr ""
"El ID de comerciante  se usa para asegurar que las comunicaciones que "
"provienen de Paypal son válidas y seguras."

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction__paypal_txn_type
msgid "Transaction type"
msgstr "Tipo de transacción"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Use IPN"
msgstr "Usar IPN"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr "Comisiones nacionales variables (en porcentaje)"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__fees_int_var
msgid "Variable international fees (in percents)"
msgstr "Comisiones internacionales variables (en porcentaje)"
