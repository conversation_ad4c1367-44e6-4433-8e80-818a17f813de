<?xml version="1.0"?>
<odoo noupdate="1">
    <record id="base.user_demo" model="res.users">
        <field name="groups_id" eval="[(4, ref('hr_recruitment.group_hr_recruitment_user'))]"/>
    </record>

    <!--Manage the job_id to get in hr.applicant-->
    <record id="hr.job_developer" model="hr.job">
        <field name="no_of_recruitment">4</field>
        <field name="no_of_hired_employee">56</field>
        <field name="user_id" ref="base.user_admin" />
    </record>
    <record id="hr.job_ceo" model="hr.job">
        <field name="no_of_hired_employee">1</field>
    </record>
    <record id="hr.job_cto" model="hr.job">
        <field name="no_of_hired_employee">1</field>
        <field name="user_id" ref="base.user_admin" />
    </record>
    <record id="hr.job_consultant" model="hr.job">
        <field name="no_of_recruitment">1</field>
        <field name="no_of_hired_employee">17</field>
        <field name="user_id" ref="base.user_demo" />
    </record>
    <record id="hr.job_hrm" model="hr.job">
        <field name="no_of_recruitment">1</field>
        <field name="no_of_hired_employee">5</field>
    </record>
    <record id="hr.job_marketing" model="hr.job">
        <field name="no_of_recruitment">3</field>
        <field name="no_of_hired_employee">2</field>
        <field name="user_id" ref="base.user_demo" />
    </record>
    <record id="hr.job_trainee" model="hr.job">
        <field name="no_of_recruitment">6</field>
    </record>

    <record id="hr_recruitment_linkedin_developer" model="hr.recruitment.source">
        <field name="source_id" ref="utm.utm_source_linkedin"/>
        <field name="job_id" ref="hr.job_developer"/>
    </record>

    <record id="hr_recruitment_linkedin_ceo" model="hr.recruitment.source">
        <field name="source_id" ref="utm.utm_source_linkedin"/>
        <field name="job_id" ref="hr.job_ceo"/>
    </record>

    <record id="hr_recruitment_linkedin_cto" model="hr.recruitment.source">
        <field name="source_id" ref="utm.utm_source_linkedin"/>
        <field name="job_id" ref="hr.job_cto"/>
    </record>

    <record id="hr_recruitment_linkedin_consultant" model="hr.recruitment.source">
        <field name="source_id" ref="utm.utm_source_linkedin"/>
        <field name="job_id" ref="hr.job_consultant"/>
    </record>

    <record id="hr_recruitment_linkedin_hrm" model="hr.recruitment.source">
        <field name="source_id" ref="utm.utm_source_linkedin"/>
        <field name="job_id" ref="hr.job_hrm"/>
    </record>

    <record id="hr_recruitment_linkedin_marketing" model="hr.recruitment.source">
        <field name="source_id" ref="utm.utm_source_linkedin"/>
        <field name="job_id" ref="hr.job_marketing"/>
    </record>

    <record id="hr_recruitment_linkedin_trainee" model="hr.recruitment.source">
        <field name="source_id" ref="utm.utm_source_linkedin"/>
        <field name="job_id" ref="hr.job_trainee"/>
    </record>

    <record id="hr_case_salesman0" model="hr.applicant">
        <field name="name">Sales Manager</field>
        <field name="job_id" ref="hr.job_marketing"/>
        <field name="department_id" ref="hr.dep_sales"/>
        <field name="medium_id" ref="utm.utm_medium_direct"/>
        <field name="type_id" ref="degree_graduate"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_sales')])]"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="priority">1</field>
        <field name="partner_name">Enrique Jones</field>
        <field name="email_from"><EMAIL></field>
        <field name="partner_mobile">9963214587</field>
        <field name="stage_id" ref="stage_job2"/>
        <field name="create_date" eval="DateTime.now() - relativedelta(days=29)"/>
        <field name="date_last_stage_update" eval="(DateTime.today() - timedelta(days=27)).strftime('%Y-%m-%d')"/>
    </record>
    <record id="hr_case_salesman1" model="hr.applicant">
        <field name="name">Sales</field>
        <field name="job_id" ref="hr.job_marketing"/>
        <field name="department_id" ref="hr.dep_sales"/>
        <field name="type_id" ref="degree_graduate"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_sales')])]"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="priority">1</field>
        <field name="partner_name">Meldona Thang</field>
        <field name="email_from"><EMAIL></field>
        <field name="partner_mobile">998655451</field>
        <field name="stage_id" ref="stage_job1"/>
    </record>
    <record id="hr_case_dev0" model="hr.applicant">
        <field name="name">Developer PHP</field>
        <field name="job_id" ref="hr.job_developer"/>
        <field name="department_id" ref="hr.dep_rd"/>
        <field name="medium_id" ref="utm.utm_medium_email"/>
        <field name="type_id" ref="degree_graduate"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_it')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="priority">3</field>
        <field name="partner_name">Johan Duck</field>
        <field name="email_from"><EMAIL></field>
        <field name="partner_mobile">8955545</field>
        <field name="stage_id" ref="stage_job1"/>
    </record>
    <record id="hr_case_dev1" model="hr.applicant">
        <field name="name">Developer Fullstack</field>
        <field name="job_id" ref="hr.job_developer"/>
        <field name="department_id" ref="hr.dep_rd"/>
        <field name="type_id" ref="degree_graduate"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_it')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="priority">0</field>
        <field name="partner_name">Kelly Wallant</field>
        <field name="email_from"><EMAIL></field>
        <field name="partner_mobile">879895515</field>
        <field name="stage_id" ref="stage_job1"/>
    </record>
    <record id="hr_case_dev2" model="hr.applicant">
        <field name="name">Developer Python</field>
        <field name="job_id" ref="hr.job_developer"/>
        <field name="department_id" ref="hr.dep_rd"/>
        <field name="medium_id" ref="utm.utm_medium_email"/>
        <field name="type_id" ref="degree_graduate"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_it')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="priority">0</field>
        <field name="partner_name">Cécile Donth</field>
        <field name="email_from"><EMAIL></field>
        <field name="partner_mobile">98765411</field>
        <field name="stage_id" ref="stage_job1"/>
    </record>
    <record id="hr_case_dev3" model="hr.applicant">
        <field name="name">Developer C/C++</field>
        <field name="job_id" ref="hr.job_developer"/>
        <field name="department_id" ref="hr.dep_rd"/>
        <field name="type_id" ref="degree_graduate"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_it')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="priority">0</field>
        <field name="partner_name">Ohen Rizome</field>
        <field name="email_from"><EMAIL></field>
        <field name="partner_mobile">654687987654</field>
        <field name="stage_id" ref="stage_job1"/>
    </record>
    <record id="hr_case_traineemca0" model="hr.applicant">
        <field name="name">Trainee - MCA</field>
        <field name="job_id" ref="hr.job_trainee"/>
        <field name="department_id" ref="hr.dep_rd"/>
        <field name="type_id" ref="degree_licenced"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_manager')])]"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="priority">2</field>
        <field name="partner_name">Marie Justine</field>
        <field name="email_from"><EMAIL></field>
        <field name="partner_mobile">9988774455</field>
        <field name="stage_id" ref="stage_job4"/>
        <field name="partner_phone">6633225</field>
        <field name="create_date" eval="DateTime.now() - relativedelta(days=17)"/>
        <field name="date_last_stage_update" eval="(DateTime.today() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
    </record>
    <record id="hr_case_fresher0" model="hr.applicant">
        <field name="name">Fresher</field>
        <field name="job_id" ref="hr.job_trainee"/>
        <field name="department_id" ref="hr.dep_administration"/>
        <field name="type_id" ref="degree_bachelor"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_it')])]"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="priority">0</field>
        <field name="partner_name">Jose</field>
        <field name="email_from"><EMAIL></field>
        <field name="stage_id" ref="stage_job3"/>
        <field name="partner_phone">999666735</field>
    </record>
    <record id="hr_case_mkt0" model="hr.applicant">
        <field name="name">Marketing</field>
        <field name="job_id" ref="hr.job_marketing"/>
        <field name="department_id" ref="hr.dep_sales"/>
        <field name="type_id" ref="degree_graduate"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_manager')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="partner_name">Yin Lee</field>
        <field name="email_from"><EMAIL></field>
        <field name="stage_id" ref="stage_job1"/>
    </record>
    <record id="hr_case_mkt1" model="hr.applicant">
        <field name="name">Marketing 2 Year Experience</field>
        <field name="job_id" ref="hr.job_marketing"/>
        <field name="department_id" ref="hr.dep_sales"/>
        <field name="type_id" ref="degree_graduate"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_manager')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="partner_name">Hubert Blank</field>
        <field name="email_from"><EMAIL></field>
        <field name="priority">3</field>
        <field name="stage_id" ref="stage_job3"/>
    </record>
    <record id="hr_case_yrsexperienceinphp0" model="hr.applicant">
        <field name="name">Marketing Job</field>
        <field eval="(datetime.now()+relativedelta(months=-2)).strftime('%Y-%m-03 01:00:00')" name="create_date"/>
        <field name="job_id" ref="hr.job_marketing"/>
        <field name="department_id" ref="hr.dep_sales"/>
        <field name="type_id" ref="degree_graduate"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_manager')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="partner_name">John Bruno</field>
        <field name="email_from"><EMAIL></field>
        <field name="stage_id" ref="stage_job5"/>
        <field name="create_date" eval="DateTime.now() - relativedelta(days=61)"/>
        <field name="date_last_stage_update" eval="(DateTime.today() - timedelta(days=37)).strftime('%Y-%m-%d')"/>
    </record>
    <record id="hr_case_marketingjob0" model="hr.applicant">
        <field name="name">More than 5 yrs Experience in PHP</field>
        <field eval="(datetime.now()+relativedelta(months=-1)).strftime('%Y-%m-08 01:00:00')" name="create_date"/>
        <field name="job_id" ref="hr.job_developer"/>
        <field name="department_id" ref="hr.dep_rd"/>
        <field name="type_id" ref="degree_licenced"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_reserve')])]"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="partner_name">Sandra Elvis</field>
        <field name="email_from"><EMAIL></field>
        <field name="stage_id" ref="stage_job5"/>
        <field name="create_date" eval="DateTime.now() - relativedelta(days=34)"/>
        <field name="date_last_stage_update" eval="(DateTime.today() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
    </record>
    <record id="hr_case_financejob0" model="hr.applicant">
        <field name="name">Finance Manager</field>
        <field name="job_id" ref="hr.job_hrm"/>
        <field name="department_id" ref="hr.dep_administration"/>
        <field name="type_id" ref="degree_licenced"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_reserve')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="priority">1</field>
        <field name="partner_name">David Armstrong</field>
        <field name="email_from"><EMAIL></field>
        <field name="stage_id" ref="stage_job2"/>
        <field name="partner_phone">33968745</field>
    </record>
    <record id="hr_case_financejob1" model="hr.applicant">
        <field name="name">Finance</field>
        <field name="job_id" ref="hr.job_hrm"/>
        <field name="department_id" ref="hr.dep_administration"/>
        <field name="type_id" ref="degree_licenced"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_reserve')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="priority">1</field>
        <field name="partner_name">Joren Jacob</field>
        <field name="email_from"><EMAIL></field>
        <field name="stage_id" ref="stage_job2"/>
        <field name="create_date" eval="DateTime.now() - relativedelta(days=7)"/>
        <field name="date_last_stage_update" eval="(DateTime.today() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
    </record>
    <record id="hr_case_traineemca1" model="hr.applicant">
        <field name="name">Trainee - MCA</field>
        <field name="job_id" ref="hr.job_trainee"/>
        <field name="department_id" ref="hr.dep_rd"/>
        <field name="type_id" ref="degree_licenced"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_sales')])]"/>
        <field name="partner_name">Tina Augustie</field>
        <field name="email_from"><EMAIL></field>
        <field name="partner_mobile">9898745745</field>
        <field name="stage_id" ref="stage_job4"/>
        <field name="partner_phone">6630125</field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="create_date" eval="DateTime.now() - relativedelta(days=67)"/>
        <field name="date_last_stage_update" eval="(DateTime.today() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
    </record>
    <record id="hr_case_programmer" model="hr.applicant">
        <field name="name">Programmer</field>
        <field name="job_id" ref="hr.job_developer"/>
        <field name="department_id" ref="hr.dep_rd"/>
        <field name="type_id" ref="degree_licenced"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_it')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="partner_name">Shane Williams</field>
        <field name="email_from"><EMAIL></field>
        <field name="partner_mobile">9812398524</field>
        <field name="stage_id" ref="stage_job4"/>
        <field name="partner_phone">6630125</field>
        <field name="salary_expected">11000.0</field>
        <field name="create_date" eval="DateTime.now() - relativedelta(days=13)"/>
        <field name="date_last_stage_update" eval="(DateTime.today() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
    </record>
    <record id="hr_case_advertisement" model="hr.applicant">
        <field name="name">Advertisement</field>
        <field name="job_id" ref="hr.job_consultant"/>
        <field name="department_id" ref="hr.dep_ps"/>
        <field name="type_id" ref="degree_licenced"/>
        <field name="categ_ids" eval="[(6,0,[ref('tag_applicant_it')])]"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="partner_name">David Billy</field>
        <field name="email_from"><EMAIL></field>
        <field name="partner_mobile">9988774455</field>
        <field name="stage_id" ref="stage_job2"/>
        <field name="salary_expected">11000.0</field>
        <field name="create_date" eval="DateTime.now() - relativedelta(days=4)"/>
        <field name="date_last_stage_update" eval="(DateTime.today() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
    </record>

    <record id="hr_case_salesman0_cv" model="ir.attachment">
        <field name="name">Jones_CV.pdf</field>
        <field name="datas" type="base64" file="hr_recruitment/data/hr_recruitment_demo_jones_cv.pdf"></field>
        <field name="res_model">hr.applicant</field>
        <field name="res_id" ref="hr_recruitment.hr_case_salesman0"/>
    </record>
    <record id="hr_case_fresher0_cv" model="ir.attachment">
        <field name="name">Jose_CV.txt</field>
        <field name="datas" type="base64" file="hr_recruitment/data/hr_recruitment_demo_jose_cv.txt"></field>
        <field name="res_model">hr.applicant</field>
        <field name="res_id" ref="hr_recruitment.hr_case_fresher0"/>
    </record>
    <record id="hr_case_programmer_cv" model="ir.attachment">
        <field name="name">Williams_CV.doc</field>
        <field name="datas" type="base64" file="hr_recruitment/data/hr_recruitment_demo_williams_cv.doc"></field>
        <field name="res_model">hr.applicant</field>
        <field name="res_id" ref="hr_recruitment.hr_case_programmer"/>
    </record>

    <record id="message_application_demo" model="mail.message">
        <field name="model">hr.applicant</field>
        <field name="res_id" ref="hr_case_advertisement"/>
        <field name="body">Please do refer to this application for sure.</field>
        <field name="message_type">comment</field>
        <field name="author_id" ref="base.res_partner_2"/>
    </record>
    <record id="msg_case18_aplicant" model="mail.message">
        <field name="subject">Regarding reference</field>
        <field name="model">hr.applicant</field>
        <field name="res_id" ref="hr_case_advertisement"/>
        <field name="date" eval="DateTime.now() - relativedelta(days=3)"/>
        <field name="body" type="html">
            <p>Hello!<br />
            I will surely refer to this application as it is by your reference and <br />
            will try to conduct an interview within a very short time<br />
            Thanks,</p>
        </field>
        <field name="message_type">comment</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
        <field name="author_id" ref="base.partner_demo"/>
    </record>
    <function model="mail.message" name="toggle_message_starred"
            eval="[ref('msg_case18_aplicant')]"
    />
    <record id="msg_case_salesman0_aplicant" model="mail.message">
        <field name="subject">Refuse Application</field>
        <field name="model">hr.applicant</field>
        <field name="res_id" ref="hr_case_salesman0"/>
        <field name="body" type="html">
            <p>Hello,</p>
            <p>I have checked this application but it does not match with our requirements. We don't need to proceed further and we should refuse this application.</p>
            <p>Kind regards,</p>
        </field>
        <field name="message_type">comment</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
        <field name="author_id" ref="base.partner_demo"/>
    </record>
    <record id="msg_case_dev0_aplicant" model="mail.message">
        <field name="subject">Refuse Application</field>
        <field name="model">hr.applicant</field>
        <field name="res_id" ref="hr_case_dev0"/>
        <field name="body" type="html">
            <p>Hello,</p>
            <p>This applicant has excellent skills and would greatly fit in the RD Team!</p>
            <p>Kind regards,</p>
        </field>
        <field name="message_type">comment</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
        <field name="author_id" ref="base.partner_demo"/>
    </record>
    <record id="msg_case_fresher0_aplicant" model="mail.message">
        <field name="model">hr.applicant</field>
        <field name="res_id" ref="hr_case_fresher0"/>
        <field name="body" type="html">
            <p>Hello,</p>
            <p>We should move further for this application as early as possible.</p>
            <p>Kind regards,</p>
        </field>
        <field name="message_type">comment</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
        <field name="author_id" ref="base.partner_demo"/>
    </record>
    <record id="msg_case_advertisement_aplicant" model="mail.message">
        <field name="model">hr.applicant</field>
        <field name="res_id" ref="hr_case_advertisement"/>
        <field name="body" type="html">
            <p>Hello,</p>
            <p>The first interview was good. Skilled and open minded applicant.</p>
            <p>I think we should consider hiring him.</p>
            <p>Kind regards,</p>
        </field>
        <field name="message_type">comment</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
        <field name="author_id" ref="base.partner_demo"/>
    </record>
    <record id="msg_case_mkt1_1" model="mail.message">
        <field name="model">hr.applicant</field>
        <field name="res_id" ref="hr_case_mkt1"/>
        <field name="body" type="html">
            <p>Hello,</p>
            <p>The first interview was good. I will propose a second interview</p>
            <p>Kind regards,</p>
        </field>
        <field name="message_type">comment</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
        <field name="author_id" ref="base.partner_demo"/>
    </record>
    <record id="msg_case_mkt1_2" model="mail.message">
        <field name="model">hr.applicant</field>
        <field name="res_id" ref="hr_case_mkt1"/>
        <field name="body" type="html">
            <p>Hello,</p>
            <p>After the second interview, I think we should consider hiring him.</p>
            <p>Kind regards,</p>
        </field>
        <field name="message_type">comment</field>
        <field name="subtype_id" ref="mail.mt_comment"/>
        <field name="author_id" ref="base.partner_admin"/>
    </record>
    <record id="mail_activity_0" model="mail.activity">
        <field name="res_id" ref="hr_recruitment.hr_case_dev0" />
        <field name="res_model_id" ref="model_hr_applicant"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_email" />
        <field name="date_deadline" eval="time.strftime('%Y-%m-27 18:15:00')"/>
        <field name="summary">Send mail regarding our interview</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="mail_activity_1" model="mail.activity">
        <field name="res_id" ref="hr_recruitment.hr_case_dev1" />
        <field name="res_model_id" ref="model_hr_applicant"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_email" />
        <field name="date_deadline" eval="time.strftime('%Y-%m-%d')"/>
        <field name="summary">Send mail for first interview</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="mail_activity_2" model="mail.activity">
        <field name="res_id" ref="hr_recruitment.hr_case_salesman0" />
        <field name="res_model_id" ref="model_hr_applicant"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_email" />
        <field name="date_deadline" eval="time.strftime('%Y-%m-15 18:15:00')"/>
        <field name="summary">Send mail regarding our interview</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="mail_activity_3" model="mail.activity">
        <field name="res_id" ref="hr_recruitment.hr_case_traineemca0" />
        <field name="res_model_id" ref="model_hr_applicant"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_call" />
        <field name="date_deadline" eval="time.strftime('%Y-%m-10 18:15:00')"/>
        <field name="summary">Call to define real needs about training</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="mail_activity_4" model="mail.activity">
        <field name="res_id" ref="hr_recruitment.hr_case_yrsexperienceinphp0" />
        <field name="res_model_id" ref="model_hr_applicant"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_call" />
        <field name="date_deadline" eval="time.strftime('%Y-%m-24 18:15:00')"/>
        <field name="summary">Call to define real needs about training</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="mail_activity_5" model="mail.activity">
        <field name="res_id" ref="hr_recruitment.hr_case_advertisement" />
        <field name="res_model_id" ref="model_hr_applicant"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_call" />
        <field name="date_deadline" eval="time.strftime('%Y-%m-26 18:15:00')"/>
        <field name="summary">Call to schedule a second interview</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="mail_activity_6" model="mail.activity">
        <field name="res_id" ref="hr_recruitment.hr_case_mkt1" />
        <field name="res_model_id" ref="model_hr_applicant"/>
        <field name="activity_type_id" ref="mail.mail_activity_data_call" />
        <field name="date_deadline" eval="time.strftime('%Y-%m-18 17:15:00')"/>
        <field name="summary">Call to propose a contract</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
</odoo>
