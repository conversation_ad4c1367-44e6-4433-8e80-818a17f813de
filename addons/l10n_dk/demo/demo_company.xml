<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_dk" model="res.partner">
        <field name="name">DK Company</field>
        <field name="vat">DK58403288</field>
        <field name="street">G</field>
        <field name="city">Aalborg</field>
        <field name="country_id" ref="base.dk"/>
        
        <field name="zip">9430</field>
        <field name="phone">+45 32 12 34 56</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.dkexample.com</field>
    </record>

    <record id="demo_company_dk" model="res.company">
        <field name="name">DK Company</field>
        <field name="partner_id" ref="partner_demo_company_dk"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_dk')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_dk.demo_company_dk'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_dk.dk_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_dk.demo_company_dk')"/>
    </function>
</odoo>
