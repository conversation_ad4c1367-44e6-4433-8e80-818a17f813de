# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* onboarding
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__progress_ids
msgid "All Onboarding Progress Records (across companies)."
msgstr "Усі записи про хід адаптації (у всіх компаніях)."

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__progress_ids
msgid "All related Onboarding Progress Step Records (across companies)"
msgstr "Усі пов’язані записи кроків адаптації (в різних компаніях)"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_form
msgid "Background color"
msgstr "Фоновий колір"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__panel_background_color__blue
msgid "Blue"
msgstr "Синій"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__button_text
msgid "Button text"
msgstr "Текст кнопки"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__panel_close_action_name
msgid "Closing action"
msgstr "Дія закриття"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__panel_background_color
msgid "Color gradient added to the panel's background."
msgstr "Граждієнт кольору додано до фонової панелі."

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__company_id
msgid "Company"
msgstr "Компанія"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__current_onboarding_state
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__current_step_state
msgid "Completion State"
msgstr "Статус завершення"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__create_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__create_uid
msgid "Created by"
msgstr "Створив"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__create_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__create_date
msgid "Created on"
msgstr "Створено"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__description
msgid "Description"
msgstr "Опис"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__display_name
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__done
msgid "Done"
msgstr "Виконано"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__done_icon
msgid "Font Awesome Icon when completed"
msgstr "Іконка з чудовим шрифтом при завершенні"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__id
msgid "ID"
msgstr "ID"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__just_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__just_done
msgid "Just done"
msgstr "Щойно завершено"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding____last_update
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step____last_update
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress____last_update
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step____last_update
msgid "Last Modified on"
msgstr "Остання модифікація"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__write_uid
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__write_date
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: onboarding
#: code:addons/onboarding/models/onboarding_step.py:0
#, python-format
msgid "Let's do it"
msgstr "Давайте зробимо це"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__name
msgid "Name of the onboarding"
msgstr "Назва залучення"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__panel_close_action_name
msgid "Name of the onboarding model action to execute when closing the panel."
msgstr "Назва дії моделі залучення для виключення під час закриття панелі."

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__panel_step_open_action_name
msgid ""
"Name of the onboarding step model action to execute when opening the step, "
"e.g. action_open_onboarding_1_step_1"
msgstr ""
"Назва дії моделі етапу адаптації для вилучення, що відкриває крок, e.g. "
"action_open_onboarding_1_step_1"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__panel_background_color__none
msgid "None"
msgstr "Немає"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__current_onboarding_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding_step__current_step_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress__onboarding_state__not_done
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_progress_step__step_state__not_done
msgid "Not done"
msgstr "Не завершено"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__onboarding_id
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__onboarding_id
msgid "Onboarding"
msgstr "Залучення"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__current_progress_id
msgid "Onboarding Progress"
msgstr "Прогрес адаптації"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__progress_ids
msgid "Onboarding Progress Records"
msgstr "Записи прогресу адаптації"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__progress_ids
msgid "Onboarding Progress Step Records"
msgstr "Записи кроку прогресу адаптації"

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_progress_step
msgid "Onboarding Progress Step Tracker"
msgstr "Трекер кроку прогресу адаптації"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__current_progress_step_id
msgid "Onboarding Progress Step for the current context (company)."
msgstr "Крок прогресу адаптації для поточного контексту (компанії)."

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_progress
msgid "Onboarding Progress Tracker"
msgstr "Трекер прогресу адаптації"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding__current_progress_id
msgid "Onboarding Progress for the current context (company)."
msgstr "Прогрес адаптації для поточного контексту (компанії)."

#. module: onboarding
#: model:ir.model,name:onboarding.model_onboarding_onboarding_step
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__step_id
msgid "Onboarding Step"
msgstr "Крок залучення"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__step_state
msgid "Onboarding Step Progress"
msgstr "Прогрес кроку адаптації"

#. module: onboarding
#: model:ir.actions.act_window,name:onboarding.action_view_onboarding_step
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_step_view_tree
msgid "Onboarding Steps"
msgstr "Кроки адаптації"

#. module: onboarding
#: model:ir.model.constraint,message:onboarding.constraint_onboarding_onboarding_route_name_uniq
msgid "Onboarding alias must be unique."
msgstr "Псевдонім адаптації має бути унікальним."

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__onboarding_state
msgid "Onboarding progress"
msgstr "Прогрес адаптації"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__step_ids
msgid "Onboarding steps"
msgstr "Кроки адаптації"

#. module: onboarding
#: model:ir.actions.act_window,name:onboarding.action_view_onboarding_onboarding
#: model:ir.ui.menu,name:onboarding.menu_onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_tree
msgid "Onboardings"
msgstr "Адаптації"

#. module: onboarding
#: model:ir.ui.menu,name:onboarding.menu_onboarding_step
msgid "Onboardings Steps"
msgstr "Кроки адаптацій"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__route_name
msgid "One word name"
msgstr "Назва одним словом"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__panel_step_open_action_name
msgid "Opening action"
msgstr "Дія відкриття"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__panel_background_color__orange
msgid "Orange"
msgstr "Помаранчевий"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__panel_background_color
msgid "Panel's Background color"
msgstr "Фоновий колір панелі"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__panel_background_image
msgid "Panel's background image"
msgstr "Фонове зображення панелі"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__progress_step_ids
msgid "Progress Steps Trackers"
msgstr "Трекери кроків прогресу"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress_step__progress_id
msgid "Related Onboarding Progress Tracker"
msgstr "Трекер пов'язаного прогресу адаптації"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__onboarding_id
msgid "Related onboarding tracked"
msgstr "Пов'язану адаптацію відстежено"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__sequence
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__sequence
msgid "Sequence"
msgstr "Послідовність"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__is_per_company
msgid "Should be done per company?"
msgstr "Має бути виконано на компанію?"

#. module: onboarding
#: code:addons/onboarding/models/onboarding_step.py:0
#, python-format
msgid "Step Completed! - Click to review"
msgstr "Крок завершено! - Натисніть для перегляду"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__current_progress_step_id
msgid "Step Progress"
msgstr "Прогрес кроку"

#. module: onboarding
#: model:ir.model.fields,help:onboarding.field_onboarding_onboarding_step__button_text
msgid "Text on the panel's button to start this step"
msgstr "Текст на кнопці панелі для старту цього кроку"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__done_text
msgid "Text to show when step is completed"
msgstr "Текст для показу, коли цей крок завершено"

#. module: onboarding
#: model:ir.model.constraint,message:onboarding.constraint_onboarding_progress_onboarding_company_uniq
msgid ""
"There cannot be multiple records of the same onboarding completion for the "
"same company."
msgstr ""
"Не може бути кількох записів про те саме завершення адаптації для однієї "
"компанії."

#. module: onboarding
#: model:ir.model.constraint,message:onboarding.constraint_onboarding_progress_step_progress_step_uniq
msgid ""
"There cannot be multiple records of the same onboarding step completion for "
"the same Progress record."
msgstr ""
"Для одного запису прогресу не може бути кількох записів про виконання одного"
" кроку адаптації."

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding_step__title
msgid "Title"
msgstr "Заголовок"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_form
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_onboarding_view_tree
msgid "Toggle visibility"
msgstr "Перемкнути видимість"

#. module: onboarding
#: model:ir.model.fields.selection,name:onboarding.selection__onboarding_onboarding__panel_background_color__violet
msgid "Violet"
msgstr "Фіолетовий"

#. module: onboarding
#: model:ir.model.fields,field_description:onboarding.field_onboarding_onboarding__is_onboarding_closed
#: model:ir.model.fields,field_description:onboarding.field_onboarding_progress__is_onboarding_closed
msgid "Was panel closed?"
msgstr "Панель була закрита?"

#. module: onboarding
#: model_terms:ir.ui.view,arch_db:onboarding.onboarding_panel
msgid "onboarding.onboarding.step"
msgstr "onboarding.onboarding.step"
