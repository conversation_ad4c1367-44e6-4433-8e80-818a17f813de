# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_configurator
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: Jeffery CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "<i class=\"fa fa-shopping-cart add-optionnal-item\"/> Add to cart"
msgstr "<i class=\"fa fa-shopping-cart add-optionnal-item\"/>添加到购物车"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Price</span>"
msgstr "<span class=\"label\">单价</span>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Product</span>"
msgstr "<span class=\"label\">产品</span>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Quantity</span>"
msgstr "<span class=\"label\">数量</span>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<strong>Total:</strong>"
msgstr "<strong>总计:</strong>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_quantity_config
msgid "Add one"
msgstr "添加一行"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "属性值"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "Available Options:"
msgstr "有效选项："

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/sale_product_field.js:0
#, python-format
msgid "Back"
msgstr "回退"

#. module: sale_product_configurator
#: model:product.template,name:sale_product_configurator.product_product_1_product_template
msgid "Chair floor protection"
msgstr "椅子地板保护"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/sale_product_field.js:0
#, python-format
msgid "Configure"
msgstr "配置"

#. module: sale_product_configurator
#. odoo-javascript
#: code:addons/sale_product_configurator/static/src/js/sale_product_field.js:0
#, python-format
msgid "Confirm"
msgstr "确认"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "产品是否可配置？"

#. module: sale_product_configurator
#: model:product.template,description_sale:sale_product_configurator.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr "办公椅会伤害您的地板：保护它。"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "Option not available"
msgstr "选项无效"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr "可选产品"

#. module: sale_product_configurator
#: model:ir.model.fields,help:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,help:sale_product_configurator.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr "每当客户点击 * 加入购物车 *，就会出现建议的可选产品（交叉销售策略，例如，就电脑而言：保修、配套软件等）。"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_product_template
msgid "Product"
msgstr "产品"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "Product Image"
msgstr "产品图像"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_order_view_form
msgid "Product Variant"
msgstr "产品变体"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_template_view_form
msgid "Recommend when 'Adding to Cart' or quotation"
msgstr "在“添加到购物车”或报价时推荐"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_quantity_config
msgid "Remove one"
msgstr "移除一行"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_sale_order_line
msgid "Sales Order Line"
msgstr "销售订单行"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
msgid "This combination does not exist."
msgstr "此组合不存在。"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
msgid "This product has no valid combination."
msgstr "此产品没有有效的组合。"
