# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_recaptcha
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# V<PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: O<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Generate reCAPTCHA v3 keys"
msgstr "<i class=\"fa fa-arrow-right\"/> Luo reCAPTCHA v3 -avaimet"

#. module: google_recaptcha
#: model:ir.model.fields,help:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid ""
"By default, should be one of 0.1, 0.3, 0.7, 0.9.\n"
"1.0 is very likely a good interaction, 0.0 is very likely a bot"
msgstr ""
"Oletusarvoisesti sen pitäisi olla jokin seuraavista: 0.1, 0.3, 0.7, 0.9.\n"
"1.0 on hyvin todennäköisesti hyvä vuorovaikutus, 0.0 on hyvin todennäköisesti botti"

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: google_recaptcha
#: model:ir.model,name:google_recaptcha.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-reititys"

#. module: google_recaptcha
#: model_terms:ir.ui.view,arch_db:google_recaptcha.res_config_settings_view_form
msgid "If no keys are provided, no checks will be done."
msgstr "Jos avaimia ei aseteta, tarkastuksia ei tehdä."

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_min_score
msgid "Minimum score"
msgstr "Minimipisteet"

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
#, python-format
msgid "No recaptcha site key set."
msgstr "Recaptcha-sivuston avainta ei ole asetettu."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
#, python-format
msgid "Privacy Policy"
msgstr "Tietosuojakäytäntö"

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
#, python-format
msgid "Protected by reCAPTCHA,"
msgstr "Suojattu reCAPTCHA:lla,"

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_private_key
msgid "Secret Key"
msgstr "Salainen avain"

#. module: google_recaptcha
#: model:ir.model.fields,field_description:google_recaptcha.field_res_config_settings__recaptcha_public_key
msgid "Site Key"
msgstr "Sivuston avain"

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
#, python-format
msgid "Terms of Service"
msgstr "Käyttöehdot"

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
#, python-format
msgid "The reCaptcha private key is invalid."
msgstr "ReCaptchan yksityinen avain on virheellinen."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
#, python-format
msgid "The reCaptcha token is invalid."
msgstr "ReCaptchan päästytunniste on virheellinen."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/js/recaptcha.js:0
#, python-format
msgid "The recaptcha site key is invalid."
msgstr "Recaptcha-sivuston avain on virheellinen."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
#, python-format
msgid "The request is invalid or malformed."
msgstr "Pyyntö on virheellinen tai väärin muotoiltu."

#. module: google_recaptcha
#. odoo-python
#: code:addons/google_recaptcha/models/ir_http.py:0
#, python-format
msgid "Your request has timed out, please retry."
msgstr "Pyyntösi on päättynyt, yritä uudelleen."

#. module: google_recaptcha
#. odoo-javascript
#: code:addons/google_recaptcha/static/src/xml/recaptcha.xml:0
#, python-format
msgid "apply."
msgstr "käytä."
