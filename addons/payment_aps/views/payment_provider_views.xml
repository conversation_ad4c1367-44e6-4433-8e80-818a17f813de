<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">APS Provider Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <group name="provider_credentials" position='inside'>
                <group attrs="{'invisible': [('code', '!=', 'aps')]}">
                    <field name="aps_merchant_identifier"
                           string="Merchant Identifier"
                           attrs="{'required': [('code', '=', 'aps'), ('state', '!=', 'disabled')]}"/>
                    <field name="aps_access_code"
                           string="Access Code"
                           attrs="{'required': [('code', '=', 'aps'), ('state', '!=', 'disabled')]}"
                           password="True"/>
                    <field name="aps_sha_request"
                           string="SHA Request Phrase"
                           attrs="{'required': [('code', '=', 'aps'), ('state', '!=', 'disabled')]}"
                           password="True"/>
                    <field name="aps_sha_response"
                           string="SHA Response Phrase"
                           attrs="{'required': [('code', '=', 'aps'), ('state', '!=', 'disabled')]}"
                           password="True"/>
                </group>
            </group>
        </field>
    </record>

</odoo>
