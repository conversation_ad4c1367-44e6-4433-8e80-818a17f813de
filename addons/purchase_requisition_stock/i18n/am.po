# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_requisition_stock
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:01+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Language-Team: Amharic (https://app.transifex.com/odoo/teams/41243/am/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: am\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition_line__move_dest_id
msgid "Downstream Move"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_order__on_time_rate_perc
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_order_line__on_time_rate_perc
msgid "OTD"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__picking_type_id
msgid "Operation Type"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_order
msgid "Purchase Order"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_order_line
msgid "Purchase Order Line"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition
msgid "Purchase Requisition"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition_line
msgid "Purchase Requisition Line"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_move__requisition_line_ids
msgid "Requisition Line"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_rule
msgid "Stock Rule"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__warehouse_id
msgid "Warehouse"
msgstr ""

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition_create_alternative
msgid "Wizard to preset values for alternative PO"
msgstr ""
