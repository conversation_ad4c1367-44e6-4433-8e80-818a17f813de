# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_sale
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid ""
"<i class=\"fa fa-ban me-2\"/>\n"
"                                            Unpublished"
msgstr ""
"<i class=\"fa fa-ban me-2\"/>\n"
"                                            Sin publicar"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid ""
"<small class=\"o_wevent_participating text-bg-success\">\n"
"                                            <i class=\"fa fa-check me-2\"/>\n"
"                                            Registered\n"
"                                        </small>"
msgstr ""
"<small class=\"o_wevent_participating text-bg-success\">\n"
"                                            <i class=\"fa fa-check me-2\"/>\n"
"                                            Registrado\n"
"                                        </small>"

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity cannot be applied to this "
"event tickets product."
msgstr ""
"No se puede aplicar un artículo de la lista de precios con una cantidad "
"mínima positiva a este tipo de boletos."

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity will not be applied to the "
"event tickets products."
msgstr ""
"No se aplicará un artículo de la lista de precios con una cantidad mínima "
"positiva a este tipo de boletos."

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_event_sale_report
msgid "Event Sales Report"
msgstr "Reporte de ventas de eventos"

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "Boletos del evento"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "Free"
msgstr "Gratis"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "From"
msgstr "Desde"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "Go to Event"
msgstr "Ir al evento"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "Regla de la lista de precios"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_template
msgid "Product"
msgstr "Producto"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_product
msgid "Product Variant"
msgstr "Variante de producto"

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_event_sale_report__is_published
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_sale_report_view_search
msgid "Published Events"
msgstr "Eventos publicados"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order
msgid "Sales Order"
msgstr "Orden de venta"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de la orden de venta"

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "Sorry, The %(ticket)s tickets for the %(event)s event are sold out."
msgstr ""
"Lo sentimos, los boletos %(ticket)s para el evento %(event)s están agotados."

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid ""
"Sorry, only %(remaining_seats)d seats are still available for the %(ticket)s"
" ticket for the %(event)s event."
msgstr ""
"Lo sentimos, solo quedan %(remaining_seats)d asientos disponibles para el "
"boleto%(ticket)s del evento %(event)s."

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "The provided ticket doesn't exist"
msgstr "El boleto proporcionado no existe"

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "The ticket doesn't match with this product."
msgstr "El tipo de boleto no coincide con este producto."

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid "Warning"
msgstr "Alerta"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "We are looking forward to meeting you at the following"
msgstr "Esperamos verle en el siguiente"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_website
msgid "Website"
msgstr "Sitio web"

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "You cannot raise manually the event ticket quantity in your cart"
msgstr ""
"No puede aumentar de forma manual la cantidad de boletos para el evento en "
"su carrito"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "event"
msgstr "evento"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "events"
msgstr "eventos"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "to"
msgstr "a"
