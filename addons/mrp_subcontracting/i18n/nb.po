# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# Rune Restad, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:27+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Alle"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Stykkliste oversiktsrapport"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_bom
msgid "Bill of Material"
msgstr "Stykkliste"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__type
msgid "BoM Type"
msgstr "Stykkliste type"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__bom_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__bom_ids
msgid "BoMs for which the Partner is one of the subcontractors"
msgstr "BoMs der partneren er en av underleverandørene."

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__bom_product_ids
msgid "Bom Product"
msgstr "BoM produkt"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_product_template_search_view
msgid "Can be Subcontracted"
msgstr "Kan underleveres"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_change_production_qty
msgid "Change Production Qty"
msgstr "Endre produksjons kvantum"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_stock_location__is_subcontracting_location
msgid ""
"Check this box to create a new dedicated subcontracting location for this "
"company. Note that standard subcontracting routes will be adapted so as to "
"take these into account automatically."
msgstr ""
"Merk av denne boksen for å opprette en ny dedikert underleveringslokasjon "
"for dette selskapet. Vær oppmerksom på at standard underleveringsruter vil "
"bli tilpasset for automatisk å ta hensyn til dette."

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid ""
"Choose a vendor of type subcontractor if you want to subcontract the product"
msgstr ""
"Velg en leverandør av typen underleverandør hvis du ønsker å underlevere "
"produktet."

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Consumed"
msgstr "Brukt"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Continue"
msgstr "Fortsett"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Deadline Date"
msgstr "Frist dato"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Demand"
msgstr "Etterspørsel"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Description"
msgstr "Beskrivelse"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__move_line_raw_ids
msgid "Detail Component"
msgstr "Detalj komponent"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Details"
msgstr "Detaljer"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Discard"
msgstr "Avbryt"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__display_action_record_components
msgid "Display Action Record Components"
msgstr "Vis handling for å registrere komponenter."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
#, python-format
msgid "Done"
msgstr "Fullført"

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__facultative
msgid "Facultative"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__subcontracting_has_been_recorded
msgid "Has been recorded?"
msgstr "Har den blitt lagret?"

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__hide
msgid "Hide"
msgstr "Skjul"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_location.py:0
#, python-format
msgid ""
"In order to manage stock accurately, subcontracting locations must be type "
"Internal, linked to the appropriate company."
msgstr ""
"For å administrere lager nøyaktig må underleveringslokasjoner være av typen "
"Intern, knyttet til riktig selskap."

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_filter
msgid "Incoming transfer"
msgstr "Innkommende overføring"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_location
msgid "Inventory Locations"
msgstr "Lagerlokasjoner"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_quant__is_subcontract
msgid "Is Subcontract"
msgstr "Er underleveranse"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_location__is_subcontracting_location
msgid "Is a Subcontracting Location?"
msgstr "Er lokasjon for underleveranse"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_mrp_production__bom_product_ids
msgid ""
"List of Products used in the BoM, used to filter the list of products in the"
" subcontracting portal view"
msgstr ""
"Liste over produkter brukt i materiallisten, brukt for å filtrere listen "
"over produkter i underleverandørportalen."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
#, python-format
msgid "Locations to update"
msgstr "Lokalisasjoner å oppdatere"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__production_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__production_ids
msgid "MRP Productions for which the Partner is the subcontractor"
msgstr "MRP-produksjoner der partneren er underleverandøren."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "Lag på bestilling "

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move_line.py:0
#, python-format
msgid ""
"Make sure you validate or adapt the related resupply picking to your "
"subcontractor in order to avoid inconsistencies in your stock."
msgstr ""
"Sørg for å validere eller tilpasse den relaterte påfyllingsplukkingen til "
"din underleverandør for å unngå inkonsekvenser i lageret ditt."

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__mandatory
msgid "Mandatory"
msgstr "Obligatorisk"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Manufacturing Orders"
msgstr "Produksjonsordre"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Manufacturing Reference"
msgstr "Produksjonsreferanse"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__move_line_nosuggest_ids
msgid "Move Line Nosuggest"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "Navn"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Nyeste"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
#, python-format
msgid "Nothing to record"
msgstr "Ingenting å lagre"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_quant.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operasjon ikke støttet"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Operations"
msgstr "Operasjoner"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "Operasjoner uten pakke"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Order"
msgstr "Ordre"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid ""
"Portal users cannot create a stock move with a state 'Done' or change the "
"current state to 'Done'."
msgstr ""
"Portalbrukere kan ikke opprette en lagerbevegelse med status 'Ferdig' eller "
"endre den nåværende statusen til 'Ferdig'."

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Produktbevegelser (Lagerbevegelseslinje)"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_product
msgid "Product Variant"
msgstr "Produktvariant"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_production
msgid "Production Order"
msgstr "Produksjonsordre"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_home_menu_production
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_home_productions
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Productions"
msgstr "Produksjoner"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_quant
msgid "Quants"
msgstr "Antall"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "Raw Materials for %s"
msgstr "Råmaterialer for %s"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#, python-format
msgid "Ready"
msgstr "Klar"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Record Production"
msgstr "Lagre produksjon"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Record components"
msgstr "Lagre komponenter"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Register components for subcontracted product"
msgstr "Registrer komponenter for underleverte produkter."

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Reserved"
msgstr "Reservert"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_route_id
#, python-format
msgid "Resupply Subcontractor"
msgstr "Påfylling av underleverandør."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:stock.route,name:mrp_subcontracting.route_resupply_subcontractor_mto
#, python-format
msgid "Resupply Subcontractor on Order"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_to_resupply
msgid "Resupply Subcontractors"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_return_picking
msgid "Return Picking"
msgstr "Returplukk"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Scheduled Date"
msgstr "Planlagt dato"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Sequence Resupply Subcontractor"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Sequence subcontracting"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__show_subcontracting_details_visible
msgid "Show Subcontracting Details Visible"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Source Document"
msgstr "Kildedokument"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "State"
msgstr "Modus"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move
msgid "Stock Move"
msgstr "Lagerbevegelse"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__picking_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__picking_ids
msgid "Stock Pickings for which the Partner is the subcontractor"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_rule
msgid "Stock Rule"
msgstr "Regel lagring"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "Subcontract"
msgstr "Underleveranse"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking__subcontract_location_id
msgid "Subcontract Location"
msgstr "Lokasjon underleveranse"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid "Subcontracted"
msgstr "Underleveranse"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "Subcontracted manufacturing orders cannot be merged."
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__mrp_bom__type__subcontract
#, python-format
msgid "Subcontracting"
msgstr "Subkontrakt"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/res_company.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__subcontracting_location_id
#, python-format
msgid "Subcontracting Location"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.quant_subcontracting_search_view
msgid "Subcontracting Locations"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_mto_pull_id
msgid "Subcontracting MTO Rule"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_pull_id
msgid "Subcontracting MTS Rule"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_type_id
msgid "Subcontracting Operation Type"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.actions.act_window,name:mrp_subcontracting.subcontracting_portal_view_production_action
msgid "Subcontracting Portal"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_resupply_type_id
msgid "Subcontracting Resupply Operation Type"
msgstr ""

#. module: mrp_subcontracting
#. odoo-javascript
#: code:addons/mrp_subcontracting/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#, python-format
msgid "Subcontracting:"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/report/mrp_report_bom_structure.py:0
#, python-format
msgid "Subcontracting: %s"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__subcontractor_id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_location__subcontractor_ids
msgid "Subcontractor"
msgstr "Underleverandør"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid "Subcontractor Location"
msgstr "Underleverandør lokasjon"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__subcontractor_ids
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.view_partner_mrp_subcontracting_filter
msgid "Subcontractors"
msgstr "Underleverandør"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Leverandørprisliste"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__is_subcontract
msgid "The move is a subcontract receipt"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,help:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid ""
"The stock location used as source and destination when sending        goods "
"to this contact during a subcontracting process."
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "There are currently no productions for your account."
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
#, python-format
msgid ""
"There shouldn't be multiple productions to record for the same subcontracted"
" move."
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "This MO isn't related to a subcontracted move"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "To subcontract, use a planned transfer."
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_form_view
msgid "Total Consumed"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_picking
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__incoming_picking
msgid "Transfer"
msgstr "Overføring"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_stock_move_line_tree_view
msgid "Unit of Measure"
msgstr "Enhet"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_mrp_production__subcontractor_id
msgid "Used to restrict access to the portal user through Record Rules"
msgstr ""

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_warehouse
msgid "Warehouse"
msgstr "Lager"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""
"Veiviser i tilfelle forbruk er i advarsel/streng modus og flere komponenter "
"er brukt til en produksjonsordre (relatert til stykklisten)"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not set a Bill of Material with operations or by-product line as "
"subcontracting."
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_location.py:0
#, python-format
msgid "You cannot alter the company's subcontracting location"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You cannot write on fields %s in mrp.production."
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
#, python-format
msgid "You might want to update the locations of this transfer's operations"
msgstr ""
"Du kan vurdere å oppdatere lokasjonene for operasjonene i denne "
"overføringen."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You must enter a serial number for %s"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You must enter a serial number for each line of %s"
msgstr ""

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid ""
"You must indicate a non-zero amount consumed for at least one of your "
"components"
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "e.g. PO0032"
msgstr ""
