# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting
# 
# Translators:
# <PERSON>, 2022
# Ива<PERSON>оздов <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <yeli<PERSON><PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <koro<PERSON><PERSON>@gmail.com>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON>, 2023
# alenafairy, 2023
# Сергей Шеб<PERSON>ин <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:27+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Все"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Отчет по ведомости материалов"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_bom
msgid "Bill of Material"
msgstr "Спецификация"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__type
msgid "BoM Type"
msgstr "Тип спецификации"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__bom_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__bom_ids
msgid "BoMs for which the Partner is one of the subcontractors"
msgstr "BoM, для которых Партнер является одним из субподрядчиков"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__bom_product_ids
msgid "Bom Product"
msgstr "Продукт в спецификации"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_product_template_search_view
msgid "Can be Subcontracted"
msgstr "Может быть отдано на субподряд"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_change_production_qty
msgid "Change Production Qty"
msgstr "Изменить количество продукции"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_stock_location__is_subcontracting_location
msgid ""
"Check this box to create a new dedicated subcontracting location for this "
"company. Note that standard subcontracting routes will be adapted so as to "
"take these into account automatically."
msgstr ""
"Установите этот флажок, чтобы создать новое выделенное место субподряда для "
"этой компании. Обратите внимание, что стандартные маршруты субподряда будут "
"адаптированы таким образом, чтобы учитывать их автоматически."

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid ""
"Choose a vendor of type subcontractor if you want to subcontract the product"
msgstr ""
"Выберите поставщика типа \"Субподрядчик\", если вы хотите передать продукт "
"на субподряд"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Consumed"
msgstr "Использовано"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Continue"
msgstr "Далее"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Deadline Date"
msgstr "Крайняя дата"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Demand"
msgstr "Потребность"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Description"
msgstr "Описание"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__move_line_raw_ids
msgid "Detail Component"
msgstr "Детальный компонент"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Details"
msgstr "Подробная информация"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Discard"
msgstr "Отменить"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__display_action_record_components
msgid "Display Action Record Components"
msgstr "Отображение компонентов записи действия"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
#, python-format
msgid "Done"
msgstr "Выполнено"

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__facultative
msgid "Facultative"
msgstr "Факультативный"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__subcontracting_has_been_recorded
msgid "Has been recorded?"
msgstr "Записано?"

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__hide
msgid "Hide"
msgstr "Скрыть"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_location.py:0
#, python-format
msgid ""
"In order to manage stock accurately, subcontracting locations must be type "
"Internal, linked to the appropriate company."
msgstr ""
"Для точного учета запасов места субподряда должны быть типа «Склад» и "
"привязаны к нужной организации."

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_filter
msgid "Incoming transfer"
msgstr "Входящий перевод"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_location
msgid "Inventory Locations"
msgstr "Места запасов"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_quant__is_subcontract
msgid "Is Subcontract"
msgstr "Является ли субподрядчиком"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_location__is_subcontracting_location
msgid "Is a Subcontracting Location?"
msgstr "Является ли это местом заключения субподряда?"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_mrp_production__bom_product_ids
msgid ""
"List of Products used in the BoM, used to filter the list of products in the"
" subcontracting portal view"
msgstr ""
"Список продуктов, используемых в BoM, используется для фильтрации списка "
"продуктов в представлении портала субконтрактов"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
#, python-format
msgid "Locations to update"
msgstr " Места хранения для обновления"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__production_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__production_ids
msgid "MRP Productions for which the Partner is the subcontractor"
msgstr "MRP-производства, для которых Партнер является субподрядчиком"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "Сделать на Заказ"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move_line.py:0
#, python-format
msgid ""
"Make sure you validate or adapt the related resupply picking to your "
"subcontractor in order to avoid inconsistencies in your stock."
msgstr ""
"Убедитесь, что вы подтвердили или адаптировали соответствующую комплектацию "
"поставок для вашего субподрядчика, чтобы избежать несоответствий на складе."

#. module: mrp_subcontracting
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__stock_picking__display_action_record_components__mandatory
msgid "Mandatory"
msgstr "Обязательное"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Manufacturing Orders"
msgstr "Заявки на производство"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Manufacturing Reference"
msgstr "Производственная ссылка"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__move_line_nosuggest_ids
msgid "Move Line Nosuggest"
msgstr "Непереминний строку перемещения"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "Имя"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Последние"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
#, python-format
msgid "Nothing to record"
msgstr "Нечего записывать"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_quant.py:0
#, python-format
msgid "Operation not supported"
msgstr "Операция не поддерживается"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Operations"
msgstr "Операции"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "Операции без упаковки"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Order"
msgstr "Заказ"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid ""
"Portal users cannot create a stock move with a state 'Done' or change the "
"current state to 'Done'."
msgstr ""
"Пользователи портала не могут создать перемещение запасов с состоянием "
"\"Выполнено\" или изменить текущее состояние на \"Выполнено\"."

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Перемещение товара (позиция складского перемещения)"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_product
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_production
msgid "Production Order"
msgstr "Производственный заказ"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_home_menu_production
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_home_productions
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Productions"
msgstr "Производство"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_quant
msgid "Quants"
msgstr "Части"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "Raw Materials for %s"
msgstr "Сырье для %s"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/controllers/portal.py:0
#, python-format
msgid "Ready"
msgstr "Можно производить"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_production_subcontracting_form_view
msgid "Record Production"
msgstr "Запись производства"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
msgid "Record components"
msgstr "Компоненты записи"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.stock_picking_form_view
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "Register components for subcontracted product"
msgstr "Регистрация компонентов для субподрядной продукции"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_tree_view
msgid "Reserved"
msgstr "Зарезервировано"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_route_id
#, python-format
msgid "Resupply Subcontractor"
msgstr "Отгрузка субподрядчику"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:stock.route,name:mrp_subcontracting.route_resupply_subcontractor_mto
#, python-format
msgid "Resupply Subcontractor on Order"
msgstr "Отгрузка субподрядчику по заказу"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_to_resupply
msgid "Resupply Subcontractors"
msgstr "Отгрузка субподрядчикам"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_return_picking
msgid "Return Picking"
msgstr "Возвратная Комплектация"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Scheduled Date"
msgstr "Плановая дата"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Sequence Resupply Subcontractor"
msgstr "Нумерация для отгрузки субподрядчикам"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#, python-format
msgid "Sequence subcontracting"
msgstr "Последовательность субподрядных работ"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__show_subcontracting_details_visible
msgid "Show Subcontracting Details Visible"
msgstr "Показать видимые сведения о субподряде"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "Source Document"
msgstr "Документ-источник"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "State"
msgstr "Статус"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_move
msgid "Stock Move"
msgstr "Перемещение на складе"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__picking_ids
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__picking_ids
msgid "Stock Pickings for which the Partner is the subcontractor"
msgstr "Комплектование запасов, по которым Партнер является субподрядчиком"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_rule
msgid "Stock Rule"
msgstr "Правило склада"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "Subcontract"
msgstr "Субподряд"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_return_picking__subcontract_location_id
msgid "Subcontract Location"
msgstr "Местонахождение субподряда"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_product_supplierinfo__is_subcontractor
msgid "Subcontracted"
msgstr "Субподряд"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "Subcontracted manufacturing orders cannot be merged."
msgstr "Заказы на субподрядное производство не могут быть объединены."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp_subcontracting.selection__mrp_bom__type__subcontract
#, python-format
msgid "Subcontracting"
msgstr "Субподряд"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/res_company.py:0
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_company__subcontracting_location_id
#, python-format
msgid "Subcontracting Location"
msgstr "Расположение субподряда"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.quant_subcontracting_search_view
msgid "Subcontracting Locations"
msgstr "Места расположения субподрядчиков"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_mto_pull_id
msgid "Subcontracting MTO Rule"
msgstr "Правило MTO для субподрядчиков"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_pull_id
msgid "Subcontracting MTS Rule"
msgstr "Правило МТС о субподрядах"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_type_id
msgid "Subcontracting Operation Type"
msgstr "Тип субподрядной операции"

#. module: mrp_subcontracting
#: model:ir.actions.act_window,name:mrp_subcontracting.subcontracting_portal_view_production_action
msgid "Subcontracting Portal"
msgstr "Портал субконтрактов"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_warehouse__subcontracting_resupply_type_id
msgid "Subcontracting Resupply Operation Type"
msgstr "Субконтрактация Тип операции по пополнению запасов"

#. module: mrp_subcontracting
#. odoo-javascript
#: code:addons/mrp_subcontracting/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#, python-format
msgid "Subcontracting:"
msgstr "Субподряд:"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/report/mrp_report_bom_structure.py:0
#, python-format
msgid "Subcontracting: %s"
msgstr "Субподряд: %s"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__subcontractor_id
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__is_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_location__subcontractor_ids
msgid "Subcontractor"
msgstr "Субподрядчик"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,field_description:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid "Subcontractor Location"
msgstr "Местонахождение субподрядчика"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_bom__subcontractor_ids
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.view_partner_mrp_subcontracting_filter
msgid "Subcontractors"
msgstr "Субподрядчики"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Прайслист поставщика"

#. module: mrp_subcontracting
#: model:ir.model.fields,field_description:mrp_subcontracting.field_stock_move__is_subcontract
msgid "The move is a subcontract receipt"
msgstr "Переезд - это получение субподряда"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_res_partner__property_stock_subcontractor
#: model:ir.model.fields,help:mrp_subcontracting.field_res_users__property_stock_subcontractor
msgid ""
"The stock location used as source and destination when sending        goods "
"to this contact during a subcontracting process."
msgstr ""
"Местонахождение склада, используемое в качестве источника и пункта "
"назначения при отправке товаров этому контакту в процессе субконтрактации."

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.portal_my_productions
msgid "There are currently no productions for your account."
msgstr "В настоящее время для вашего аккаунта нет постановок."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
#, python-format
msgid ""
"There shouldn't be multiple productions to record for the same subcontracted"
" move."
msgstr ""
"Для одного и того же субподрядного переезда не должно быть нескольких "
"производств."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "This MO isn't related to a subcontracted move"
msgstr "Этот МО не связан с субподрядным переездом"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_move.py:0
#, python-format
msgid "To subcontract, use a planned transfer."
msgstr ""

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_move_form_view
msgid "Total Consumed"
msgstr "Всего поглощено"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_picking
#: model:ir.model.fields,field_description:mrp_subcontracting.field_mrp_production__incoming_picking
msgid "Transfer"
msgstr "Перемещение"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.mrp_subcontracting_stock_move_line_tree_view
msgid "Unit of Measure"
msgstr "Ед. изм"

#. module: mrp_subcontracting
#: model:ir.model.fields,help:mrp_subcontracting.field_mrp_production__subcontractor_id
msgid "Used to restrict access to the portal user through Record Rules"
msgstr ""
"Используется для ограничения доступа к пользователю портала с помощью правил"
" записи"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_stock_warehouse
msgid "Warehouse"
msgstr "Склад"

#. module: mrp_subcontracting
#: model:ir.model,name:mrp_subcontracting.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""
"Мастер, предупреждающий об использовании большего количества компонентов в "
"заявке на производство (связанной со спецификацией)"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not set a Bill of Material with operations or by-product line as "
"subcontracting."
msgstr ""
"Вы не можете установить накладную с операциями или побочными продуктами как "
"субподрядную."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_location.py:0
#, python-format
msgid "You cannot alter the company's subcontracting location"
msgstr "Вы не можете изменить местоположение субподряда компании"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You cannot write on fields %s in mrp.production."
msgstr "В mrp.production нельзя записывать в поля %s."

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/stock_picking.py:0
#, python-format
msgid "You might want to update the locations of this transfer's operations"
msgstr ""
"Возможно, вам захочется обновить местоположение операций этого перевода"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You must enter a serial number for %s"
msgstr "Вы должны ввести серийный номер для %s"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid "You must enter a serial number for each line of %s"
msgstr "Вы должны ввести серийный номер для каждой строки %s"

#. module: mrp_subcontracting
#. odoo-python
#: code:addons/mrp_subcontracting/models/mrp_production.py:0
#, python-format
msgid ""
"You must indicate a non-zero amount consumed for at least one of your "
"components"
msgstr ""
"Необходимо указать количество не равное 0, использованное хотя бы для одного"
" из компонентов"

#. module: mrp_subcontracting
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting.subcontracting_portal_production_form_view
msgid "e.g. PO0032"
msgstr "прим. ЗЗ0032"
