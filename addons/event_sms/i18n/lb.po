# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sms
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_sms
#: model:sms.template,body:event_sms.sms_template_data_event_reminder
msgid ""
"${object.event_id.organizer_id.name or object.event_id.company_id.name or "
"user.env.company.name}: We are excited to remind you that the "
"${object.event_id.name} event is starting ${object.get_date_range_str()}. We"
" confirm your registration and hope to meet you there."
msgstr ""

#. module: event_sms
#: model:sms.template,body:event_sms.sms_template_data_event_registration
msgid ""
"${object.event_id.organizer_id.name or object.event_id.company_id.name or "
"user.env.company.name}: We are happy to confirm your registration for the "
"${object.event_id.name} event."
msgstr ""

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_mail
msgid "Event Automated Mailing"
msgstr ""

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_registration
msgid "Event Registration"
msgstr ""

#. module: event_sms
#: model:sms.template,name:event_sms.sms_template_data_event_registration
msgid "Event: Registration"
msgstr ""

#. module: event_sms
#: model:sms.template,name:event_sms.sms_template_data_event_reminder
msgid "Event: Reminder"
msgstr ""

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr ""

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr ""

#. module: event_sms
#: model:ir.model.fields.selection,name:event_sms.selection__event_mail__notification_type__sms
#: model:ir.model.fields.selection,name:event_sms.selection__event_type_mail__notification_type__sms
msgid "SMS"
msgstr ""

#. module: event_sms
#: model:ir.model.fields,field_description:event_sms.field_event_mail__sms_template_id
#: model:ir.model.fields,field_description:event_sms.field_event_type_mail__sms_template_id
msgid "SMS Template"
msgstr ""

#. module: event_sms
#: model:ir.model.fields,field_description:event_sms.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event_sms.field_event_type_mail__notification_type
msgid "Send"
msgstr ""

#. module: event_sms
#: model:ir.model.fields,help:event_sms.field_event_mail__sms_template_id
#: model:ir.model.fields,help:event_sms.field_event_type_mail__sms_template_id
msgid ""
"This field contains the template of the SMS that will be automatically sent"
msgstr ""
