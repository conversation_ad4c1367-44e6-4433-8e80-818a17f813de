# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sms
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>r Gözütok, 2023
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.3\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-19 13:58+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_mail
msgid "Event Automated Mailing"
msgstr "Otomatik Etkinlik Postalama"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_registration
msgid "Event Registration"
msgstr "Etkinlik Kaydı"

#. module: event_sms
#: model:sms.template,name:event_sms.sms_template_data_event_registration
msgid "Event: Registration"
msgstr "Etkinlik: Kayıt"

#. module: event_sms
#: model:sms.template,name:event_sms.sms_template_data_event_reminder
msgid "Event: Reminder"
msgstr "Etkinlik: Hatırlatma"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Etkinlik Kategorisine göre  E-Posta Planlama"

#. module: event_sms
#: model:sms.template,body:event_sms.sms_template_data_event_reminder
msgid ""
"Ready for \"{{ object.event_id.name }}\" {{ object.get_date_range_str(object.partner_id.lang) }}?\n"
"{{ 'It starts at %s' % format_time(time=object.event_begin_date, tz=object.event_id.date_tz, time_format='short', lang_code=object.partner_id.lang) + (', at %s' % object.event_id.address_inline if object.event_id.address_inline else '') + '.\\nSee you there!' if object.event_id.address_inline or 'website_published' not in object.event_id._fields else 'Join us on %s/event/%i !' % (object.get_base_url(), object.event_id.id) }}"
msgstr ""
"\"{{ object.event_id.name }}\" {{ object.get_date_range_str(object.partner_id.lang) }} için hazır mısınız?\n"
"{{ '%s' % format_time başlar object.event_begin_date(time=object.event_id, tz=.date_tz, time_format='short', lang_code=object.partner_id.lang) + (', at %s' % object.event_id.address_inline else '') + '.\\nSee you there!' if object.event_id.address_inline or 'object.event_id' if website_published._fields else 'Join us on object.event_id/event/%s !' % (object.get_base_url%i(),  object.event_id.id) }}"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "Mail Zamanlayıcı Kaydı"

#. module: event_sms
#: model:ir.model.fields.selection,name:event_sms.selection__event_mail__notification_type__sms
#: model:ir.model.fields.selection,name:event_sms.selection__event_type_mail__notification_type__sms
msgid "SMS"
msgstr "SMS"

#. module: event_sms
#: model:ir.model,name:event_sms.model_sms_template
msgid "SMS Templates"
msgstr "SMS Şablonları"

#. module: event_sms
#: model:ir.model.fields,field_description:event_sms.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event_sms.field_event_type_mail__notification_type
msgid "Send"
msgstr "Gönder"

#. module: event_sms
#: model:sms.template,body:event_sms.sms_template_data_event_registration
msgid ""
"{{ object.event_id.organizer_id.name or object.event_id.company_id.name or "
"user.env.company.name }}: We are happy to confirm your registration for the "
"{{ object.event_id.name }} event."
msgstr ""
"{{ object.event_id.organizer_id.name veya object.event_id.company_id.name "
"veya user.env.company.name }}: {{ object.event_id.name }} etkinliğine "
"kaydınızı onaylamaktan mutluluk duyuyoruz."
