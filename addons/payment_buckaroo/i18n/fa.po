# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_buckaroo
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Persian (https://www.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:141
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:139
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:148
#, python-format
msgid "Buckaroo: invalid shasign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:137
#, python-format
msgid "Buckaroo: received data for reference %s"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:131
#, python-format
msgid ""
"Buckaroo: received data with missing reference (%s) or pay_id (%s) or "
"shasign (%s)"
msgstr ""

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr ""

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "دریافت کننده پرداخت"

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_transaction
msgid "Payment Transaction"
msgstr "تراکنش پرداخت"

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__provider
msgid "Provider"
msgstr "فراهم‌کننده"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_secretkey
msgid "SecretKey"
msgstr ""

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr ""

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_websitekey
msgid "WebsiteKey"
msgstr ""

#. module: payment_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr ""
