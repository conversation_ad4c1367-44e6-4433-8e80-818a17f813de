# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fleet
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: ສີສຸວັນ ສັງບົວບຸລົມ <<EMAIL>>, 2023\n"
"Language-Team: Lao (https://app.transifex.com/odoo/teams/41243/lo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lo\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "<i class=\"fa fa-map-marker\" title=\"Location\"/>"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span class=\"o_form_label\">End Date Contract Alert</span>"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span> days before the end date</span>"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span>Send an alert </span>"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>cm</span>"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>g/km</span>"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>kW</span>"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_1
msgid "A/C Compressor Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_2
msgid "A/C Condenser Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_3
msgid "A/C Diagnosis"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_4
msgid "A/C Evaporator Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_5
msgid "A/C Recharge"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_needaction
msgid "Action Needed"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Activation Cost"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__active
msgid "Active"
msgstr "ໃຊ້ຢູ່"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_ids
msgid "Activities"
msgstr "ກິດຈຳກຳ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_state
msgid "Activity State"
msgstr "ສະຖານະ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.mail_activity_type_action_config_fleet
#: model:ir.ui.menu,name:fleet.fleet_menu_config_activity_type
msgid "Activity Types"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_tag_action
msgid "Add a new tag"
msgstr ""

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_manager
msgid "Administrator"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_6
msgid "Air Filter Replacement"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "All vehicles"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Apply New Driver"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Archived"
msgstr "ສຳເນົາໄວ້ແລ້ວ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__next_assignation_date
msgid "Assignment Date"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_drivers
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_assignation_log_view_list
msgid "Assignment Logs"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_8
msgid "Assistance"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Attention: renewal overdue"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__transmission__automatic
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__transmission__automatic
msgid "Automatic"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Available"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_7
msgid "Ball Joint Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_9
msgid "Battery Inspection"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_10
msgid "Battery Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__vehicle_type__bike
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__vehicle_type__bike
msgid "Bike"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__frame_type
msgid "Bike Frame Type"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Bikes"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_11
msgid "Brake Caliper Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_12
msgid "Brake Inspection"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_13
msgid "Brake Pad(s) Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__brand_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Brand"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_brand
msgid "Brand of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__cng
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__cng
msgid "CNG"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__default_co2
msgid "CO2 Emissions"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "CO2 Emissions g/km"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2_standard
msgid "CO2 Standard"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__co2
msgid "CO2 emissions of the vehicle"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_1
msgid "Calculation Benefit In Kind"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cancel"
msgstr "ຍົກເລີກ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_off_date
msgid "Cancellation Date"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__cancelled
msgid "Cancelled"
msgstr "ຖືກຍົກເລີກແລ້ວ"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__vehicle_type__car
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__vehicle_type__car
msgid "Car"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_14
msgid "Car Wash"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Cars"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__car_value
msgid "Catalog Value (VAT Incl.)"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_15
msgid "Catalytic Converter Replacement"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_category_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_category_menu
msgid "Categories"
msgstr "ໝວດໝູ່"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__category
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__category_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__category_id
msgid "Category"
msgstr "ໝວດ"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_model_category_name_uniq
msgid "Category name must be unique"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_category
msgid "Category of the model"
msgstr ""

#. module: fleet
#: model:mail.message.subtype,description:fleet.mt_fleet_driver_updated
#: model:mail.message.subtype,name:fleet.mt_fleet_driver_updated
msgid "Changed Driver"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_16
msgid "Charging System Diagnosis"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vin_sn
msgid "Chassis Number"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__state
msgid "Choose whether the contract is still valid or not"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_service_type__category
msgid ""
"Choose whether the service refer to contracts, vehicle services or both"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Close Contract"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__closed
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__closed
msgid "Closed"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__co2_standard
msgid "Co2 Standard"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__color
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__color
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__color
msgid "Color"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__color
msgid "Color of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__company_id
msgid "Company"
msgstr "ບໍລິສັດ"

#. module: fleet
#: model:ir.model,name:fleet.model_res_config_settings
msgid "Config Settings"
msgstr "ການຕັ້ງຄ່າ"

#. module: fleet
#: model:ir.ui.menu,name:fleet.fleet_configuration
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Configuration"
msgstr "ການຕັ້ງຄ່າລະບົບ"

#. module: fleet
#: model:ir.model,name:fleet.model_res_partner
msgid "Contact"
msgstr "ຂໍ້ມູນຕິດຕໍ່ພົວພັນ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Contains Vehicles"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_service_type__category__contract
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__cost_type__contract
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contract"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_graph
msgid "Contract Costs Per Month"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_count
msgid "Contract Count"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid "Contract Expiration Date"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Contract Start Date"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_tree
msgid "Contract logs"
msgstr ""

#. module: fleet
#: model:mail.activity.type,name:fleet.mail_act_fleet_contract_to_renew
msgid "Contract to Renew"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Contract(s)"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_contract_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_contracts
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_contract_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contracts"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__cost
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__amount
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cost"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__cost_type
msgid "Cost Type"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cost that is paid only once at the creation of the contract"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
msgid "Cost type purchased with this cost"
msgstr ""

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting_costs
msgid "Costs"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_costs_reporting_action
msgid "Costs Analysis"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__country_id
msgid "Country"
msgstr "ປະເທດ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__country_code
msgid "Country Code"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_category_action
msgid "Create a new category"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid "Create a new contract"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_brand_action
msgid "Create a new manufacturer"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "Create a new model"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "Create a new odometer log"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid "Create a new service entry"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Create a new type of service"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid "Create a new vehicle status"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_uid
msgid "Created by"
msgstr "ສ້າງໂດຍ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_date
msgid "Created on"
msgstr "ສ້າງເມື່ອ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__currency_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__currency_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__currency_id
msgid "Currency"
msgstr "ສະກຸນເງິນ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_assignation_log_view_list
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Current Driver"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__state_id
msgid "Current state of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__daily
msgid "Daily"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__date_start
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__date
msgid "Date"
msgstr "ວັນທີ"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__acquisition_date
msgid "Date of vehicle registration"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__date
msgid "Date when the cost has been executed"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Date when the coverage of the contract begins"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid ""
"Date when the coverage of the contract expirates (by default, one year after"
" begin date)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__write_off_date
msgid "Date when the vehicle's license plate has been cancelled/removed."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_res_config_settings__delay_alert_contract
msgid "Delay alert contract outdated"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_2
msgid "Depreciation and Interests"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__description
msgid "Description"
msgstr "ຄຳອະທິບາຍ"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__diamant
msgid "Diamant"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__diesel
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__diesel
msgid "Diesel"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__display_name
msgid "Display Name"
msgstr "ຊື່ເຕັມ"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__done
msgid "Done"
msgstr "ສໍາເລັດແລ້ວ"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_17
msgid "Door Window Motor/Regulator Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__doors
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__doors
msgid "Doors Number"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_downgraded
msgid "Downgraded"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__driver_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Driver"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer__driver_id
msgid "Driver address of the vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Drivers"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Drivers History"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__history_count
msgid "Drivers History Count"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Dropdown menu"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Each contract (e.g.: leasing) may include several services\n"
"            (reparation, insurances, periodic maintenance)."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Each service can used in contracts, as a standalone service or both."
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__electric
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__electric
msgid "Electric"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__electric_assistance
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__electric_assistance
msgid "Electric Assistance"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_16
msgid "Emissions"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_leasing
msgid "Employee Car"
msgstr ""

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle_log_services.py:0
#, python-format
msgid "Emptying the odometer value of a vehicle is not allowed."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_end
msgid "End Date"
msgstr "ວັນທີສິ້ນສຸດ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Engine"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_18
msgid "Engine Belt Inspection"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_19
msgid "Engine Coolant Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_20
msgid "Engine/Drive Belt(s) Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_12
msgid "Entry into service tax"
msgstr ""

#. module: fleet
#. odoo-javascript
#: code:addons/fleet/static/src/js/fleet_form.js:0
#, python-format
msgid ""
"Every service and contract of this vehicle will be considered as archived. "
"Are you sure that you want to archive this record?"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_21
msgid "Exhaust Manifold Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__expired
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__expired
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Expired"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__expires_today
msgid "Expires Today"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__first_contract_date
msgid "First Contract Date"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Fiscality"
msgstr ""

#. module: fleet
#: model:ir.module.category,name:fleet.module_fleet_category
#: model:ir.ui.menu,name:fleet.fleet_vehicle_menu
#: model:ir.ui.menu,name:fleet.fleet_vehicles
#: model:ir.ui.menu,name:fleet.menu_root
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "Fleet"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_cost_report
msgid "Fleet Analysis Report"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_graph
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_tree
msgid "Fleet Costs Analysis"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "Fleet Management"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__manager_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__manager_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
msgid "Fleet Manager"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_service_type
msgid "Fleet Service Type"
msgstr ""

#. module: fleet
#: model:ir.actions.server,name:fleet.ir_cron_contract_costs_generator_ir_actions_server
#: model:ir.cron,cron_name:fleet.ir_cron_contract_costs_generator
msgid "Fleet: Generate contracts costs based on costs frequency"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_follower_ids
msgid "Followers"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__frame_size
msgid "Frame Size"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__fuel_type
msgid "Fuel"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_22
msgid "Fuel Injector Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_23
msgid "Fuel Pump Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__fuel_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__default_fuel_type
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Fuel Type"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__full_hybrid
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__full_hybrid
msgid "Full Hybrid"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Future Activities"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__future_driver_id
msgid "Future Driver"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Future Driver :"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__gasoline
msgid "Gasoline"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_service_types_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Group By"
msgstr "ຈັດຂໍ້ມູນຕາມ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_overdue
msgid "Has Contracts Overdue"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_due_soon
msgid "Has Contracts to renew"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__has_message
msgid "Has Message"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_24
msgid "Head Gasket(s) Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_25
msgid "Heater Blower Motor Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_26
msgid "Heater Control Valve Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_27
msgid "Heater Core Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_28
msgid "Heater Hose Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__horsepower
msgid "Horsepower"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower_tax
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__horsepower_tax
msgid "Horsepower Taxation"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__hydrogen
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__hydrogen
msgid "Hydrogen"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__id
msgid "ID"
msgstr "ເລກລຳດັບ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_29
msgid "Ignition Coil Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__open
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__open
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "In Progress"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__service_ids
msgid "Included Services"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__futur
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__futur
msgid "Incoming"
msgstr "ກໍາລັງມາຮອດ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Information"
msgstr "ຂໍ້ມູນຂ່າວສານ"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_30
msgid "Intake Manifold Gasket Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_junior
msgid "Junior"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_kanban
msgid "Km"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__lpg
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__lpg
msgid "LPG"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_state
msgid "Last Contract State"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag____last_update
msgid "Last Modified on"
msgstr "ແກ້ໄຂລ້າສຸດເມື່ອ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer
msgid "Last Odometer"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_uid
msgid "Last Updated by"
msgstr "ປັບປຸງລ້າສຸດໂດຍ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_date
msgid "Last Updated on"
msgstr "ປັບປຸງລ້າສຸດເມື່ອ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Late Activities"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_leasing
msgid "Leasing"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Let's create your first vehicle."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__license_plate
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "License Plate"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__license_plate
msgid "License plate number of the vehicle (i = plate number for a car)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__location
msgid "Location"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__location
msgid "Location of the vehicle (garage, ...)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_128
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_128
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__image_128
msgid "Logo"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "MODELS"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_main_attachment_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_main_attachment_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Manage all your contracts (leasing, insurances, etc.) with\n"
"            their related services, costs. Odoo will automatically warn\n"
"            you when some contracts have to be renewed."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
msgid "Manage efficiently your different effective vehicles Costs with Odoo."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_10
msgid "Management Fee"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__transmission__manual
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__transmission__manual
msgid "Manual"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__brand_id
msgid "Manufacturer"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_brand_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_brand_menu
msgid "Manufacturers"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_ids
msgid "Messages"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__model_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Model"
msgstr "ແບບ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_category_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_category_view_tree
msgid "Model Category"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__model_count
msgid "Model Count"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_tree
msgid "Model Make"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_year
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__model_year
msgid "Model Year"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__name
msgid "Model name"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model
msgid "Model of a vehicle"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_action
#: model:ir.ui.menu,name:fleet.fleet_models_configuration
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_tree
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_kanban
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
msgid "Models"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__monthly
msgid "Monthly"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__name
msgid "Name"
msgstr "ຊື່"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_name
msgid "Name of contract to renew soon"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Need Action"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__new
msgid "New"
msgstr "ສ້າງໃໝ່"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_new_request
msgid "New Request"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_summary
msgid "Next Activity Summary"
msgstr "ເນື້ອໃນກິດຈະກຳຕໍ່ໄປ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__future_driver_id
msgid "Next Driver Address of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__no
msgid "No"
msgstr ""

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "No Plate"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
msgid "No data for analysis"
msgstr ""

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "No plate"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__service_activity__none
msgid "None"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Note"
msgstr "ໝາຍເຫດ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Notes"
msgstr "ໝາຍເຫດ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__doors
msgid "Number of doors of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__seats
msgid "Number of seats of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Odometer"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_tree
msgid "Odometer Logs"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_unit
msgid "Odometer Unit"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__value
msgid "Odometer Value"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_graph
msgid "Odometer Values Per Vehicle"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer_id
msgid "Odometer measure of the vehicle at the moment of this log"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_odometer_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_odometer_menu
msgid "Odometers"
msgstr ""

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_user
msgid "Officer : Manage all vehicles"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_31
msgid "Oil Change"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_32
msgid "Oil Pump Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_omnium
msgid "Omnium"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_15
msgid "Options"
msgstr "ທາງເລືອກ"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_ordered
msgid "Ordered"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_33
msgid "Other Maintenance"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__service_activity__overdue
msgid "Overdue"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_34
msgid "Oxygen Sensor Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__plan_to_change_bike
#: model:ir.model.fields,field_description:fleet.field_res_partner__plan_to_change_bike
#: model:ir.model.fields,field_description:fleet.field_res_users__plan_to_change_bike
msgid "Plan To Change Bike"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__plan_to_change_car
#: model:ir.model.fields,field_description:fleet.field_res_partner__plan_to_change_car
#: model:ir.model.fields,field_description:fleet.field_res_users__plan_to_change_car
msgid "Plan To Change Car"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Planned for Change"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__plug_in_hybrid_diesel
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__plug_in_hybrid_diesel
msgid "Plug-in Hybrid Diesel"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__plug_in_hybrid_gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__plug_in_hybrid_gasoline
msgid "Plug-in Hybrid Gasoline"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__power
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__power
msgid "Power"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_35
msgid "Power Steering Hose Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_36
msgid "Power Steering Pump Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__power
msgid "Power in kW of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__net_car_value
msgid "Purchase Value"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_purchased
msgid "Purchased"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_37
msgid "Radiator Repair"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Ready to manage your fleet more efficiently ?"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_generated
msgid "Recurring Cost"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_frequency
msgid "Recurring Cost Frequency"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__ins_ref
msgid "Reference"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_refueling
msgid "Refueling"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_registered
msgid "Registered"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__acquisition_date
msgid "Registration Date"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_11
msgid "Rent (Excluding VAT)"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_7
msgid "Repair and maintenance"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_repairing
msgid "Repairing"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_9
msgid "Replacement Vehicle"
msgstr ""

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting
msgid "Reporting"
msgstr "ບົດລາຍງານ"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_reserve
msgid "Reserve"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Reset To Draft"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__residual_value
msgid "Residual Value"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_14
msgid "Residual value (Excluding VAT)"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_18
msgid "Residual value in %"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__user_id
msgid "Responsible"
msgstr "ຮັບຜິດຊອບ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_38
msgid "Resurface Rotors"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_39
msgid "Rotate Tires"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_40
msgid "Rotor Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__running
msgid "Running"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__seats
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__seats
msgid "Seats Number"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_senior
msgid "Senior"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__sequence
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__sequence
msgid "Sequence"
msgstr "ລຳດັບ"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_service_type__category__service
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__cost_type__service
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
msgid "Service"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__service_activity
msgid "Service Activity"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__service_type_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
msgid "Service Type"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_service_types_view_tree
msgid "Service Types"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_services_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__service_count
#: model:ir.ui.menu,name:fleet.fleet_services_configuration
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_services_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_activity
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Services"
msgstr "ການບໍລິການ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_graph
msgid "Services Costs Per Month"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_services
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Services Logs"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_config_settings_action
#: model:ir.ui.menu,name:fleet.fleet_config_settings_menu
msgid "Settings"
msgstr "ການກໍານົດຄ່າ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_6
msgid "Snow tires"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_41
msgid "Spark Plug Replacement"
msgstr ""

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "Specify the End date of %s"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__state
msgid "Stage"
msgstr "ຂັ້ນຕອນ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Start Contract"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_start
msgid "Start Date"
msgstr "ວັນທີເລີ່ມ"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_42
msgid "Starter Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__state_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_state_view_tree
msgid "State"
msgstr "ແຂວງ"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_state_fleet_state_name_unique
msgid "State name already exists"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_state_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__state
#: model:ir.ui.menu,name:fleet.fleet_vehicle_state_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Status"
msgstr "ສະຖານະພາບ"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_tree
msgid "Sum of Cost"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_5
msgid "Summer tires"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__name
msgid "Tag Name"
msgstr ""

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_tag_name_uniq
msgid "Tag name already exists!"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_tag_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__tag_ids
#: model:ir.ui.menu,name:fleet.fleet_vehicle_tag_menu
msgid "Tags"
msgstr "ເປົ້າໝາຍ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Tax Info"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_3
msgid "Tax roll"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Terms and Conditions"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_43
msgid "Thermostat Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__next_assignation_date
msgid ""
"This is the date at which the car will be available, if not set it means "
"available instantly"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_44
msgid "Tie Rod End Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_45
msgid "Tire Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_46
msgid "Tire Service"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_to_order
msgid "To Order"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__service_activity__today
msgid "Today"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Today Activities"
msgstr "ກິດຈະກຳມື້ນີ້"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Total"
msgstr "ລວມທັງໝົດ"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_13
msgid "Total expenses (Excluding VAT)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_total
msgid "Total of contracts due or overdue minus one"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_17
msgid "Touring Assistance"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid ""
"Track all the services done on your vehicle.\n"
"            Services can be of many types: occasional repair, fixed maintenance, etc."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__trailer_hook
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__trailer_hook
msgid "Trailer Hitch"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Trailer Hook"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__transmission
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__transmission
msgid "Transmission"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_47
msgid "Transmission Filter Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_48
msgid "Transmission Fluid Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_49
msgid "Transmission Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__trapez
msgid "Trapez"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
msgid "Type"
msgstr "ປະເພດ"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_service_types_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_service_types_menu
msgid "Types"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__vin_sn
msgid "Unique number written on the vehicle motor (VIN/SN number)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__unit
msgid "Unit"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__vehicle_id
#: model:ir.ui.menu,name:fleet.fleet_vehicles_configuration
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "Vehicle"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_count
msgid "Vehicle Count"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__description
msgid "Vehicle Description"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vehicle Information"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__name
msgid "Vehicle Name"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_state
msgid "Vehicle Status"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_tag
msgid "Vehicle Tag"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_tree
msgid "Vehicle Tags"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vehicle_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__vehicle_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_type
msgid "Vehicle Type"
msgstr ""

#. module: fleet
#. odoo-python
#: code:addons/fleet/models/fleet_vehicle_model.py:0
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_action
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_activity
#, python-format
msgid "Vehicles"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_activity
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vehicles Contracts"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Vehicles costs"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
msgid "Vehicles odometers"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__insurer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vendor_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vendor"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__inv_ref
msgid "Vendor Reference"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vendors
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vendors"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_waiting_list
msgid "Waiting List"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__days_left
msgid "Warning Date"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Warning: renewal due soon"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_50
msgid "Water Pump Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__wave
msgid "Wave"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__weekly
msgid "Weekly"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_51
msgid "Wheel Alignment"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_52
msgid "Wheel Bearing Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_53
msgid "Windshield Wiper(s) Replacement"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_search
msgid "With Models"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Write here all other information relative to this contract"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Write here any other information related to the service completed."
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Write here any other information related to this vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__model_year
msgid "Year of the model"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__yearly
msgid "Yearly"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "You can add various odometer entries for all vehicles."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid ""
"You can customize available status to track the evolution of\n"
"            each vehicle. Example: active, being repaired, sold."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "You can define several models (e.g. A3, A4) for each make (Audi)."
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. Model S"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. PAE 326"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "e.g. Tesla"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "img"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__odometer_unit__kilometers
msgid "km"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__odometer_unit__miles
msgid "mi"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the contract for this vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the odometer logs for this vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the services logs for this vehicle"
msgstr ""
