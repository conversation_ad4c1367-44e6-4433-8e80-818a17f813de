# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <jarmo.kortetjar<PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <ossi.manty<PERSON><EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <ossi.manty<PERSON><EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#, python-format
msgid "\"%s\" added to dashboard"
msgstr "\"%s\" lisätty kojelautaan"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid ""
"\"Add to\n"
"                  Dashboard\""
msgstr ""
"\"Lisää\n"
"                  kojelautaan\""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/legacy_add_to_board.js:0
#, python-format
msgid "'%s' added to dashboard"
msgstr "'%s' lisätty työpöydällesi"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#, python-format
msgid "Add"
msgstr "Lisää"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#, python-format
msgid "Add to my dashboard"
msgstr "Lisää omaan kojelautaan"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.js:0
#, python-format
msgid "Are you sure that you want to remove this item?"
msgstr "Haluatko varmasti poistaa tämän kohteen?"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_view.js:0
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr "Työpöytä"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "Change Layout"
msgstr "Muuta asettelu"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/add_to_board/legacy_add_to_board.js:0
#, python-format
msgid "Could not add filter to dashboard"
msgstr "Suodatinta ei pystytty lisäämään työpöydälle"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_action.xml:0
#, python-format
msgid "Invalid action"
msgstr "Virheellinen toiminto"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "Layout"
msgstr "Ulkoasu"

#. module: board
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
msgid "My Dashboard"
msgstr "Oma työpöytä"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/add_to_board/legacy_add_to_board.js:0
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr "Virkistä selaimesi, jotta muutokset tulevat voimaan."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                  menu, switch to list or graph view, and click"
msgstr ""
"Jos haluat lisätä ensimmäisen raporttisi tähän kojelautaan, siirry mihin tahansa osoitteessa\n"
"                  valikkoon, vaihda luettelo- tai graafinäkymään ja napsauta sitten"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                  dashboard using the search options."
msgstr ""
"Voit suodattaa ja ryhmitellä tietoja ennen niiden lisäämistä\n"
"                  kojelautaan hakuvaihtoehtojen avulla."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "Your personal dashboard is empty"
msgstr "Henkilökohtainen ilmoitustaulusi on tyhjä"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "in the extended search options."
msgstr "laajennetuissa hakuvaihtoehdoissa."
