# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Malayalam (https://app.transifex.com/odoo/teams/41243/ml/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ml\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#, python-format
msgid "\"%s\" added to dashboard"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid ""
"\"Add to\n"
"                  Dashboard\""
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/legacy_add_to_board.js:0
#, python-format
msgid "'%s' added to dashboard"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#, python-format
msgid "Add"
msgstr "ചേർക്കുക"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#, python-format
msgid "Add to my dashboard"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.js:0
#, python-format
msgid "Are you sure that you want to remove this item?"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_view.js:0
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "Change Layout"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/add_to_board/legacy_add_to_board.js:0
#, python-format
msgid "Could not add filter to dashboard"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ഐഡി"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_action.xml:0
#, python-format
msgid "Invalid action"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "Layout"
msgstr "ലേഔട്ട്"

#. module: board
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
msgid "My Dashboard"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/add_to_board/legacy_add_to_board.js:0
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                  menu, switch to list or graph view, and click"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                  dashboard using the search options."
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "Your personal dashboard is empty"
msgstr ""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
#, python-format
msgid "in the extended search options."
msgstr ""
