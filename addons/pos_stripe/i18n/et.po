# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_stripe
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:22+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: Anna, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Complete the Stripe onboarding for company %s."
msgstr ""

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Do not have access to fetch token from Stripe"
msgstr "Puudub ligipääs Stripe tokenile"

#. module: pos_stripe
#: model_terms:ir.ui.view,arch_db:pos_stripe.pos_payment_method_view_form_inherit_pos_stripe
msgid ""
"Don't forget to complete Stripe connect before using this payment method."
msgstr ""
"Ära unusta lõpuni viia Stripe connect enne selle maksemeetodi kasutamist."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Failed to discover: %s"
msgstr "Ei suutnud avastada: 1%s"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Failed to load resource: net::ERR_INTERNET_DISCONNECTED."
msgstr "Võrguühendus puudub: net::ERR_INTERNET_DISCONNECTED."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "No available Stripe readers."
msgstr "Pole saadaval Stripe lugejaid."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Payment canceled because not reader connected"
msgstr "Makse tühistati, sest lugeja ei ole ühendatud"

#. module: pos_stripe
#: model:ir.model,name:pos_stripe.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Kassa maksemeetodid"

#. module: pos_stripe
#: model:ir.model,name:pos_stripe.model_pos_session
msgid "Point of Sale Session"
msgstr "Kassa Sessioon"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Reader disconnected"
msgstr "Lugeja ühendus katkes"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Stripe"
msgstr "Stripe"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Stripe Error"
msgstr "Stripe veateade"

#. module: pos_stripe
#: model:ir.model.fields,field_description:pos_stripe.field_pos_payment_method__stripe_serial_number
msgid "Stripe Serial Number"
msgstr "Stripe seerianumber"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Stripe payment provider for company %s is missing"
msgstr ""

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Stripe readers %s not listed in your account"
msgstr "Stripe lugeja 1%s ei ole märgitud sinu kontol"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Terminal %s is already used on payment method %s."
msgstr "Terminal 1%s on juba kasutatud sellel maksemeetodil 1%s."

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Stripe, try again later."
msgstr "Esineb probleeme Stripe ühenduses, palun proovi hiljem uuesti."

#. module: pos_stripe
#: model:ir.model.fields,help:pos_stripe.field_pos_payment_method__stripe_serial_number
msgid "[Serial number of the stripe terminal], for example: WSC513105011295"
msgstr "[Stripe terminali seerianumber], näiteks: WSC513105011295"
