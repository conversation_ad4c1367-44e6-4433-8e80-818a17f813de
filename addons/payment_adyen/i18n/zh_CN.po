# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_adyen
# 
# Translators:
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: inspur qiuguodong <<EMAIL>>, 2019\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr "; 找到多个订单"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr "; 没有订单"

#. module: payment_adyen
#: model:ir.model.fields.selection,name:payment_adyen.selection__payment_acquirer__provider__adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:0
#, python-format
msgid "Adyen: feedback error"
msgstr "Adyen: 反馈出错"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:0
#, python-format
msgid "Adyen: invalid merchantSig, received %s, computed %s"
msgstr "Adyen: 无效的 merchantSig，已接收 %s, 已计算 %s"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:0
#, python-format
msgid "Adyen: received data for reference %s"
msgstr "Adyen: 接收数据供参考%s"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:0
#, python-format
msgid ""
"Adyen: received data with missing reference (%s) or missing pspReference "
"(%s)"
msgstr "Adyen：已接收缺少参考 (%s) 或缺少 PSP 参考 (%s) 的数据"

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.acquirer_form_adyen
msgid "How to configure your Adyen account?"
msgstr "如何设置您的Adyen账户？"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_merchant_account
msgid "Merchant Account"
msgstr "商业帐户"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "付款收单单位"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__provider
msgid "Provider"
msgstr "供应商"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_skin_code
msgid "Skin Code"
msgstr "皮肤代码"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_skin_hmac_key
msgid "Skin HMAC Key"
msgstr "皮肤HMAC密钥"
