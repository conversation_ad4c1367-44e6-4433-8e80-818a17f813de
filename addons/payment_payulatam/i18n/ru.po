# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payulatam
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Russian (https://www.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: payment_payulatam
#: model:ir.model.fields.selection,name:payment_payulatam.selection__payment_acquirer__provider__payulatam
msgid "PayU Latam"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__payulatam_api_key
msgid "PayU Latam API Key"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__payulatam_account_id
msgid "PayU Latam Account ID"
msgstr ""

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__payulatam_merchant_id
msgid "PayU Latam Merchant ID"
msgstr ""

#. module: payment_payulatam
#: code:addons/payment_payulatam/models/payment.py:0
#, python-format
msgid "PayU Latam: received data for reference %s; multiple orders found"
msgstr ""

#. module: payment_payulatam
#: code:addons/payment_payulatam/models/payment.py:0
#, python-format
msgid "PayU Latam: received data for reference %s; no order found"
msgstr ""

#. module: payment_payulatam
#: code:addons/payment_payulatam/models/payment.py:0
#, python-format
msgid ""
"PayU Latam: received data with missing reference (%s) or transaction id (%s)"
" or sign (%s)"
msgstr ""

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Платежная система"

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_transaction
msgid "Payment Transaction"
msgstr "Операция Оплаты"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_acquirer__provider
msgid "Provider"
msgstr "Провайдер"
