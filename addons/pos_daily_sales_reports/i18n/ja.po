# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_daily_sales_reports
# 
# Translators:
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2023-04-14 06:18+0000\n"
"Last-Translator: Jun<PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>Amount of discounts</strong>:"
msgstr "<strong>割引金額</strong>:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>Config names</strong>"
msgstr "<strong>設定名</strong>"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>End of session note:</strong>"
msgstr "<strong>セッション終了メモ:</strong>"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>Number of discounts</strong>:"
msgstr "<strong>割引数:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>Opening of session note:</strong>"
msgstr "<strong>セッション開始メモ:</strong>"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "<strong>Total</strong>"
msgstr "<strong>合計</strong>"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Base Amount"
msgstr "基準額"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.view_pos_daily_sales_reports_wizard
msgid "Cancel"
msgstr "キャンセル"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_session__closing_notes
msgid "Closing Notes"
msgstr "終了ノート"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Counted"
msgstr "棚卸数量"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__create_uid
msgid "Created by"
msgstr "作成者"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__create_date
msgid "Created on"
msgstr "作成日"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Daily Report"
msgstr ""

#. module: pos_daily_sales_reports
#: model:ir.actions.act_window,name:pos_daily_sales_reports.action_report_pos_daily_sales_reports
msgid "Daily Reports"
msgstr ""

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Difference"
msgstr "差異"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Disc:"
msgstr "ディスク:"

#. module: pos_daily_sales_reports
#. odoo-javascript
#: code:addons/pos_daily_sales_reports/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/pos_daily_sales_reports/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "Discount:"
msgstr "値引:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Discounts:"
msgstr "値引:"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__display_name
msgid "Display Name"
msgstr "表示名"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Expected"
msgstr "見込"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__id
msgid "ID"
msgstr "ID"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Invoices"
msgstr "請求書"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard____last_update
msgid "Last Modified on"
msgstr "最後更新日"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Multiple Report"
msgstr ""

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Name"
msgstr "名称"

#. module: pos_daily_sales_reports
#. odoo-python
#: code:addons/pos_daily_sales_reports/models/pos_daily_sales_reports.py:0
#, python-format
msgid "No Taxes"
msgstr "税金なし"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Number of transactions:"
msgstr "取引数:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Order reference"
msgstr "オーダ参照"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Payments"
msgstr "支払"

#. module: pos_daily_sales_reports
#: model:ir.model,name:pos_daily_sales_reports.model_pos_daily_sales_reports_wizard
msgid "Point of Sale Daily Report"
msgstr "POS日報"

#. module: pos_daily_sales_reports
#: model:ir.model,name:pos_daily_sales_reports.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "POSの詳細"

#. module: pos_daily_sales_reports
#: model:ir.model,name:pos_daily_sales_reports.model_pos_session
msgid "Point of Sale Session"
msgstr "POSセッション"

#. module: pos_daily_sales_reports
#: model:ir.model.fields,field_description:pos_daily_sales_reports.field_pos_daily_sales_reports_wizard__pos_session_id
msgid "Pos Session"
msgstr "Posセッション"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.view_pos_daily_sales_reports_wizard
msgid "Pos session"
msgstr "POSセッション"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.view_pos_daily_sales_reports_wizard
msgid "Print"
msgstr "印刷"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Product"
msgstr "製品"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Product Category"
msgstr "製品カテゴリ"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Quantity"
msgstr "数量"

#. module: pos_daily_sales_reports
#. odoo-javascript
#: code:addons/pos_daily_sales_reports/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "REFUNDED:"
msgstr "返金済:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Refunds"
msgstr "返金"

#. module: pos_daily_sales_reports
#. odoo-javascript
#: code:addons/pos_daily_sales_reports/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "SOLD:"
msgstr "販売済:"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Sales"
msgstr "売上"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.view_pos_daily_sales_reports_wizard
msgid "Sales Details"
msgstr "売上詳細"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Session Control"
msgstr "セッション管理"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Session ID:"
msgstr "セッションID: "

#. module: pos_daily_sales_reports
#: model:ir.ui.menu,name:pos_daily_sales_reports.menu_report_daily_details
msgid "Session Report"
msgstr "セッションレポ―t"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Tax Amount"
msgstr "税額"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Taxes on refunds"
msgstr "返金の税金"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Taxes on sales"
msgstr "販売の税金"

#. module: pos_daily_sales_reports
#. odoo-python
#: code:addons/pos_daily_sales_reports/models/pos_daily_sales_reports.py:0
#, python-format
msgid "This session is already closed."
msgstr "このセッションはすでに終了しています。"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Total"
msgstr "合計"

#. module: pos_daily_sales_reports
#: model_terms:ir.ui.view,arch_db:pos_daily_sales_reports.pos_daily_report
msgid "Total:"
msgstr "合計:"
