# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>munk<PERSON> Ganbat <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>ark<PERSON><PERSON> Bataa, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 05:51+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Last-Translator: Bayarkhuu Bataa, 2025\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_work_entry_contract
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_contract__work_entry_source
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_work_entry__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_work_entry.py:0
#, python-format
msgid "%s does not have a contract from %s to %s."
msgstr "%s -ны хувьд %s ээс %s хугацаанд хамрагдах ямар нэг гэрээ алга."

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_work_entry.py:0
#, python-format
msgid ""
"%s has multiple contracts from %s to %s. A work entry cannot overlap "
"multiple contracts."
msgstr ""
"%s нь %s ээс %s хугацаанд хамрагдах хоёр гэрээтэй байна. Олон гэрээ "
"хугацаагаар давхцаж буй тохиолдолд ажилласан цагийн бүртгэлийг үүсгэх "
"боломжгүй."

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid ""
"<i class=\"fa fa-exclamation-triangle me-1\" title=\"Warning\"/>You are not "
"allowed to regenerate validated work entries"
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "<i class=\"fa fa-info-circle me-1\" title=\"Hint\"/>"
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid ""
"<span class=\"text-muted\">Warning: The work entry regeneration will delete "
"all manual changes on the selected period.</span>"
msgstr ""
"<span class=\"text-muted\">Анхааруулга: Цагийн тооцоо үүсгэсэнээр сонгогдсон"
" хугацаан дахь гар өөрчлөлтүүдийг устгах болно.</span>"

#. module: hr_work_entry_contract
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_work_entry_type__is_leave
msgid "Allow the work entry type to be linked with time off types."
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Cancel"
msgstr "Цуцлах"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_compensatory
msgid "Compensatory Time Off"
msgstr "Нөхөн амралт"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__contract_id
msgid "Contract"
msgstr "Гэрээ"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__earliest_available_date_message
msgid "Earliest Available Date Message"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__earliest_available_date
msgid "Earliest date"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__employee_id
msgid "Employee"
msgstr "Ажилтан"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_contract
msgid "Employee Contract"
msgstr "Ажилтны гэрээ"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__employee_ids
msgid "Employees"
msgstr "Хүний нөөц"

#. module: hr_work_entry_contract
#. odoo-javascript
#: code:addons/hr_work_entry_contract/static/src/views/work_entry_calendar/work_entry_calendar_model.js:0
#, python-format
msgid "Everybody's work entries"
msgstr "Бүх нийтийн цагийн тооцоо"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_extra_hours
msgid "Extra Hours"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__date_from
msgid "From"
msgstr "Эхлэх"

#. module: hr_work_entry_contract
#: model:ir.actions.server,name:hr_work_entry_contract.ir_cron_generate_missing_work_entries_ir_actions_server
#: model:ir.cron,cron_name:hr_work_entry_contract.ir_cron_generate_missing_work_entries
msgid "Generate Missing Work Entries"
msgstr "Цагийн тооцоог нөхөж үүсгэх"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__date_generated_from
msgid "Generated From"
msgstr "Цагийн тооцоо үүсгэж эхэлсэн огноо"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__date_generated_to
msgid "Generated To"
msgstr "Цагийн тооцоо үүсгэж дууссан огноо"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_leave
msgid "Generic Time Off"
msgstr "Ердийн чөлөө"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry
msgid "HR Work Entry"
msgstr "ХН Ажлын цагийн тооцоо"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "ХН Ажлын цагийн тооцооны төрөл"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_home_working
msgid "Home Working"
msgstr "Гэрээсээ ажиллах"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/wizard/hr_work_entry_regeneration_wizard.py:0
#, python-format
msgid ""
"In order to regenerate the work entries, you need to provide the wizard with"
" an employee_id, a date_from and a date_to. In addition to that, the time "
"interval defined by date_from and date_to must not contain any validated "
"work entries."
msgstr ""
"Ажлын цагийн тооцоог үүсгэхийн тулд ажилтан болон эхлэх дуусах огноог "
"оруулна. Мөн түүнчлэн өгөгдөх хугацааны мужад өмнө нь үүссэн ямар нэг "
"батлагдсан цагийн тооцоо давхцах ёсгүй."

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__last_generation_date
msgid "Last Generation Date"
msgstr "Цагийн тооцоог сүүлд үүсгэсэн огноо"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__latest_available_date_message
msgid "Latest Available Date Message"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__latest_available_date
msgid "Latest date"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_long_leave
msgid "Long Term Time Off"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_legal_leave
msgid "Paid Time Off"
msgstr "Ээлжийн амралт"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry_regeneration_wizard
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "Ажилтны цагийн тооцоо үүсгэх"

#. module: hr_work_entry_contract
#. odoo-javascript
#: code:addons/hr_work_entry_contract/static/src/js/work_entries_controller_mixin.js:0
#: code:addons/hr_work_entry_contract/static/src/views/work_entry_calendar/work_entry_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
#, python-format
msgid "Regenerate Work Entries"
msgstr "Цагийн тооцоо үүсгэх"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__search_criteria_completed
msgid "Search Criteria Completed"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_sick_leave
msgid "Sick Time Off"
msgstr "Өвчний чөлөө"

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_contract.py:0
#, python-format
msgid ""
"Sorry, generating work entries from cancelled contracts is not allowed."
msgstr ""

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/wizard/hr_work_entry_regeneration_wizard.py:0
#, python-format
msgid ""
"The from date must be >= '%(earliest_available_date)s' and the to date must "
"be <= '%(latest_available_date)s', which correspond to the generated work "
"entries time interval."
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_contract_view_form_inherit
msgid "This work entry cannot be validated. The work entry type is undefined."
msgstr ""
"Энэ ажлын цагийн тооцоог батлах боломжгүй. Ажлын цагийн төрөл "
"тодорхойлогдоогүй."

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_type__is_leave
msgid "Time Off"
msgstr "Чөлөө"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__date_to
msgid "To"
msgstr "Дуусах"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_unpaid_leave
msgid "Unpaid"
msgstr "Цалингүй"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__valid
msgid "Valid"
msgstr "Батлагдсан"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Work Entries"
msgstr "Ажлын цагийн тооцоо"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__validated_work_entry_ids
msgid "Work Entries Within Interval"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.actions.act_window,name:hr_work_entry_contract.hr_work_entry_regeneration_wizard_action
msgid "Work Entry Regeneration"
msgstr "Ажлын цагийн тооцоог дахин үүсгэх"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__work_entry_source
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__work_entry_source
msgid "Work Entry Source"
msgstr "Ажлын цагийн эх үүсвэр"

#. module: hr_work_entry_contract
#: model:ir.model.fields.selection,name:hr_work_entry_contract.selection__hr_contract__work_entry_source__calendar
msgid "Working Schedule"
msgstr "Ажлын цагийн хуваарь"
