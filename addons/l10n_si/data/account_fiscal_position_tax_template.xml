<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- EU partner -->
        <record id="gd_fp_eu_sale_vat_22" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="gd_fp_eu"/>
            <field name="tax_src_id" ref="gd_taxr_3"/>
            <field name="tax_dest_id" ref="gd_taxr_1"/>
        </record>

        <record id="gd_fp_eu_sale_vat_9" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="gd_fp_eu"/>
            <field name="tax_src_id" ref="gd_taxr_2"/>
            <field name="tax_dest_id" ref="gd_taxr_1"/>
        </record>

        <record id="gd_fp_eu_sale_vat_5" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="gd_fp_eu"/>
            <field name="tax_src_id" ref="l10n_si_vat_5_sale"/>
            <field name="tax_dest_id" ref="gd_taxr_1"/>
        </record>

        <record id="gd_fp_eu_purchase_vat_22" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="gd_fp_eu"/>
            <field name="tax_src_id" ref="gd_taxp_3"/>
            <field name="tax_dest_id" ref="gd_taxp_st_2"/>
        </record>

        <record id="gd_fp_eu_purchase_vat_9" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="gd_fp_eu"/>
            <field name="tax_src_id" ref="gd_taxp_2"/>
            <field name="tax_dest_id" ref="gd_taxp_st_1"/>
        </record>

        <record id="gd_fp_eu_purchase_vat_5" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="gd_fp_eu"/>
            <field name="tax_src_id" ref="l10n_si_vat_5_purchase"/>
            <field name="tax_dest_id" ref="l10n_si_vat_5_purchase_goods_eu"/>
        </record>
        <!-- EU partner [END] -->

        <!-- Partner outside the EU -->
        <record id="gd_fp_ne_purchase_vat_22" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="gd_fp_ne"/>
            <field name="tax_src_id" ref="gd_taxp_3"/>
            <field name="tax_dest_id" ref="gd_taxp_1"/>
        </record>

        <record id="gd_fp_ne_purchase_vat_9" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="gd_fp_ne"/>
            <field name="tax_src_id" ref="gd_taxp_2"/>
            <field name="tax_dest_id" ref="gd_taxp_1"/>
        </record>

        <record id="gd_fp_ne_purchase_vat_5" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="gd_fp_ne"/>
            <field name="tax_src_id" ref="l10n_si_vat_5_purchase"/>
            <field name="tax_dest_id" ref="gd_taxp_1"/>
        </record>
        <!-- Partner outside the EU [END] -->
    </data>
</odoo>
