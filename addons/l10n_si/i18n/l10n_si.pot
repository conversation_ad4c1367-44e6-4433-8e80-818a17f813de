# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_si
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 06:53+0000\n"
"PO-Revision-Date: 2022-08-24 06:53+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_gd_taxp_1
#: model:account.tax,name:l10n_si.2_gd_taxp_1
#: model:account.tax.group,name:l10n_si.tax_group_0
#: model:account.tax.template,name:l10n_si.gd_taxp_1
msgid "0% VAT"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_gd_taxr_1
#: model:account.tax,name:l10n_si.2_gd_taxr_1
#: model:account.tax.template,name:l10n_si.gd_taxr_1
msgid "0% VAT EU"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_0_purchase_fixed
#: model:account.tax,name:l10n_si.2_l10n_si_vat_0_purchase_fixed
#: model:account.tax.template,name:l10n_si.l10n_si_vat_0_purchase_fixed
msgid "0% VAT fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_0_sale_no_deduction
#: model:account.tax,name:l10n_si.2_l10n_si_vat_0_sale_no_deduction
#: model:account.tax.template,name:l10n_si.l10n_si_vat_0_sale_no_deduction
msgid "0% VAT without deduction"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_11
msgid "11"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_11
msgid "11. Supplies of goods and services"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_11a
msgid "11a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_11a
msgid ""
"11a. Supplies of goods and services in Slovenia, of which VAT is charged by "
"the recipient"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_12
msgid "12"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_12
msgid "12. Deliveries of goods and services to other EU Member States"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_13
msgid "13"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_13
msgid "13. Sale of goods at a distance"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_14
msgid "14"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_14
msgid "14. Assembly and installation of goods in another Member State"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_15
msgid "15"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_15
msgid "15. Exempt supplies without the right to deduct VAT"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_21
msgid "21"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_21
msgid "21. At a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_22
msgid "22"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_gd_taxp_3
#: model:account.tax,name:l10n_si.1_gd_taxr_3
#: model:account.tax,name:l10n_si.2_gd_taxp_3
#: model:account.tax,name:l10n_si.2_gd_taxr_3
#: model:account.tax.group,name:l10n_si.tax_group_22
#: model:account.tax.template,name:l10n_si.gd_taxp_3
#: model:account.tax.template,name:l10n_si.gd_taxr_3
msgid "22% VAT"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_22_purchase_estate_76a
#: model:account.tax,name:l10n_si.1_l10n_si_vat_self_22_sale_76a
#: model:account.tax,name:l10n_si.2_l10n_si_vat_22_purchase_estate_76a
#: model:account.tax,name:l10n_si.2_l10n_si_vat_self_22_sale_76a
#: model:account.tax.template,name:l10n_si.l10n_si_vat_22_purchase_estate_76a
#: model:account.tax.template,name:l10n_si.l10n_si_vat_self_22_sale_76a
msgid "22% VAT 76.a"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_22_purchase_fixed_eu
#: model:account.tax,name:l10n_si.2_l10n_si_vat_22_purchase_fixed_eu
#: model:account.tax.template,name:l10n_si.l10n_si_vat_22_purchase_fixed_eu
msgid "22% VAT EU fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_gd_taxp_st_2
#: model:account.tax,name:l10n_si.2_gd_taxp_st_2
#: model:account.tax.template,name:l10n_si.gd_taxp_st_2
msgid "22% VAT EU goods"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_22_sale_installation_eu
#: model:account.tax,name:l10n_si.2_l10n_si_vat_22_sale_installation_eu
#: model:account.tax.template,name:l10n_si.l10n_si_vat_22_sale_installation_eu
msgid "22% VAT EU installation"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_22_purchase_services_eu
#: model:account.tax,name:l10n_si.2_l10n_si_vat_22_purchase_services_eu
#: model:account.tax.template,name:l10n_si.l10n_si_vat_22_purchase_services_eu
msgid "22% VAT EU services"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_22_sale_distance
#: model:account.tax,name:l10n_si.2_l10n_si_vat_22_sale_distance
#: model:account.tax.template,name:l10n_si.l10n_si_vat_22_sale_distance
msgid "22% VAT distance"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_22_purchase_fixed
#: model:account.tax,name:l10n_si.2_l10n_si_vat_22_purchase_fixed
#: model:account.tax.template,name:l10n_si.l10n_si_vat_22_purchase_fixed
msgid "22% VAT fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_self_22_sale_imports
#: model:account.tax,name:l10n_si.2_l10n_si_vat_self_22_sale_imports
#: model:account.tax.template,name:l10n_si.l10n_si_vat_self_22_sale_imports
msgid "22% VAT imports self-assessment"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_22_purchase_estate
#: model:account.tax,name:l10n_si.2_l10n_si_vat_22_purchase_estate
#: model:account.tax.template,name:l10n_si.l10n_si_vat_22_purchase_estate
msgid "22% VAT real estate"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_recipient_22_sale
#: model:account.tax,name:l10n_si.2_l10n_si_vat_recipient_22_sale
#: model:account.tax.template,name:l10n_si.l10n_si_vat_recipient_22_sale
msgid "22% VAT recipient"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_self_22_sale
#: model:account.tax,name:l10n_si.2_l10n_si_vat_self_22_sale
#: model:account.tax.template,name:l10n_si.l10n_si_vat_self_22_sale
msgid "22% VAT self-assessment"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_22
msgid "22. At a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_22a
msgid "22a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_22a
msgid "22a. At a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_23
msgid "23"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_23
msgid "23. 22% of acquisitions of goods from other EU Member States"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_23a
msgid "23a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_23a
msgid ""
"23a. Of the services received from other EU Member States at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_24
msgid "24"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_24
msgid "24. 9,5% of acquisitions of goods from other EU Member States"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_24a
msgid "24a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_24a
msgid ""
"24a. Of the services received from other EU Member States at the rate of "
"9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_24b
msgid "24b"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_24b
msgid ""
"24b. Acquisitions of goods from other EU Member States at the rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_24c
msgid "24c"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_24c
msgid ""
"24c. Of the services received from other EU Member States at the rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_25
msgid "25"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_25
msgid ""
"25. On the basis of self-assessment as a recipient of goods and services at "
"a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_25a
msgid "25a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_25a
msgid ""
"25a. On the basis of self-assessment as a recipient of goods and services at"
" a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_25b
msgid "25b"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_25b
msgid ""
"25b. On the basis of self-assessment as a recipient of goods and services at"
" a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_26
msgid "26"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_26
msgid "26. On the basis of self-assessment of imports"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_31
msgid "31"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_31
msgid "31. Purchases of goods and services"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_31a
msgid "31a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_31a
msgid ""
"31a. Purchases of goods and services in Slovenia, of which the recipient "
"charges VAT"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_32
msgid "32"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_32
msgid "32. Acquisitions of goods from other EU Member States"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_32a
msgid "32a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_32a
msgid "32a. Services received from other EU Member States"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_33
msgid "33"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_33
msgid ""
"33. Exempt purchases of goods and services and exempt acquisitions of goods"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_34
msgid "34"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_34
msgid "34. Purchase value of real estate"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_35
msgid "35"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_35
msgid "35. Cost of other fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_41
msgid "41"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_41
msgid ""
"41. From purchases of goods and services, acquisition of goods and services "
"received from other EU Member States and from imports at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_42
msgid "42"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_42
msgid ""
"42. From purchases of goods and services, acquisition of goods and services "
"received from other EU Member States and from imports at a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_42a
msgid "42a"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_42a
msgid ""
"42a. From purchases of goods and services, acquisition of goods and services"
" received from other EU Member States and from imports at a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.report.line,tag_name:l10n_si.tax_report_43
msgid "43"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_43
msgid "43. Of the flat-rate compensation at the rate of 8%"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_5_purchase
#: model:account.tax,name:l10n_si.1_l10n_si_vat_5_sale
#: model:account.tax,name:l10n_si.2_l10n_si_vat_5_purchase
#: model:account.tax,name:l10n_si.2_l10n_si_vat_5_sale
#: model:account.tax.group,name:l10n_si.tax_group_5
#: model:account.tax.template,name:l10n_si.l10n_si_vat_5_purchase
#: model:account.tax.template,name:l10n_si.l10n_si_vat_5_sale
msgid "5% VAT"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_5_purchase_estate_76a
#: model:account.tax,name:l10n_si.2_l10n_si_vat_5_purchase_estate_76a
#: model:account.tax.template,name:l10n_si.l10n_si_vat_5_purchase_estate_76a
msgid "5% VAT 76.a"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_5_purchase_fixed_eu
#: model:account.tax,name:l10n_si.2_l10n_si_vat_5_purchase_fixed_eu
#: model:account.tax.template,name:l10n_si.l10n_si_vat_5_purchase_fixed_eu
msgid "5% VAT EU fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_5_purchase_goods_eu
#: model:account.tax,name:l10n_si.2_l10n_si_vat_5_purchase_goods_eu
#: model:account.tax.template,name:l10n_si.l10n_si_vat_5_purchase_goods_eu
msgid "5% VAT EU goods"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_5_sale_installation_eu
#: model:account.tax,name:l10n_si.2_l10n_si_vat_5_sale_installation_eu
#: model:account.tax.template,name:l10n_si.l10n_si_vat_5_sale_installation_eu
msgid "5% VAT EU installation"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_5_purchase_services_eu
#: model:account.tax,name:l10n_si.2_l10n_si_vat_5_purchase_services_eu
#: model:account.tax.template,name:l10n_si.l10n_si_vat_5_purchase_services_eu
msgid "5% VAT EU services"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_5_sale_distance
#: model:account.tax,name:l10n_si.2_l10n_si_vat_5_sale_distance
#: model:account.tax.template,name:l10n_si.l10n_si_vat_5_sale_distance
msgid "5% VAT distance"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_5_purchase_fixed
#: model:account.tax,name:l10n_si.2_l10n_si_vat_5_purchase_fixed
#: model:account.tax.template,name:l10n_si.l10n_si_vat_5_purchase_fixed
msgid "5% VAT fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_self_5_sale_imports
#: model:account.tax,name:l10n_si.2_l10n_si_vat_self_5_sale_imports
#: model:account.tax.template,name:l10n_si.l10n_si_vat_self_5_sale_imports
msgid "5% VAT imports self-assessment"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_5_purchase_estate
#: model:account.tax,name:l10n_si.2_l10n_si_vat_5_purchase_estate
#: model:account.tax.template,name:l10n_si.l10n_si_vat_5_purchase_estate
msgid "5% VAT real estate"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_recipient_5_sale
#: model:account.tax,name:l10n_si.2_l10n_si_vat_recipient_5_sale
#: model:account.tax.template,name:l10n_si.l10n_si_vat_recipient_5_sale
msgid "5% VAT recipient"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_self_5_sale
#: model:account.tax,name:l10n_si.2_l10n_si_vat_self_5_sale
#: model:account.tax.template,name:l10n_si.l10n_si_vat_self_5_sale
msgid "5% VAT self-assessment"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_51
msgid "51. VAT liability"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_52
msgid "52. VAT surplus"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_gd_taxp_2
#: model:account.tax,name:l10n_si.1_gd_taxr_2
#: model:account.tax,name:l10n_si.2_gd_taxp_2
#: model:account.tax,name:l10n_si.2_gd_taxr_2
#: model:account.tax.group,name:l10n_si.tax_group_95
#: model:account.tax.template,name:l10n_si.gd_taxp_2
#: model:account.tax.template,name:l10n_si.gd_taxr_2
msgid "9,5% VAT"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_9_purchase_estate_76a
#: model:account.tax,name:l10n_si.1_l10n_si_vat_self_9_sale_76a
#: model:account.tax,name:l10n_si.2_l10n_si_vat_9_purchase_estate_76a
#: model:account.tax,name:l10n_si.2_l10n_si_vat_self_9_sale_76a
#: model:account.tax.template,name:l10n_si.l10n_si_vat_9_purchase_estate_76a
#: model:account.tax.template,name:l10n_si.l10n_si_vat_self_9_sale_76a
msgid "9,5% VAT 76.a"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_9_purchase_fixed_eu
#: model:account.tax,name:l10n_si.2_l10n_si_vat_9_purchase_fixed_eu
#: model:account.tax.template,name:l10n_si.l10n_si_vat_9_purchase_fixed_eu
msgid "9,5% VAT EU fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_gd_taxp_st_1
#: model:account.tax,name:l10n_si.2_gd_taxp_st_1
#: model:account.tax.template,name:l10n_si.gd_taxp_st_1
msgid "9,5% VAT EU goods"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_9_sale_installation_eu
#: model:account.tax,name:l10n_si.2_l10n_si_vat_9_sale_installation_eu
#: model:account.tax.template,name:l10n_si.l10n_si_vat_9_sale_installation_eu
msgid "9,5% VAT EU installation"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_9_purchase_services_eu
#: model:account.tax,name:l10n_si.2_l10n_si_vat_9_purchase_services_eu
#: model:account.tax.template,name:l10n_si.l10n_si_vat_9_purchase_services_eu
msgid "9,5% VAT EU services"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_9_sale_distance
#: model:account.tax,name:l10n_si.2_l10n_si_vat_9_sale_distance
#: model:account.tax.template,name:l10n_si.l10n_si_vat_9_sale_distance
msgid "9,5% VAT distance"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_9_purchase_fixed
#: model:account.tax,name:l10n_si.2_l10n_si_vat_9_purchase_fixed
#: model:account.tax.template,name:l10n_si.l10n_si_vat_9_purchase_fixed
msgid "9,5% VAT fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_self_9_sale_imports
#: model:account.tax,name:l10n_si.2_l10n_si_vat_self_9_sale_imports
#: model:account.tax.template,name:l10n_si.l10n_si_vat_self_9_sale_imports
msgid "9,5% VAT imports self-assessment"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_9_purchase_estate
#: model:account.tax,name:l10n_si.2_l10n_si_vat_9_purchase_estate
#: model:account.tax.template,name:l10n_si.l10n_si_vat_9_purchase_estate
msgid "9,5% VAT real estate"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_recipient_9_sale
#: model:account.tax,name:l10n_si.2_l10n_si_vat_recipient_9_sale
#: model:account.tax.template,name:l10n_si.l10n_si_vat_recipient_9_sale
msgid "9,5% VAT recipient"
msgstr ""

#. module: l10n_si
#: model:account.tax,name:l10n_si.1_l10n_si_vat_self_9_sale
#: model:account.tax,name:l10n_si.2_l10n_si_vat_self_9_sale
#: model:account.tax.template,name:l10n_si.l10n_si_vat_self_9_sale
msgid "9,5% VAT self-assessment"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_659000
#: model:account.account,name:l10n_si.2_gd_acc_659000
#: model:account.account.template,name:l10n_si.gd_acc_659000
#: model:account.group,name:l10n_si.1_gd_acc_group_65
#: model:account.group,name:l10n_si.2_gd_acc_group_65
#: model:account.group.template,name:l10n_si.gd_acc_group_65
msgid "Accounting for the purchase of goods"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_309000
#: model:account.account,name:l10n_si.2_gd_acc_309000
#: model:account.account.template,name:l10n_si.gd_acc_309000
msgid "Accounting for the purchase of raw materials and supplies"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_30
#: model:account.group,name:l10n_si.2_gd_acc_group_30
#: model:account.group.template,name:l10n_si.gd_acc_group_30
msgid ""
"Accounting for the purchase of raw materials and supplies (including small "
"inventory and packaging)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_290000
#: model:account.account,name:l10n_si.2_gd_acc_290000
#: model:account.account.template,name:l10n_si.gd_acc_290000
msgid "Accrued costs or expenses"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_929000
#: model:account.account,name:l10n_si.2_gd_acc_929000
#: model:account.account.template,name:l10n_si.gd_acc_929000
msgid "Acquired treasury shares or treasury shares (deductible item)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_957000
#: model:account.account,name:l10n_si.2_gd_acc_957000
#: model:account.account.template,name:l10n_si.gd_acc_957000
msgid "Actuarial gains or losses on certain earnings"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_05
#: model:account.group,name:l10n_si.2_gd_acc_group_05
#: model:account.group.template,name:l10n_si.gd_acc_group_05
msgid ""
"Adjustment and impairment of equipment and other property, plant and "
"equipment"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_82
#: model:account.group,name:l10n_si.2_gd_acc_group_82
#: model:account.group.template,name:l10n_si.gd_acc_group_82
msgid ""
"Allocation of net profit for the financial year or net surplus of revenues"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_917000
#: model:account.account,name:l10n_si.2_gd_acc_917000
#: model:account.account.template,name:l10n_si.gd_acc_917000
msgid "Amounts from the effects of confirmed compulsory settlement"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_915000
#: model:account.account,name:l10n_si.2_gd_acc_915000
#: model:account.account.template,name:l10n_si.gd_acc_915000
msgid ""
"Amounts from the simplified reduction of share capital and amounts of "
"reduction of share capital by withdrawal of shares or stakes"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_956000
#: model:account.account,name:l10n_si.2_gd_acc_956000
#: model:account.account.template,name:l10n_si.gd_acc_956000
msgid ""
"Amounts of proven profit or loss from change in the fair value of available-"
"for-sale financial assets that are not part of a hedging relationship"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_473000
#: model:account.account,name:l10n_si.2_gd_acc_473000
#: model:account.account.template,name:l10n_si.gd_acc_473000
msgid ""
"Annual leave allowance, bonuses, refunds (for transport to and from work, "
"for food, for separate living) and other employee benefits"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_674000
#: model:account.account,name:l10n_si.2_gd_acc_674000
#: model:account.account.template,name:l10n_si.gd_acc_674000
msgid "Assets of a cash-generating unit held for sale"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_673000
#: model:account.account,name:l10n_si.2_gd_acc_673000
#: model:account.account.template,name:l10n_si.gd_acc_673000
msgid "Assets work money-generating units intended for sale"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_482000
#: model:account.account,name:l10n_si.2_gd_acc_482000
#: model:account.account.template,name:l10n_si.gd_acc_482000
msgid ""
"Awards to pupils and students on work placements together with benefits"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_11
#: model:account.group,name:l10n_si.2_gd_acc_group_11
#: model:account.group.template,name:l10n_si.gd_acc_group_11
msgid "Balances with banks and other financial institutions"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_480000
#: model:account.account,name:l10n_si.2_gd_acc_480000
#: model:account.account.template,name:l10n_si.gd_acc_480000
msgid "Benefits that do not depend on labor costs or other types of costs"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_991000
#: model:account.account,name:l10n_si.2_gd_acc_991000
#: model:account.account.template,name:l10n_si.gd_acc_991000
msgid "Bills of exchange and other securities received to secure payments"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_185000
#: model:account.account,name:l10n_si.2_gd_acc_185000
#: model:account.account.template,name:l10n_si.gd_acc_185000
msgid "Bills of exchange received"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_638000
#: model:account.account,name:l10n_si.2_gd_acc_638000
#: model:account.account.template,name:l10n_si.gd_acc_638000
msgid "Biological assets that are stocks"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_044000
#: model:account.account,name:l10n_si.2_gd_acc_044000
#: model:account.account.template,name:l10n_si.gd_acc_044000
msgid "Biological resources - basic herd"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_043000
#: model:account.account,name:l10n_si.2_gd_acc_043000
#: model:account.account.template,name:l10n_si.gd_acc_043000
msgid "Biological resources - perennial crops"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_021000
#: model:account.account,name:l10n_si.2_gd_acc_021000
#: model:account.account.template,name:l10n_si.gd_acc_021000
msgid "Buildings valued according to the cost model"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_023000
#: model:account.account,name:l10n_si.2_gd_acc_023000
#: model:account.account.template,name:l10n_si.gd_acc_023000
msgid "Buildings valued according to the revaluation model"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_76
#: model:account.group,name:l10n_si.2_gd_acc_group_76
#: model:account.group.template,name:l10n_si.gd_acc_group_76
msgid "Business income"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_90
#: model:account.group,name:l10n_si.2_gd_acc_group_90
#: model:account.group.template,name:l10n_si.gd_acc_group_90
msgid "Called-up and initial capital and founding deposits"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_91
#: model:account.group,name:l10n_si.2_gd_acc_group_91
#: model:account.group.template,name:l10n_si.gd_acc_group_91
msgid "Capital reserves and transfers of funds"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_9
#: model:account.group,name:l10n_si.2_gd_acc_group_9
#: model:account.group.template,name:l10n_si.gd_acc_group_9
msgid "Capital, long-term liabilities (debt) and long-term provisions"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_001000
#: model:account.account,name:l10n_si.2_gd_acc_001000
#: model:account.account.template,name:l10n_si.gd_acc_001000
msgid "Capitalization costs of investments in foreign tangible fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_79
#: model:account.group,name:l10n_si.2_gd_acc_group_79
#: model:account.group.template,name:l10n_si.gd_acc_group_79
msgid "Capitalize own products and own services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_931000
#: model:account.account,name:l10n_si.2_gd_acc_931000
#: model:account.account.template,name:l10n_si.gd_acc_931000
msgid "Carried forward net loss from previous years"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_110000
#: model:account.account,name:l10n_si.2_gd_acc_110000
#: model:account.account.template,name:l10n_si.gd_acc_110000
msgid "Cash on accounts other than foreign currency"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_10
#: model:account.group,name:l10n_si.2_gd_acc_group_10
#: model:account.group.template,name:l10n_si.gd_acc_group_10
msgid "Cash on hand and immediately realizable securities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_100000
#: model:account.account,name:l10n_si.2_gd_acc_100000
#: model:account.account.template,name:l10n_si.gd_acc_100000
msgid "Cash on hand, except foreign currency"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_114000
#: model:account.account,name:l10n_si.2_gd_acc_114000
#: model:account.account.template,name:l10n_si.gd_acc_114000
msgid "Cash on special accounts or for special purposes"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_102000
#: model:account.account,name:l10n_si.2_gd_acc_102000
#: model:account.account.template,name:l10n_si.gd_acc_102000
msgid "Checks issued (deduction item)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_753000
#: model:account.account,name:l10n_si.1_gd_acc_786000
#: model:account.account,name:l10n_si.2_gd_acc_753000
#: model:account.account,name:l10n_si.2_gd_acc_786000
#: model:account.account.template,name:l10n_si.gd_acc_753000
#: model:account.account.template,name:l10n_si.gd_acc_786000
msgid "Compensation not related to business effects"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_471000
#: model:account.account,name:l10n_si.2_gd_acc_471000
#: model:account.account.template,name:l10n_si.gd_acc_471000
msgid "Compensation of employees' salaries"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_810000
#: model:account.account,name:l10n_si.2_gd_acc_810000
#: model:account.account.template,name:l10n_si.gd_acc_810000
msgid "Corporate income tax"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_264000
#: model:account.account,name:l10n_si.2_gd_acc_264000
#: model:account.account.template,name:l10n_si.gd_acc_264000
msgid "Corporate income tax liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_702000
#: model:account.account,name:l10n_si.1_gd_acc_711000
#: model:account.account,name:l10n_si.2_gd_acc_702000
#: model:account.account,name:l10n_si.2_gd_acc_711000
#: model:account.account.template,name:l10n_si.gd_acc_702000
#: model:account.account.template,name:l10n_si.gd_acc_711000
msgid "Cost of goods and goods sold"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_41
#: model:account.group,name:l10n_si.2_gd_acc_group_41
#: model:account.group.template,name:l10n_si.gd_acc_group_41
msgid "Cost of service"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_49
#: model:account.group,name:l10n_si.2_gd_acc_group_49
#: model:account.group.template,name:l10n_si.gd_acc_group_49
msgid "Cost transfer"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_4
#: model:account.group,name:l10n_si.2_gd_acc_group_4
#: model:account.group.template,name:l10n_si.gd_acc_group_4
msgid "Costs"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_401000
#: model:account.account,name:l10n_si.2_gd_acc_401000
#: model:account.account.template,name:l10n_si.gd_acc_401000
msgid "Costs of auxiliary material"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_417000
#: model:account.account,name:l10n_si.2_gd_acc_417000
#: model:account.account.template,name:l10n_si.gd_acc_417000
msgid "Costs of fairs, advertising and representation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_416000
#: model:account.account,name:l10n_si.2_gd_acc_416000
#: model:account.account.template,name:l10n_si.gd_acc_416000
msgid "Costs of intellectual and personal services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_412000
#: model:account.account,name:l10n_si.2_gd_acc_412000
#: model:account.account.template,name:l10n_si.gd_acc_412000
msgid "Costs of maintenance services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_406000
#: model:account.account,name:l10n_si.2_gd_acc_406000
#: model:account.account.template,name:l10n_si.gd_acc_406000
msgid "Costs of office supplies and professional literature"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_419000
#: model:account.account,name:l10n_si.2_gd_acc_419000
#: model:account.account.template,name:l10n_si.gd_acc_419000
msgid "Costs of other services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_410000
#: model:account.account,name:l10n_si.2_gd_acc_410000
#: model:account.account.template,name:l10n_si.gd_acc_410000
msgid "Costs of services in creating products and providing services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_418000
#: model:account.account,name:l10n_si.2_gd_acc_418000
#: model:account.account.template,name:l10n_si.gd_acc_418000
msgid ""
"Costs of services of natural persons who do not perform activities, together"
" with duties charged to the organization (costs under employment contracts, "
"copyright contracts, meeting fees for employees and other persons ...)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_403000
#: model:account.account,name:l10n_si.2_gd_acc_403000
#: model:account.account.template,name:l10n_si.gd_acc_403000
msgid ""
"Costs of spare parts for fixed assets and materials for the maintenance of "
"fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_472000
#: model:account.account,name:l10n_si.2_gd_acc_472000
#: model:account.account.template,name:l10n_si.gd_acc_472000
msgid "Costs of supplementary pension insurance for employees"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_411000
#: model:account.account,name:l10n_si.2_gd_acc_411000
#: model:account.account.template,name:l10n_si.gd_acc_411000
msgid "Costs of transport services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_652000
#: model:account.account,name:l10n_si.2_gd_acc_652000
#: model:account.account.template,name:l10n_si.gd_acc_652000
msgid "Customs and other import duties on goods"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_302000
#: model:account.account,name:l10n_si.2_gd_acc_302000
#: model:account.account.template,name:l10n_si.gd_acc_302000
msgid "Customs duties and other import duties on raw materials and supplies"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_996000
#: model:account.account,name:l10n_si.2_gd_acc_996000
#: model:account.account.template,name:l10n_si.gd_acc_996000
msgid ""
"Debtors who secured payments with bills of exchange and other securities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_002000
#: model:account.account,name:l10n_si.2_gd_acc_002000
#: model:account.account.template,name:l10n_si.gd_acc_002000
msgid "Deferred development costs"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_09
#: model:account.group,name:l10n_si.2_gd_acc_group_09
#: model:account.group.template,name:l10n_si.gd_acc_group_09
msgid "Deferred tax assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_090000
#: model:account.account,name:l10n_si.2_gd_acc_090000
#: model:account.account.template,name:l10n_si.gd_acc_090000
msgid "Deferred tax assets from deductible temporary differences"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_092000
#: model:account.account,name:l10n_si.2_gd_acc_092000
#: model:account.account.template,name:l10n_si.gd_acc_092000
msgid "Deferred tax assets from tax credits carried forward"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_091000
#: model:account.account,name:l10n_si.2_gd_acc_091000
#: model:account.account.template,name:l10n_si.gd_acc_091000
msgid "Deferred tax assets from unused tax losses carried forward"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_813000
#: model:account.account,name:l10n_si.2_gd_acc_813000
#: model:account.account.template,name:l10n_si.gd_acc_813000
msgid "Deferred tax income (expenses)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_988000
#: model:account.account,name:l10n_si.2_gd_acc_988000
#: model:account.account.template,name:l10n_si.gd_acc_988000
msgid "Deferred tax liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_651000
#: model:account.account,name:l10n_si.2_gd_acc_651000
#: model:account.account.template,name:l10n_si.gd_acc_651000
msgid "Dependent costs of purchasing goods"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_301000
#: model:account.account,name:l10n_si.2_gd_acc_301000
#: model:account.account.template,name:l10n_si.gd_acc_301000
msgid "Dependent costs of purchasing raw materials and supplies"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_43
#: model:account.group,name:l10n_si.2_gd_acc_group_43
#: model:account.group.template,name:l10n_si.gd_acc_group_43
msgid "Depreciation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_436000
#: model:account.account,name:l10n_si.2_gd_acc_436000
#: model:account.account.template,name:l10n_si.gd_acc_436000
msgid "Depreciation of biological assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_431000
#: model:account.account,name:l10n_si.2_gd_acc_431000
#: model:account.account.template,name:l10n_si.gd_acc_431000
msgid "Depreciation of buildings"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_432000
#: model:account.account,name:l10n_si.2_gd_acc_432000
#: model:account.account.template,name:l10n_si.gd_acc_432000
msgid "Depreciation of equipment and spare parts"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_050000
#: model:account.account,name:l10n_si.2_gd_acc_050000
#: model:account.account.template,name:l10n_si.gd_acc_050000
msgid "Depreciation of equipment and spare parts due to depreciation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_430000
#: model:account.account,name:l10n_si.2_gd_acc_430000
#: model:account.account.template,name:l10n_si.gd_acc_430000
msgid "Depreciation of intangible assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_435000
#: model:account.account,name:l10n_si.2_gd_acc_435000
#: model:account.account.template,name:l10n_si.gd_acc_435000
msgid "Depreciation of investment property"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_434000
#: model:account.account,name:l10n_si.2_gd_acc_434000
#: model:account.account.template,name:l10n_si.gd_acc_434000
msgid "Depreciation of other property, plant and equipment"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_433000
#: model:account.account,name:l10n_si.2_gd_acc_433000
#: model:account.account.template,name:l10n_si.gd_acc_433000
msgid "Depreciation of small inventory"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_319000
#: model:account.account,name:l10n_si.2_gd_acc_319000
#: model:account.account.template,name:l10n_si.gd_acc_319000
msgid "Deviations from constant prices of raw materials and supplies"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_329000
#: model:account.account,name:l10n_si.2_gd_acc_329000
#: model:account.account.template,name:l10n_si.gd_acc_329000
msgid "Deviations from constant prices of small inventory and packaging"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_619000
#: model:account.account,name:l10n_si.2_gd_acc_619000
#: model:account.account.template,name:l10n_si.gd_acc_619000
msgid "Deviations from crop prices"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_609000
#: model:account.account,name:l10n_si.2_gd_acc_609000
#: model:account.account.template,name:l10n_si.gd_acc_609000
msgid "Deviations from prices of work in progress and services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_639000
#: model:account.account,name:l10n_si.2_gd_acc_639000
#: model:account.account.template,name:l10n_si.gd_acc_639000
msgid "Deviations from product prices"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_22_sale_distance
#: model:account.tax,description:l10n_si.2_l10n_si_vat_22_sale_distance
#: model:account.tax.template,description:l10n_si.l10n_si_vat_22_sale_distance
msgid "Distance selling goods at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_5_sale_distance
#: model:account.tax,description:l10n_si.2_l10n_si_vat_5_sale_distance
#: model:account.tax.template,description:l10n_si.l10n_si_vat_5_sale_distance
msgid "Distance selling goods at a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_9_sale_distance
#: model:account.tax,description:l10n_si.2_l10n_si_vat_9_sale_distance
#: model:account.tax.template,description:l10n_si.l10n_si_vat_9_sale_distance
msgid "Distance selling goods at a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_81
#: model:account.group,name:l10n_si.2_gd_acc_group_81
#: model:account.group.template,name:l10n_si.gd_acc_group_81
msgid "Distribution of profit and / or total surplus revenue"
msgstr ""

#. module: l10n_si
#: model:account.fiscal.position,name:l10n_si.1_gd_fp_do
#: model:account.fiscal.position,name:l10n_si.2_gd_fp_do
#: model:account.fiscal.position.template,name:l10n_si.gd_fp_do
msgid "Domestic"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_754000
#: model:account.account,name:l10n_si.1_gd_acc_784000
#: model:account.account,name:l10n_si.2_gd_acc_754000
#: model:account.account,name:l10n_si.2_gd_acc_784000
#: model:account.account.template,name:l10n_si.gd_acc_754000
#: model:account.account.template,name:l10n_si.gd_acc_784000
msgid "Donations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_967000
#: model:account.account,name:l10n_si.2_gd_acc_967000
#: model:account.account.template,name:l10n_si.gd_acc_967000
msgid "Donations received"
msgstr ""

#. module: l10n_si
#: model:account.fiscal.position,name:l10n_si.1_gd_fp_eu
#: model:account.fiscal.position,name:l10n_si.2_gd_fp_eu
#: model:account.fiscal.position.template,name:l10n_si.gd_fp_eu
msgid "EU partner"
msgstr ""

#. module: l10n_si
#: model:account.fiscal.position,name:l10n_si.1_gd_fp_do1
#: model:account.fiscal.position,name:l10n_si.2_gd_fp_do1
#: model:account.fiscal.position.template,name:l10n_si.gd_fp_do1
msgid "EU partner private"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_470000
#: model:account.account,name:l10n_si.2_gd_acc_470000
#: model:account.account.template,name:l10n_si.gd_acc_470000
msgid "Employee salaries"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_474000
#: model:account.account,name:l10n_si.2_gd_acc_474000
#: model:account.account.template,name:l10n_si.gd_acc_474000
msgid ""
"Employer's contributions from salaries, wage compensations, bonuses, "
"reimbursements and other employee benefits"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_402000
#: model:account.account,name:l10n_si.2_gd_acc_402000
#: model:account.account.template,name:l10n_si.gd_acc_402000
msgid "Energy costs"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_04
#: model:account.group,name:l10n_si.2_gd_acc_group_04
#: model:account.group.template,name:l10n_si.gd_acc_group_04
msgid "Equipment and other tangible fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_047000
#: model:account.account,name:l10n_si.2_gd_acc_047000
#: model:account.account.template,name:l10n_si.gd_acc_047000
msgid "Equipment and other tangible fixed assets under construction"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_040000
#: model:account.account,name:l10n_si.2_gd_acc_040000
#: model:account.account.template,name:l10n_si.gd_acc_040000
msgid "Equipment and spare parts valued according to the cost model"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_042000
#: model:account.account,name:l10n_si.2_gd_acc_042000
#: model:account.account.template,name:l10n_si.gd_acc_042000
msgid "Equipment and spare parts valued according to the revaluation model"
msgstr ""

#. module: l10n_si
#: model:account.fiscal.position,name:l10n_si.1_gd_fp_exempt
#: model:account.fiscal.position,name:l10n_si.2_gd_fp_exempt
#: model:account.fiscal.position.template,name:l10n_si.gd_fp_exempt
msgid "Exempt taxpayer"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_481000
#: model:account.account,name:l10n_si.2_gd_acc_481000
#: model:account.account.template,name:l10n_si.gd_acc_481000
msgid "Expenditure on environmental protection"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_704000
#: model:account.account,name:l10n_si.2_gd_acc_704000
#: model:account.account.template,name:l10n_si.gd_acc_704000
msgid ""
"Expenditure on evaluation of biological assets and harvesting of "
"agricultural products"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_7
#: model:account.group,name:l10n_si.2_gd_acc_group_7
#: model:account.group.template,name:l10n_si.gd_acc_group_7
msgid "Expenses and revenues"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_747000
#: model:account.account,name:l10n_si.2_gd_acc_747000
#: model:account.account.template,name:l10n_si.gd_acc_747000
msgid ""
"Expenses from assets allocated at fair value through profit or loss and "
"expenses from the valuation of investment property at fair value"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_749000
#: model:account.account,name:l10n_si.2_gd_acc_749000
#: model:account.account.template,name:l10n_si.gd_acc_749000
msgid ""
"Expenses from derecognition of financial investments and investment property"
" measured at fair value"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_751000
#: model:account.account,name:l10n_si.2_gd_acc_751000
#: model:account.account.template,name:l10n_si.gd_acc_751000
msgid "Expenses from disposal of investment property measured at fair value"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_748000
#: model:account.account,name:l10n_si.2_gd_acc_748000
#: model:account.account.template,name:l10n_si.gd_acc_748000
msgid "Expenses from impairment of financial investments"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_742000
#: model:account.account,name:l10n_si.2_gd_acc_742000
#: model:account.account.template,name:l10n_si.gd_acc_742000
msgid "Expenses from issued bonds"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_741000
#: model:account.account,name:l10n_si.2_gd_acc_741000
#: model:account.account.template,name:l10n_si.gd_acc_741000
msgid "Expenses from loans received from banks"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_740000
#: model:account.account,name:l10n_si.2_gd_acc_740000
#: model:account.account.template,name:l10n_si.gd_acc_740000
msgid "Expenses from loans received from group organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_744000
#: model:account.account,name:l10n_si.2_gd_acc_744000
#: model:account.account.template,name:l10n_si.gd_acc_744000
msgid "Expenses from operating liabilities to group organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_743000
#: model:account.account,name:l10n_si.2_gd_acc_743000
#: model:account.account.template,name:l10n_si.gd_acc_743000
msgid "Expenses from other financial liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_746000
#: model:account.account,name:l10n_si.2_gd_acc_746000
#: model:account.account.template,name:l10n_si.gd_acc_746000
msgid ""
"Expenses from other operating liabilities, including interest expenses from "
"the recalculation of severance pay upon retirement"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_745000
#: model:account.account,name:l10n_si.2_gd_acc_745000
#: model:account.account.template,name:l10n_si.gd_acc_745000
msgid "Expenses from trade payables and bill of exchange liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_750000
#: model:account.account,name:l10n_si.2_gd_acc_750000
#: model:account.account.template,name:l10n_si.gd_acc_750000
msgid ""
"Expenses from valuation of investment property according to the fair value "
"model"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_74
#: model:account.group,name:l10n_si.2_gd_acc_group_74
#: model:account.group.template,name:l10n_si.gd_acc_group_74
msgid "Financial expenses"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_77
#: model:account.group,name:l10n_si.2_gd_acc_group_77
#: model:account.group.template,name:l10n_si.gd_acc_group_77
msgid "Financial income"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_778000
#: model:account.account,name:l10n_si.2_gd_acc_778000
#: model:account.account.template,name:l10n_si.gd_acc_778000
msgid ""
"Financial income from financial assets allocated at fair value through "
"profit or loss"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_771000
#: model:account.account,name:l10n_si.2_gd_acc_771000
#: model:account.account.template,name:l10n_si.gd_acc_771000
msgid ""
"Financial income from interests in associates and jointly controlled "
"entities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_774000
#: model:account.account,name:l10n_si.2_gd_acc_774000
#: model:account.account.template,name:l10n_si.gd_acc_774000
msgid "Financial income from loans granted to group organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_775000
#: model:account.account,name:l10n_si.2_gd_acc_775000
#: model:account.account.template,name:l10n_si.gd_acc_775000
msgid "Financial income from loans to others (including deposits)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_773000
#: model:account.account,name:l10n_si.2_gd_acc_773000
#: model:account.account.template,name:l10n_si.gd_acc_773000
msgid "Financial income from other investments"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_772000
#: model:account.account,name:l10n_si.2_gd_acc_772000
#: model:account.account.template,name:l10n_si.gd_acc_772000
msgid "Financial income from shares in other organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_779000
#: model:account.account,name:l10n_si.2_gd_acc_779000
#: model:account.account.template,name:l10n_si.gd_acc_779000
msgid ""
"Financial income from the valuation of investment property at fair value and"
" income from the disposal of investment property measured at fair value"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_776000
#: model:account.account,name:l10n_si.2_gd_acc_776000
#: model:account.account.template,name:l10n_si.gd_acc_776000
msgid "Financial revenues from operating receivables from group organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_777000
#: model:account.account,name:l10n_si.2_gd_acc_777000
#: model:account.account.template,name:l10n_si.gd_acc_777000
msgid "Financial revenues from operating receivables from others"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_770000
#: model:account.account,name:l10n_si.2_gd_acc_770000
#: model:account.account.template,name:l10n_si.gd_acc_770000
msgid "Financial revenues from shares in group organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_752000
#: model:account.account,name:l10n_si.2_gd_acc_752000
#: model:account.account.template,name:l10n_si.gd_acc_752000
msgid "Fines not related to business effects"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_101000
#: model:account.account,name:l10n_si.2_gd_acc_101000
#: model:account.account.template,name:l10n_si.gd_acc_101000
msgid "Foreign currency at the box office"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_112000
#: model:account.account,name:l10n_si.2_gd_acc_112000
#: model:account.account.template,name:l10n_si.gd_acc_112000
msgid "Foreign currency on accounts"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_907000
#: model:account.account,name:l10n_si.2_gd_acc_907000
#: model:account.account.template,name:l10n_si.gd_acc_907000
msgid "Founding and subsequent roles"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_713000
#: model:account.account,name:l10n_si.2_gd_acc_713000
#: model:account.account.template,name:l10n_si.gd_acc_713000
msgid "General and administrative expenses (purchasing and administration)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_916000
#: model:account.account,name:l10n_si.2_gd_acc_916000
#: model:account.account.template,name:l10n_si.gd_acc_916000
msgid ""
"General capital revaluation adjustment and amounts transferred from the "
"revaluation reserve"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_000000
#: model:account.account,name:l10n_si.2_gd_acc_000000
#: model:account.account.template,name:l10n_si.gd_acc_000000
msgid "Good name"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_661000
#: model:account.account,name:l10n_si.2_gd_acc_661000
#: model:account.account.template,name:l10n_si.gd_acc_661000
msgid "Goods in a foreign warehouse"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_663000
#: model:account.account,name:l10n_si.2_gd_acc_663000
#: model:account.account.template,name:l10n_si.gd_acc_663000
msgid "Goods in own store"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_660000
#: model:account.account,name:l10n_si.2_gd_acc_660000
#: model:account.account.template,name:l10n_si.gd_acc_660000
msgid "Goods in own warehouse"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_662000
#: model:account.account,name:l10n_si.2_gd_acc_662000
#: model:account.account.template,name:l10n_si.gd_acc_662000
msgid "Goods on the way"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_992000
#: model:account.account,name:l10n_si.2_gd_acc_992000
#: model:account.account.template,name:l10n_si.gd_acc_992000
msgid "Goods received for commission and consignment sale"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_488000
#: model:account.account,name:l10n_si.2_gd_acc_488000
#: model:account.account.template,name:l10n_si.gd_acc_488000
msgid "Grants to other associations and legal entities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_618000
#: model:account.account,name:l10n_si.2_gd_acc_618000
#: model:account.account.template,name:l10n_si.gd_acc_618000
msgid "Harvested crops at cost"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_610000
#: model:account.account,name:l10n_si.2_gd_acc_610000
#: model:account.account.template,name:l10n_si.gd_acc_610000
msgid "Harvested crops at fair value"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_I
msgid "I. Supplies of goods and services (values excluding VAT)"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_II
msgid "II. VAT charged"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_III
msgid "III. Purchases of goods and services (values excluding VAT)"
msgstr ""

#. module: l10n_si
#: model:account.report.line,name:l10n_si.tax_report_IV
msgid "IV. VAT deduction"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_03
#: model:account.group,name:l10n_si.2_gd_acc_group_03
#: model:account.group.template,name:l10n_si.gd_acc_group_03
msgid "Impairment and impairment of real estate"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_058000
#: model:account.account,name:l10n_si.2_gd_acc_058000
#: model:account.account.template,name:l10n_si.gd_acc_058000
msgid "Impairment of biological assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_039000
#: model:account.account,name:l10n_si.2_gd_acc_039000
#: model:account.account.template,name:l10n_si.gd_acc_039000
msgid "Impairment of buildings"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_052000
#: model:account.account,name:l10n_si.2_gd_acc_052000
#: model:account.account.template,name:l10n_si.gd_acc_052000
msgid "Impairment of equipment and spare parts"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_009000
#: model:account.account,name:l10n_si.2_gd_acc_009000
#: model:account.account.template,name:l10n_si.gd_acc_009000
msgid "Impairment of intangible assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_019000
#: model:account.account,name:l10n_si.2_gd_acc_019000
#: model:account.account.template,name:l10n_si.gd_acc_019000
msgid "Impairment of investment property"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_031000
#: model:account.account,name:l10n_si.2_gd_acc_031000
#: model:account.account.template,name:l10n_si.gd_acc_031000
msgid "Impairment of land value"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_069000
#: model:account.account,name:l10n_si.2_gd_acc_069000
#: model:account.account.template,name:l10n_si.gd_acc_069000
msgid "Impairment of long-term financial investments"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_079000
#: model:account.account,name:l10n_si.2_gd_acc_079000
#: model:account.account.template,name:l10n_si.gd_acc_079000
msgid "Impairment of long-term loans granted"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_089000
#: model:account.account,name:l10n_si.2_gd_acc_089000
#: model:account.account.template,name:l10n_si.gd_acc_089000
msgid "Impairment of long-term operating receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_059000
#: model:account.account,name:l10n_si.2_gd_acc_059000
#: model:account.account.template,name:l10n_si.gd_acc_059000
msgid "Impairment of other property, plant and equipment"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_169000
#: model:account.account,name:l10n_si.2_gd_acc_169000
#: model:account.account.template,name:l10n_si.gd_acc_169000
msgid "Impairment of other short-term receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_139000
#: model:account.account,name:l10n_si.2_gd_acc_139000
#: model:account.account.template,name:l10n_si.gd_acc_139000
msgid "Impairment of short-term advances, overpayments and securities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_179000
#: model:account.account,name:l10n_si.2_gd_acc_179000
#: model:account.account.template,name:l10n_si.gd_acc_179000
msgid "Impairment of short-term financial investments"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_189000
#: model:account.account,name:l10n_si.2_gd_acc_189000
#: model:account.account.template,name:l10n_si.gd_acc_189000
msgid "Impairment of short-term loans"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_149000
#: model:account.account,name:l10n_si.2_gd_acc_149000
#: model:account.account.template,name:l10n_si.gd_acc_149000
msgid "Impairment of short-term operating receivables on behalf of others"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_159000
#: model:account.account,name:l10n_si.2_gd_acc_159000
#: model:account.account.template,name:l10n_si.gd_acc_159000
msgid "Impairment of short-term receivables related to financial income"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_129000
#: model:account.account,name:l10n_si.2_gd_acc_129000
#: model:account.account.template,name:l10n_si.gd_acc_129000
msgid "Impairment of short-term trade receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_669000
#: model:account.account,name:l10n_si.2_gd_acc_669000
#: model:account.account.template,name:l10n_si.gd_acc_669000
msgid "Included difference in prices of stocks of goods"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_634000
#: model:account.account,name:l10n_si.2_gd_acc_634000
#: model:account.account.template,name:l10n_si.gd_acc_634000
msgid "Included vat on products in the store"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_781000
#: model:account.account,name:l10n_si.2_gd_acc_781000
#: model:account.account.template,name:l10n_si.gd_acc_781000
msgid "Income from disposal of investment property measured at fair value"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_935000
#: model:account.account,name:l10n_si.2_gd_acc_935000
#: model:account.account.template,name:l10n_si.gd_acc_935000
msgid "Income of sole proprietors"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_601000
#: model:account.account,name:l10n_si.2_gd_acc_601000
#: model:account.account.template,name:l10n_si.gd_acc_601000
msgid "Incomplete services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_919000
#: model:account.account,name:l10n_si.2_gd_acc_919000
#: model:account.account.template,name:l10n_si.gd_acc_919000
msgid "Inflows and outflows between enterprise and household"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_902000
#: model:account.account,name:l10n_si.2_gd_acc_902000
#: model:account.account.template,name:l10n_si.gd_acc_902000
msgid "Initial capital of sole proprietors"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_905000
#: model:account.account,name:l10n_si.2_gd_acc_905000
#: model:account.account.template,name:l10n_si.gd_acc_905000
msgid "Inseparable cooperative capital"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_00
#: model:account.group,name:l10n_si.2_gd_acc_group_00
#: model:account.group.template,name:l10n_si.gd_acc_group_00
msgid "Intangible assets and long-term accrued costs and deferred revenue"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_450000
#: model:account.account,name:l10n_si.2_gd_acc_450000
#: model:account.account.template,name:l10n_si.gd_acc_450000
#: model:account.group,name:l10n_si.1_gd_acc_group_45
#: model:account.group,name:l10n_si.2_gd_acc_group_45
#: model:account.group.template,name:l10n_si.gd_acc_group_45
msgid "Interest costs"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_6
#: model:account.group,name:l10n_si.2_gd_acc_group_6
#: model:account.group.template,name:l10n_si.gd_acc_group_6
msgid ""
"Inventories of products, services, goods and non-current assets (disposal "
"groups) for sale"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_31
#: model:account.group,name:l10n_si.2_gd_acc_group_31
#: model:account.group.template,name:l10n_si.gd_acc_group_31
msgid "Inventories of raw materials and supplies"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_316000
#: model:account.account,name:l10n_si.2_gd_acc_316000
#: model:account.account.template,name:l10n_si.gd_acc_316000
msgid "Inventories of raw materials and supplies in finishing and processing"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_01
#: model:account.group,name:l10n_si.2_gd_acc_group_01
#: model:account.group.template,name:l10n_si.gd_acc_group_01
msgid "Investment property"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_017000
#: model:account.account,name:l10n_si.2_gd_acc_017000
#: model:account.account.template,name:l10n_si.gd_acc_017000
msgid "Investment property under construction"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_010000
#: model:account.account,name:l10n_si.2_gd_acc_010000
#: model:account.account.template,name:l10n_si.gd_acc_010000
msgid "Investment property valued according to the cost model"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_671000
#: model:account.account,name:l10n_si.2_gd_acc_671000
#: model:account.account.template,name:l10n_si.gd_acc_671000
msgid "Investment property valued at cost"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_011000
#: model:account.account,name:l10n_si.2_gd_acc_011000
#: model:account.account.template,name:l10n_si.gd_acc_011000
msgid "Investment property valued at fair value model"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_046000
#: model:account.account,name:l10n_si.2_gd_acc_046000
#: model:account.account.template,name:l10n_si.gd_acc_046000
msgid "Investments in foreign-owned property, plant and equipment"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_026000
#: model:account.account,name:l10n_si.2_gd_acc_026000
#: model:account.account.template,name:l10n_si.gd_acc_026000
msgid "Investments in foreign-owned real estate"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_47
#: model:account.group,name:l10n_si.2_gd_acc_group_47
#: model:account.group.template,name:l10n_si.gd_acc_group_47
msgid "Labor costs"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_020000
#: model:account.account,name:l10n_si.2_gd_acc_020000
#: model:account.account.template,name:l10n_si.gd_acc_020000
msgid "Land valued according to the cost model"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_022000
#: model:account.account,name:l10n_si.2_gd_acc_022000
#: model:account.account.template,name:l10n_si.gd_acc_022000
msgid "Land valued according to the revaluation model"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_990000
#: model:account.account,name:l10n_si.2_gd_acc_990000
#: model:account.account.template,name:l10n_si.gd_acc_990000
msgid "Leased, borrowed and leased (foreign) assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_920000
#: model:account.account,name:l10n_si.2_gd_acc_920000
#: model:account.account.template,name:l10n_si.gd_acc_920000
msgid "Legal reserves"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_263000
#: model:account.account,name:l10n_si.2_gd_acc_263000
#: model:account.account.template,name:l10n_si.gd_acc_263000
msgid ""
"Liabilities for advance payment of personal income tax on income from "
"activities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_262000
#: model:account.account,name:l10n_si.2_gd_acc_262000
#: model:account.account.template,name:l10n_si.gd_acc_262000
msgid "Liabilities for contributions"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_258000
#: model:account.account,name:l10n_si.2_gd_acc_258000
#: model:account.account.template,name:l10n_si.gd_acc_258000
msgid "Liabilities for payer 's contributions"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_260000
#: model:account.account,name:l10n_si.2_gd_acc_260000
#: model:account.account.template,name:l10n_si.gd_acc_260000
msgid "Liabilities for vat charged"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_261000
#: model:account.account,name:l10n_si.2_gd_acc_261000
#: model:account.account.template,name:l10n_si.gd_acc_261000
msgid "Liabilities for vat, customs and other duties on imported goods"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_997000
#: model:account.account,name:l10n_si.2_gd_acc_997000
#: model:account.account.template,name:l10n_si.gd_acc_997000
msgid "Liabilities from goods received for commission and consignment sales"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_278000
#: model:account.account,name:l10n_si.2_gd_acc_278000
#: model:account.account.template,name:l10n_si.gd_acc_278000
msgid ""
"Liabilities from the payment of capital until entry in the court register"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_210000
#: model:account.account,name:l10n_si.2_gd_acc_210000
#: model:account.account.template,name:l10n_si.gd_acc_210000
#: model:account.group,name:l10n_si.1_gd_acc_group_21
#: model:account.group,name:l10n_si.2_gd_acc_group_21
#: model:account.group.template,name:l10n_si.gd_acc_group_21
msgid "Liabilities included in disposal groups"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_26
#: model:account.group,name:l10n_si.2_gd_acc_group_26
#: model:account.group.template,name:l10n_si.gd_acc_group_26
msgid "Liabilities to state and other institutions"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_chart_liquidity_transfer
#: model:account.account,name:l10n_si.2_gd_chart_liquidity_transfer
#: model:account.account.template,name:l10n_si.gd_chart_liquidity_transfer
msgid "Liquidity Transfer"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_007000
#: model:account.account,name:l10n_si.2_gd_acc_007000
#: model:account.account.template,name:l10n_si.gd_acc_007000
msgid "Long-term accrued costs and deferred revenue"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_986000
#: model:account.account,name:l10n_si.2_gd_acc_986000
#: model:account.account.template,name:l10n_si.gd_acc_986000
msgid "Long-term advances and securities received"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_083000
#: model:account.account,name:l10n_si.2_gd_acc_083000
#: model:account.account.template,name:l10n_si.gd_acc_083000
msgid "Long-term advances given"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_0
#: model:account.group,name:l10n_si.2_gd_acc_group_0
#: model:account.group.template,name:l10n_si.gd_acc_group_0
msgid "Long-term assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_985000
#: model:account.account,name:l10n_si.2_gd_acc_985000
#: model:account.account.template,name:l10n_si.gd_acc_985000
msgid "Long-term bill of exchange liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_081000
#: model:account.account,name:l10n_si.2_gd_acc_081000
#: model:account.account.template,name:l10n_si.gd_acc_081000
msgid "Long-term commodity loans granted abroad"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_080000
#: model:account.account,name:l10n_si.2_gd_acc_080000
#: model:account.account.template,name:l10n_si.gd_acc_080000
msgid "Long-term commodity loans granted in the country"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_082000
#: model:account.account,name:l10n_si.2_gd_acc_082000
#: model:account.account.template,name:l10n_si.gd_acc_082000
msgid "Long-term consumer loans granted"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_078000
#: model:account.account,name:l10n_si.2_gd_acc_078000
#: model:account.account.template,name:l10n_si.gd_acc_078000
msgid "Long-term deposits given"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_085000
#: model:account.account,name:l10n_si.2_gd_acc_085000
#: model:account.account.template,name:l10n_si.gd_acc_085000
msgid "Long-term finance lease receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_060000
#: model:account.account,name:l10n_si.2_gd_acc_060000
#: model:account.account.template,name:l10n_si.gd_acc_060000
msgid ""
"Long-term financial investments in shares and stakes in group organizations,"
" allocated and measured at cost"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_06
#: model:account.group,name:l10n_si.2_gd_acc_group_06
#: model:account.group.template,name:l10n_si.gd_acc_group_06
msgid "Long-term financial investments, except loans"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_984000
#: model:account.account,name:l10n_si.2_gd_acc_984000
#: model:account.account.template,name:l10n_si.gd_acc_984000
msgid "Long-term financial lease debts"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_97
#: model:account.group,name:l10n_si.2_gd_acc_group_97
#: model:account.group.template,name:l10n_si.gd_acc_group_97
msgid "Long-term financial liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_974000
#: model:account.account,name:l10n_si.2_gd_acc_974000
#: model:account.account.template,name:l10n_si.gd_acc_974000
msgid "Long-term financial liabilities related to bonds and bills of exchange"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_976000
#: model:account.account,name:l10n_si.2_gd_acc_976000
#: model:account.account.template,name:l10n_si.gd_acc_976000
msgid "Long-term financial liabilities to individuals"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_063000
#: model:account.account,name:l10n_si.2_gd_acc_063000
#: model:account.account.template,name:l10n_si.gd_acc_063000
msgid ""
"Long-term investments in shares and interests of associates and jointly "
"controlled entities, allocated and measured at cost"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_065000
#: model:account.account,name:l10n_si.2_gd_acc_065000
#: model:account.account.template,name:l10n_si.gd_acc_065000
msgid ""
"Long-term investments in shares and interests of associates and jointly "
"controlled entities, allocated and measured at fair value through equity"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_064000
#: model:account.account,name:l10n_si.2_gd_acc_064000
#: model:account.account.template,name:l10n_si.gd_acc_064000
msgid ""
"Long-term investments in shares and interests of associates and jointly "
"controlled entities, allocated and measured at fair value through profit or "
"loss"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_062000
#: model:account.account,name:l10n_si.2_gd_acc_062000
#: model:account.account.template,name:l10n_si.gd_acc_062000
msgid ""
"Long-term investments in shares and stakes in group companies, allocated and"
" measured at fair value through equity"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_061000
#: model:account.account,name:l10n_si.2_gd_acc_061000
#: model:account.account.template,name:l10n_si.gd_acc_061000
msgid ""
"Long-term investments in shares and stakes in group companies, allocated and"
" measured at fair value through profit or loss"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_975000
#: model:account.account,name:l10n_si.2_gd_acc_975000
#: model:account.account.template,name:l10n_si.gd_acc_975000
msgid "Long-term lease debts"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_07
#: model:account.group,name:l10n_si.2_gd_acc_group_07
#: model:account.group.template,name:l10n_si.gd_acc_group_07
msgid "Long-term loans and receivables given for unpaid called-up capital"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_075000
#: model:account.account,name:l10n_si.2_gd_acc_075000
#: model:account.account.template,name:l10n_si.gd_acc_075000
msgid "Long-term loans given by repurchasing bonds from others"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_074000
#: model:account.account,name:l10n_si.2_gd_acc_074000
#: model:account.account.template,name:l10n_si.gd_acc_074000
msgid ""
"Long-term loans granted by repurchase of bonds from associates and jointly "
"controlled entities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_073000
#: model:account.account,name:l10n_si.2_gd_acc_073000
#: model:account.account.template,name:l10n_si.gd_acc_073000
msgid "Long-term loans granted by repurchasing bonds from group organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_071000
#: model:account.account,name:l10n_si.2_gd_acc_071000
#: model:account.account.template,name:l10n_si.gd_acc_071000
msgid ""
"Long-term loans granted under loan agreements to associates and jointly "
"controlled entities, including long-term finance lease receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_070000
#: model:account.account,name:l10n_si.2_gd_acc_070000
#: model:account.account.template,name:l10n_si.gd_acc_070000
msgid ""
"Long-term loans granted under loan agreements to group organizations, "
"including long-term finance lease receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_971000
#: model:account.account,name:l10n_si.2_gd_acc_971000
#: model:account.account.template,name:l10n_si.gd_acc_971000
msgid "Long-term loans obtained from associated organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_973000
#: model:account.account,name:l10n_si.2_gd_acc_973000
#: model:account.account.template,name:l10n_si.gd_acc_973000
msgid "Long-term loans obtained from banks and organizations abroad"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_972000
#: model:account.account,name:l10n_si.2_gd_acc_972000
#: model:account.account.template,name:l10n_si.gd_acc_972000
msgid "Long-term loans obtained from banks and organizations in the country"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_970000
#: model:account.account,name:l10n_si.2_gd_acc_970000
#: model:account.account.template,name:l10n_si.gd_acc_970000
msgid "Long-term loans obtained from group organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_982000
#: model:account.account,name:l10n_si.2_gd_acc_982000
#: model:account.account.template,name:l10n_si.gd_acc_982000
msgid "Long-term loans obtained from other domestic suppliers"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_983000
#: model:account.account,name:l10n_si.2_gd_acc_983000
#: model:account.account.template,name:l10n_si.gd_acc_983000
msgid "Long-term loans obtained from other foreign suppliers"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_980000
#: model:account.account,name:l10n_si.2_gd_acc_980000
#: model:account.account.template,name:l10n_si.gd_acc_980000
msgid ""
"Long-term loans obtained on the basis of credit agreements from group "
"organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_981000
#: model:account.account,name:l10n_si.2_gd_acc_981000
#: model:account.account.template,name:l10n_si.gd_acc_981000
msgid ""
"Long-term loans obtained under credit agreements from associates and jointly"
" controlled entities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_072000
#: model:account.account,name:l10n_si.2_gd_acc_072000
#: model:account.account.template,name:l10n_si.gd_acc_072000
msgid ""
"Long-term loans to others, including long-term finance lease receivables"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_98
#: model:account.group,name:l10n_si.2_gd_acc_group_98
#: model:account.group.template,name:l10n_si.gd_acc_group_98
msgid "Long-term operating liabilities"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_08
#: model:account.group,name:l10n_si.2_gd_acc_group_08
#: model:account.group.template,name:l10n_si.gd_acc_group_08
msgid "Long-term operating receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_087000
#: model:account.account,name:l10n_si.2_gd_acc_087000
#: model:account.account.template,name:l10n_si.gd_acc_087000
msgid "Long-term operating receivables from group companies"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_076000
#: model:account.account,name:l10n_si.2_gd_acc_076000
#: model:account.account.template,name:l10n_si.gd_acc_076000
msgid "Long-term receivables for unpaid called - up capital"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_084000
#: model:account.account,name:l10n_si.2_gd_acc_084000
#: model:account.account.template,name:l10n_si.gd_acc_084000
msgid "Long-term securities given"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_477000
#: model:account.account,name:l10n_si.2_gd_acc_477000
#: model:account.account.template,name:l10n_si.gd_acc_477000
msgid "Management costs charged on a basis other than employment"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_400000
#: model:account.account,name:l10n_si.2_gd_acc_400000
#: model:account.account.template,name:l10n_si.gd_acc_400000
#: model:account.group,name:l10n_si.1_gd_acc_group_40
#: model:account.group,name:l10n_si.2_gd_acc_group_40
#: model:account.group.template,name:l10n_si.gd_acc_group_40
msgid "Material costs"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_109000
#: model:account.account,name:l10n_si.2_gd_acc_109000
#: model:account.account.template,name:l10n_si.gd_acc_109000
msgid "Money on the go"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_758000
#: model:account.account,name:l10n_si.2_gd_acc_758000
#: model:account.account.template,name:l10n_si.gd_acc_758000
msgid "Negative euro equalizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_937000
#: model:account.account,name:l10n_si.2_gd_acc_937000
#: model:account.account.template,name:l10n_si.gd_acc_937000
msgid "Negative profit of sole proprietors"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_933000
#: model:account.account,name:l10n_si.2_gd_acc_933000
#: model:account.account.template,name:l10n_si.gd_acc_933000
msgid "Net loss for the financial year or net surplus of expenses"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_89
#: model:account.group,name:l10n_si.2_gd_acc_group_89
#: model:account.group.template,name:l10n_si.gd_acc_group_89
msgid ""
"Net loss or net surplus of expenses and transfer of net loss or net surplus "
"of expenses"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_815000
#: model:account.account,name:l10n_si.2_gd_acc_815000
#: model:account.account.template,name:l10n_si.gd_acc_815000
msgid "Net profit for the financial year or net surplus of revenues"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_93
#: model:account.group,name:l10n_si.2_gd_acc_group_93
#: model:account.group.template,name:l10n_si.gd_acc_group_93
msgid ""
"Net profit or net loss or unallocated net surplus of revenues or net surplus"
" of expenses"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_998000
#: model:account.account,name:l10n_si.2_gd_acc_998000
#: model:account.account.template,name:l10n_si.gd_acc_998000
msgid ""
"Nominal value of securities issued for accounting within the organization"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_67
#: model:account.group,name:l10n_si.2_gd_acc_group_67
#: model:account.group.template,name:l10n_si.gd_acc_group_67
msgid "Non-current assets (disposal groups) for sale"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_99
#: model:account.group,name:l10n_si.2_gd_acc_group_99
#: model:account.group.template,name:l10n_si.gd_acc_group_99
msgid "Off-balance sheet accounts"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_70
#: model:account.group,name:l10n_si.2_gd_acc_group_70
#: model:account.group.template,name:l10n_si.gd_acc_group_70
msgid "Operating expenses (I. version of the income statement)"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_71
#: model:account.group,name:l10n_si.2_gd_acc_group_71
#: model:account.group.template,name:l10n_si.gd_acc_group_71
msgid "Operating expenses (II. Version of the income statement)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_994000
#: model:account.account,name:l10n_si.2_gd_acc_994000
#: model:account.account.template,name:l10n_si.gd_acc_994000
msgid "Other active off-balance sheet accounts"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_914000
#: model:account.account,name:l10n_si.2_gd_acc_914000
#: model:account.account.template,name:l10n_si.gd_acc_914000
msgid "Other capital payments based on the articles of association"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_489000
#: model:account.account,name:l10n_si.2_gd_acc_489000
#: model:account.account.template,name:l10n_si.gd_acc_489000
#: model:account.group,name:l10n_si.1_gd_acc_group_48
#: model:account.group,name:l10n_si.2_gd_acc_group_48
#: model:account.group.template,name:l10n_si.gd_acc_group_48
msgid "Other costs"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_714000
#: model:account.account,name:l10n_si.2_gd_acc_714000
#: model:account.account.template,name:l10n_si.gd_acc_714000
msgid "Other costs not held in stock"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_407000
#: model:account.account,name:l10n_si.2_gd_acc_407000
#: model:account.account.template,name:l10n_si.gd_acc_407000
msgid "Other costs of material"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_28
#: model:account.group,name:l10n_si.2_gd_acc_group_28
#: model:account.group.template,name:l10n_si.gd_acc_group_28
msgid "Other current liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_245000
#: model:account.account,name:l10n_si.2_gd_acc_245000
#: model:account.account.template,name:l10n_si.gd_acc_245000
msgid "Other current liabilities for foreign account"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_475000
#: model:account.account,name:l10n_si.2_gd_acc_475000
#: model:account.account.template,name:l10n_si.gd_acc_475000
msgid ""
"Other employer's benefits from salaries, wage compensations, bonuses, "
"refunds and other employee benefits"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_75
#: model:account.group,name:l10n_si.2_gd_acc_group_75
#: model:account.group.template,name:l10n_si.gd_acc_group_75
msgid "Other expenses"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_78
#: model:account.group,name:l10n_si.2_gd_acc_group_78
#: model:account.group.template,name:l10n_si.gd_acc_group_78
msgid "Other incomes"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_005000
#: model:account.account,name:l10n_si.2_gd_acc_005000
#: model:account.account.template,name:l10n_si.gd_acc_005000
msgid "Other intangible assets (including allowances)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_478000
#: model:account.account,name:l10n_si.2_gd_acc_478000
#: model:account.account.template,name:l10n_si.gd_acc_478000
msgid "Other labor costs"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_968000
#: model:account.account,name:l10n_si.2_gd_acc_968000
#: model:account.account.template,name:l10n_si.gd_acc_968000
msgid "Other long-term accrued costs and deferred revenue"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_067000
#: model:account.account,name:l10n_si.2_gd_acc_067000
#: model:account.account.template,name:l10n_si.gd_acc_067000
msgid ""
"Other long-term financial investments allocated and measured at fair value "
"through profit or loss"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_066000
#: model:account.account,name:l10n_si.2_gd_acc_066000
#: model:account.account.template,name:l10n_si.gd_acc_066000
msgid "Other long-term financial investments, allocated and measured at cost"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_068000
#: model:account.account,name:l10n_si.2_gd_acc_068000
#: model:account.account.template,name:l10n_si.gd_acc_068000
msgid ""
"Other long-term financial investments, allocated and measured at fair value "
"through equity or own source of funds"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_979000
#: model:account.account,name:l10n_si.2_gd_acc_979000
#: model:account.account.template,name:l10n_si.gd_acc_979000
msgid "Other long-term financial liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_077000
#: model:account.account,name:l10n_si.2_gd_acc_077000
#: model:account.account.template,name:l10n_si.gd_acc_077000
msgid "Other long-term investments"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_989000
#: model:account.account,name:l10n_si.2_gd_acc_989000
#: model:account.account.template,name:l10n_si.gd_acc_989000
msgid "Other long-term operating liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_086000
#: model:account.account,name:l10n_si.2_gd_acc_086000
#: model:account.account.template,name:l10n_si.gd_acc_086000
msgid "Other long-term operating receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_672000
#: model:account.account,name:l10n_si.2_gd_acc_672000
#: model:account.account.template,name:l10n_si.gd_acc_672000
msgid "Other non-current assets held for sale"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_759000
#: model:account.account,name:l10n_si.2_gd_acc_759000
#: model:account.account.template,name:l10n_si.gd_acc_759000
msgid "Other non-operating expenses"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_789000
#: model:account.account,name:l10n_si.2_gd_acc_789000
#: model:account.account.template,name:l10n_si.gd_acc_789000
msgid "Other non-operating income"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_703000
#: model:account.account,name:l10n_si.2_gd_acc_703000
#: model:account.account.template,name:l10n_si.gd_acc_703000
msgid "Other operating expenses"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_999000
#: model:account.account,name:l10n_si.2_gd_acc_999000
#: model:account.account.template,name:l10n_si.gd_acc_999000
msgid "Other passive off-balance sheet accounts"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_923000
#: model:account.account,name:l10n_si.2_gd_acc_923000
#: model:account.account.template,name:l10n_si.gd_acc_923000
msgid "Other profit reserves"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_965000
#: model:account.account,name:l10n_si.2_gd_acc_965000
#: model:account.account.template,name:l10n_si.gd_acc_965000
msgid "Other provisions for long-term accrued expenses"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_724000
#: model:account.account,name:l10n_si.2_gd_acc_724000
#: model:account.account.template,name:l10n_si.gd_acc_724000
msgid ""
"Other revaluation operating expenses related to current assets, except "
"financial investments"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_768000
#: model:account.account,name:l10n_si.2_gd_acc_768000
#: model:account.account.template,name:l10n_si.gd_acc_768000
msgid ""
"Other revenues related to business performance (subsidies, grants, "
"resources, compensations, premiums ...)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_133000
#: model:account.account,name:l10n_si.2_gd_acc_133000
#: model:account.account.template,name:l10n_si.gd_acc_133000
msgid "Other short-term advances and overpayments given"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_177000
#: model:account.account,name:l10n_si.2_gd_acc_177000
#: model:account.account.template,name:l10n_si.gd_acc_177000
msgid ""
"Other short-term financial investments allocated and measured at fair value "
"through profit or loss"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_176000
#: model:account.account,name:l10n_si.2_gd_acc_176000
#: model:account.account.template,name:l10n_si.gd_acc_176000
msgid "Other short-term financial investments classified and measured at cost"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_178000
#: model:account.account,name:l10n_si.2_gd_acc_178000
#: model:account.account.template,name:l10n_si.gd_acc_178000
msgid ""
"Other short-term financial investments, allocated and measured at fair value"
" through equity or own source of funds"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_279000
#: model:account.account,name:l10n_si.2_gd_acc_279000
#: model:account.account.template,name:l10n_si.gd_acc_279000
msgid "Other short-term financial liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_266000
#: model:account.account,name:l10n_si.2_gd_acc_266000
#: model:account.account.template,name:l10n_si.gd_acc_266000
msgid "Other short-term liabilities to state and other institutions"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_285000
#: model:account.account,name:l10n_si.2_gd_acc_285000
#: model:account.account.template,name:l10n_si.gd_acc_285000
msgid "Other short-term operating liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_145000
#: model:account.account,name:l10n_si.2_gd_acc_145000
#: model:account.account.template,name:l10n_si.gd_acc_145000
msgid "Other short-term operating receivables on behalf of others"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_165000
#: model:account.account,name:l10n_si.2_gd_acc_165000
#: model:account.account.template,name:l10n_si.gd_acc_165000
#: model:account.group,name:l10n_si.1_gd_acc_group_16
#: model:account.group,name:l10n_si.2_gd_acc_group_16
#: model:account.group.template,name:l10n_si.gd_acc_group_16
msgid "Other short-term receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_164000
#: model:account.account,name:l10n_si.2_gd_acc_164000
#: model:account.account.template,name:l10n_si.gd_acc_164000
msgid "Other short-term receivables from state and other institutions"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_155000
#: model:account.account,name:l10n_si.2_gd_acc_155000
#: model:account.account.template,name:l10n_si.gd_acc_155000
msgid "Other short-term receivables related to financial income"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_045000
#: model:account.account,name:l10n_si.2_gd_acc_045000
#: model:account.account.template,name:l10n_si.gd_acc_045000
msgid "Other tangible fixed assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_812000
#: model:account.account,name:l10n_si.2_gd_acc_812000
#: model:account.account.template,name:l10n_si.gd_acc_812000
msgid "Other taxes not included in other items"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_995000
#: model:account.account,name:l10n_si.2_gd_acc_995000
#: model:account.account.template,name:l10n_si.gd_acc_995000
msgid "Owners of leased, borrowed and leased assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_926000
#: model:account.account,name:l10n_si.2_gd_acc_926000
#: model:account.account.template,name:l10n_si.gd_acc_926000
msgid "Part of the net surplus of revenue for a specific purpose (fund)"
msgstr ""

#. module: l10n_si
#: model:account.fiscal.position,name:l10n_si.1_gd_fp_ne
#: model:account.fiscal.position,name:l10n_si.2_gd_fp_ne
#: model:account.fiscal.position.template,name:l10n_si.gd_fp_ne
msgid "Partner outside the EU"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_415000
#: model:account.account,name:l10n_si.2_gd_acc_415000
#: model:account.account.template,name:l10n_si.gd_acc_415000
msgid ""
"Payment transaction costs, banking services costs, transaction costs and "
"insurance premiums"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_912000
#: model:account.account,name:l10n_si.2_gd_acc_912000
#: model:account.account.template,name:l10n_si.gd_acc_912000
msgid ""
"Payments above the minimum issue amount of capital obtained by issuing "
"convertible bonds and bonds with a share option"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_910000
#: model:account.account,name:l10n_si.2_gd_acc_910000
#: model:account.account.template,name:l10n_si.gd_acc_910000
msgid ""
"Payments above the minimum issue amounts of shares or stakes (paid-in "
"capital surplus)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_913000
#: model:account.account,name:l10n_si.2_gd_acc_913000
#: model:account.account.template,name:l10n_si.gd_acc_913000
msgid ""
"Payments for the acquisition of additional rights from shares or stakes"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_911000
#: model:account.account,name:l10n_si.2_gd_acc_911000
#: model:account.account.template,name:l10n_si.gd_acc_911000
msgid ""
"Payments over the book value in case of disposal of temporarily repurchased "
"own shares or stakes"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_787000
#: model:account.account,name:l10n_si.2_gd_acc_787000
#: model:account.account.template,name:l10n_si.gd_acc_787000
msgid "Penalties not related to business effects"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_788000
#: model:account.account,name:l10n_si.2_gd_acc_788000
#: model:account.account.template,name:l10n_si.gd_acc_788000
msgid "Positive euro equalizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_604000
#: model:account.account,name:l10n_si.2_gd_acc_604000
#: model:account.account.template,name:l10n_si.gd_acc_604000
msgid "Production in finishing and processing"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_63
#: model:account.group,name:l10n_si.2_gd_acc_group_63
#: model:account.group.template,name:l10n_si.gd_acc_group_63
msgid "Products"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_631000
#: model:account.account,name:l10n_si.2_gd_acc_631000
#: model:account.account.template,name:l10n_si.gd_acc_631000
msgid "Products in a foreign warehouse"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_635000
#: model:account.account,name:l10n_si.2_gd_acc_635000
#: model:account.account.template,name:l10n_si.gd_acc_635000
msgid "Products in finishing and processing"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_633000
#: model:account.account,name:l10n_si.2_gd_acc_633000
#: model:account.account.template,name:l10n_si.gd_acc_633000
msgid "Products in our own store"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_630000
#: model:account.account,name:l10n_si.2_gd_acc_630000
#: model:account.account.template,name:l10n_si.gd_acc_630000
msgid "Products in own warehouse"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_632000
#: model:account.account,name:l10n_si.2_gd_acc_632000
#: model:account.account.template,name:l10n_si.gd_acc_632000
msgid "Products on the go"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_8
#: model:account.group,name:l10n_si.2_gd_acc_group_8
#: model:account.group.template,name:l10n_si.gd_acc_group_8
msgid "Profit or loss"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_80
#: model:account.group,name:l10n_si.2_gd_acc_group_80
#: model:account.group.template,name:l10n_si.gd_acc_group_80
msgid "Profit or loss before tax"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_92
#: model:account.group,name:l10n_si.2_gd_acc_group_92
#: model:account.group.template,name:l10n_si.gd_acc_group_92
msgid "Profit reserves or allocated net surplus of revenues"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_003000
#: model:account.account,name:l10n_si.2_gd_acc_003000
#: model:account.account.template,name:l10n_si.gd_acc_003000
msgid "Property and other rights"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_96
#: model:account.group,name:l10n_si.2_gd_acc_group_96
#: model:account.group.template,name:l10n_si.gd_acc_group_96
msgid "Provisions and long-term accrued costs and deferred revenue"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_440000
#: model:account.account,name:l10n_si.1_gd_acc_960000
#: model:account.account,name:l10n_si.2_gd_acc_440000
#: model:account.account,name:l10n_si.2_gd_acc_960000
#: model:account.account.template,name:l10n_si.gd_acc_440000
#: model:account.account.template,name:l10n_si.gd_acc_960000
msgid "Provisions for organizational reorganization costs"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_443000
#: model:account.account,name:l10n_si.1_gd_acc_479000
#: model:account.account,name:l10n_si.1_gd_acc_963000
#: model:account.account,name:l10n_si.2_gd_acc_443000
#: model:account.account,name:l10n_si.2_gd_acc_479000
#: model:account.account,name:l10n_si.2_gd_acc_963000
#: model:account.account.template,name:l10n_si.gd_acc_443000
#: model:account.account.template,name:l10n_si.gd_acc_479000
#: model:account.account.template,name:l10n_si.gd_acc_963000
msgid "Provisions for pensions, jubilee awards and retirement benefits"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_961000
#: model:account.account,name:l10n_si.2_gd_acc_961000
#: model:account.account.template,name:l10n_si.gd_acc_961000
msgid ""
"Provisions to cover future costs or expenses due to decommissioning and "
"restoration and other similar provisions"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_449000
#: model:account.account,name:l10n_si.2_gd_acc_449000
#: model:account.account.template,name:l10n_si.gd_acc_449000
msgid "Provisions to cover other liabilities from past operations"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_02
#: model:account.group,name:l10n_si.2_gd_acc_group_02
#: model:account.group.template,name:l10n_si.gd_acc_group_02
msgid "Real estate"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_027000
#: model:account.account,name:l10n_si.2_gd_acc_027000
#: model:account.account.template,name:l10n_si.gd_acc_027000
msgid "Real estate under construction"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_103000
#: model:account.account,name:l10n_si.2_gd_acc_103000
#: model:account.account.template,name:l10n_si.gd_acc_103000
msgid "Receipts received"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_405000
#: model:account.account,name:l10n_si.2_gd_acc_405000
#: model:account.account.template,name:l10n_si.gd_acc_405000
msgid ""
"Reconciliation of material costs and small inventory due to identified "
"inventory differences"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_486000
#: model:account.account,name:l10n_si.2_gd_acc_486000
#: model:account.account.template,name:l10n_si.gd_acc_486000
msgid "Reimbursement of costs to sole proprietors"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_414000
#: model:account.account,name:l10n_si.2_gd_acc_414000
#: model:account.account.template,name:l10n_si.gd_acc_414000
msgid "Reimbursement of expenses to employees in connection with work"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_765000
#: model:account.account,name:l10n_si.2_gd_acc_765000
#: model:account.account.template,name:l10n_si.gd_acc_765000
msgid "Rental income"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_413000
#: model:account.account,name:l10n_si.2_gd_acc_413000
#: model:account.account.template,name:l10n_si.gd_acc_413000
msgid "Rents"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_44
#: model:account.group,name:l10n_si.2_gd_acc_group_44
#: model:account.group.template,name:l10n_si.gd_acc_group_44
msgid "Reservations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_442000
#: model:account.account,name:l10n_si.1_gd_acc_962000
#: model:account.account,name:l10n_si.2_gd_acc_442000
#: model:account.account,name:l10n_si.2_gd_acc_962000
#: model:account.account.template,name:l10n_si.gd_acc_442000
#: model:account.account.template,name:l10n_si.gd_acc_962000
msgid "Reservations for tricky contracts"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_441000
#: model:account.account,name:l10n_si.1_gd_acc_964000
#: model:account.account,name:l10n_si.2_gd_acc_441000
#: model:account.account,name:l10n_si.2_gd_acc_964000
#: model:account.account.template,name:l10n_si.gd_acc_441000
#: model:account.account.template,name:l10n_si.gd_acc_964000
msgid "Reservations for warranty days"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_95
#: model:account.group,name:l10n_si.2_gd_acc_group_95
#: model:account.group.template,name:l10n_si.gd_acc_group_95
msgid "Reserves arising from fair value measurement"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_954000
#: model:account.account,name:l10n_si.2_gd_acc_954000
#: model:account.account.template,name:l10n_si.gd_acc_954000
msgid ""
"Reserves arising from the valuation of long-term financial investments at "
"fair value"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_955000
#: model:account.account,name:l10n_si.2_gd_acc_955000
#: model:account.account.template,name:l10n_si.gd_acc_955000
msgid ""
"Reserves arising from the valuation of short-term financial investments at "
"fair value"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_921000
#: model:account.account,name:l10n_si.2_gd_acc_921000
#: model:account.account.template,name:l10n_si.gd_acc_921000
msgid "Reserves for own shares or own business shares"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_930000
#: model:account.account,name:l10n_si.2_gd_acc_930000
#: model:account.account.template,name:l10n_si.gd_acc_930000
msgid "Retained earnings from previous years"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_72
#: model:account.group,name:l10n_si.2_gd_acc_group_72
#: model:account.group.template,name:l10n_si.gd_acc_group_72
msgid "Revaluation operating expenses"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_722000
#: model:account.account,name:l10n_si.2_gd_acc_722000
#: model:account.account.template,name:l10n_si.gd_acc_722000
msgid ""
"Revaluation operating expenses as a result of revaluation due to impairment "
"in relation to operating receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_723000
#: model:account.account,name:l10n_si.2_gd_acc_723000
#: model:account.account.template,name:l10n_si.gd_acc_723000
msgid ""
"Revaluation operating expenses as a result of write-offs related to "
"operating receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_720000
#: model:account.account,name:l10n_si.2_gd_acc_720000
#: model:account.account.template,name:l10n_si.gd_acc_720000
msgid ""
"Revaluation operating expenses related to intangible assets, property, plant"
" and equipment and investment property"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_721000
#: model:account.account,name:l10n_si.2_gd_acc_721000
#: model:account.account.template,name:l10n_si.gd_acc_721000
msgid "Revaluation operating expenses related to inventories"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_769000
#: model:account.account,name:l10n_si.2_gd_acc_769000
#: model:account.account.template,name:l10n_si.gd_acc_769000
msgid "Revaluation operating revenues"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_94
#: model:account.group,name:l10n_si.2_gd_acc_group_94
#: model:account.group.template,name:l10n_si.gd_acc_group_94
msgid "Revaluation reserves"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_940000
#: model:account.account,name:l10n_si.2_gd_acc_940000
#: model:account.account.template,name:l10n_si.gd_acc_940000
msgid "Revaluation reserves from land revaluation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_941000
#: model:account.account,name:l10n_si.2_gd_acc_941000
#: model:account.account.template,name:l10n_si.gd_acc_941000
msgid "Revaluation reserves from revaluation of buildings"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_763100
#: model:account.account,name:l10n_si.2_gd_acc_763100
#: model:account.account.template,name:l10n_si.gd_acc_763100
msgid "Revenue from the sale of merchandise and materials on the eu market"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_763200
#: model:account.account,name:l10n_si.2_gd_acc_763200
#: model:account.account.template,name:l10n_si.gd_acc_763200
msgid ""
"Revenue from the sale of merchandise and materials outside the eu market"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_780000
#: model:account.account,name:l10n_si.2_gd_acc_780000
#: model:account.account.template,name:l10n_si.gd_acc_780000
msgid "Revenue from the valuation of investment property at fair value"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_767000
#: model:account.account,name:l10n_si.2_gd_acc_767000
#: model:account.account.template,name:l10n_si.gd_acc_767000
msgid "Revenues from business combinations (revaluation surplus - bad name)"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_763000
#: model:account.account,name:l10n_si.2_gd_acc_763000
#: model:account.account.template,name:l10n_si.gd_acc_763000
msgid "Revenues from sales of merchandise and materials on foreign markets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_762000
#: model:account.account,name:l10n_si.2_gd_acc_762000
#: model:account.account.template,name:l10n_si.gd_acc_762000
msgid ""
"Revenues from sales of merchandise and materials on the domestic market"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_761000
#: model:account.account,name:l10n_si.2_gd_acc_761000
#: model:account.account.template,name:l10n_si.gd_acc_761000
msgid "Revenues from sales of products and services on foreign markets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_760000
#: model:account.account,name:l10n_si.2_gd_acc_760000
#: model:account.account.template,name:l10n_si.gd_acc_760000
msgid "Revenues from sales of products and services on the domestic market"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_761100
#: model:account.account,name:l10n_si.2_gd_acc_761100
#: model:account.account.template,name:l10n_si.gd_acc_761100
msgid "Revenues from sales of products and services on the eu market"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_761200
#: model:account.account,name:l10n_si.2_gd_acc_761200
#: model:account.account.template,name:l10n_si.gd_acc_761200
msgid "Revenues from sales of products and services outside the eu market"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_766000
#: model:account.account,name:l10n_si.2_gd_acc_766000
#: model:account.account.template,name:l10n_si.gd_acc_766000
msgid ""
"Revenues from the elimination of provisions and accrued costs and deferred "
"revenue at the expense of accrued costs or expenses"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_764000
#: model:account.account,name:l10n_si.2_gd_acc_764000
#: model:account.account.template,name:l10n_si.gd_acc_764000
msgid ""
"Revenues from valuation of biological assets and harvesting of agricultural "
"products"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_476000
#: model:account.account,name:l10n_si.2_gd_acc_476000
#: model:account.account.template,name:l10n_si.gd_acc_476000
msgid "Rewards to apprentices along with levies charged to the organization"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_104000
#: model:account.account,name:l10n_si.2_gd_acc_104000
#: model:account.account.template,name:l10n_si.gd_acc_104000
msgid "Risk-free immediately redeemable debt securities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_483000
#: model:account.account,name:l10n_si.2_gd_acc_483000
#: model:account.account.template,name:l10n_si.gd_acc_483000
msgid "Scholarships for high school and university students"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_192000
#: model:account.account,name:l10n_si.2_gd_acc_192000
#: model:account.account.template,name:l10n_si.gd_acc_192000
msgid "Securities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_993000
#: model:account.account,name:l10n_si.2_gd_acc_993000
#: model:account.account.template,name:l10n_si.gd_acc_993000
msgid "Securities issued for accounting within the organization"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_712000
#: model:account.account,name:l10n_si.2_gd_acc_712000
#: model:account.account.template,name:l10n_si.gd_acc_712000
msgid "Selling costs"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_602000
#: model:account.account,name:l10n_si.2_gd_acc_602000
#: model:account.account.template,name:l10n_si.gd_acc_602000
msgid "Semi-finished products"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_903000
#: model:account.account,name:l10n_si.2_gd_acc_903000
#: model:account.account.template,name:l10n_si.gd_acc_903000
msgid "Share capital - capital deposit"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_901000
#: model:account.account,name:l10n_si.2_gd_acc_901000
#: model:account.account.template,name:l10n_si.gd_acc_901000
msgid "Share capital - capital shares or capital contribution"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_900000
#: model:account.account,name:l10n_si.2_gd_acc_900000
#: model:account.account.template,name:l10n_si.gd_acc_900000
msgid "Share capital - shares"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_906000
#: model:account.account,name:l10n_si.2_gd_acc_906000
#: model:account.account.template,name:l10n_si.gd_acc_906000
msgid "Shares of cooperative members"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_19
#: model:account.group,name:l10n_si.2_gd_acc_group_19
#: model:account.group.template,name:l10n_si.gd_acc_group_19
msgid "Short-term accrued costs and deferred revenue"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_191000
#: model:account.account,name:l10n_si.2_gd_acc_191000
#: model:account.account.template,name:l10n_si.gd_acc_191000
msgid "Short-term accrued income"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_13
#: model:account.group,name:l10n_si.2_gd_acc_group_13
#: model:account.group.template,name:l10n_si.gd_acc_group_13
msgid "Short-term advances and securities given"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_23
#: model:account.group,name:l10n_si.2_gd_acc_group_23
#: model:account.group.template,name:l10n_si.gd_acc_group_23
msgid "Short-term advances and securities received"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_131000
#: model:account.account,name:l10n_si.2_gd_acc_131000
#: model:account.account.template,name:l10n_si.gd_acc_131000
msgid "Short-term advances given for intangible assets"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_130000
#: model:account.account,name:l10n_si.2_gd_acc_130000
#: model:account.account.template,name:l10n_si.gd_acc_130000
msgid "Short-term advances given for property, plant and equipment"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_132000
#: model:account.account,name:l10n_si.2_gd_acc_132000
#: model:account.account.template,name:l10n_si.gd_acc_132000
msgid ""
"Short-term advances paid for inventories of materials and goods and services"
" not yet performed"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_230000
#: model:account.account,name:l10n_si.2_gd_acc_230000
#: model:account.account.template,name:l10n_si.gd_acc_230000
msgid "Short-term advances received"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_1
#: model:account.group,name:l10n_si.2_gd_acc_group_1
#: model:account.group.template,name:l10n_si.gd_acc_group_1
msgid ""
"Short-term assets, except inventories, and short-term accrued and deferred "
"income"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_281000
#: model:account.account,name:l10n_si.2_gd_acc_281000
#: model:account.account.template,name:l10n_si.gd_acc_281000
msgid "Short-term bill of exchange liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_123000
#: model:account.account,name:l10n_si.2_gd_acc_123000
#: model:account.account.template,name:l10n_si.gd_acc_123000
msgid "Short-term commodity loans given to customers abroad"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_122000
#: model:account.account,name:l10n_si.2_gd_acc_122000
#: model:account.account.template,name:l10n_si.gd_acc_122000
msgid "Short-term commodity loans given to customers in the country"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_223000
#: model:account.account,name:l10n_si.2_gd_acc_223000
#: model:account.account.template,name:l10n_si.gd_acc_223000
msgid "Short-term commodity loans received abroad"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_222000
#: model:account.account,name:l10n_si.2_gd_acc_222000
#: model:account.account.template,name:l10n_si.gd_acc_222000
msgid "Short-term commodity loans received in the country"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_124000
#: model:account.account,name:l10n_si.2_gd_acc_124000
#: model:account.account.template,name:l10n_si.gd_acc_124000
msgid "Short-term consumer loans given to customers in the country"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_161000
#: model:account.account,name:l10n_si.2_gd_acc_161000
#: model:account.account.template,name:l10n_si.gd_acc_161000
msgid "Short-term corporate income tax receivables, including tax paid abroad"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_190000
#: model:account.account,name:l10n_si.2_gd_acc_190000
#: model:account.account.template,name:l10n_si.gd_acc_190000
msgid "Short-term deferred costs or expenses"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_291000
#: model:account.account,name:l10n_si.2_gd_acc_291000
#: model:account.account.template,name:l10n_si.gd_acc_291000
msgid "Short-term deferred income"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_183000
#: model:account.account,name:l10n_si.2_gd_acc_183000
#: model:account.account.template,name:l10n_si.gd_acc_183000
msgid "Short-term deposits with banks and other financial institutions"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_151000
#: model:account.account,name:l10n_si.2_gd_acc_151000
#: model:account.account.template,name:l10n_si.gd_acc_151000
msgid "Short-term dividend receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_240000
#: model:account.account,name:l10n_si.2_gd_acc_240000
#: model:account.account.template,name:l10n_si.gd_acc_240000
msgid "Short-term export liabilities for foreign account"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_170000
#: model:account.account,name:l10n_si.2_gd_acc_170000
#: model:account.account.template,name:l10n_si.gd_acc_170000
msgid ""
"Short-term financial investments in shares and stakes in group "
"organizations, allocated and measured at cost"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_17
#: model:account.group,name:l10n_si.2_gd_acc_group_17
#: model:account.group.template,name:l10n_si.gd_acc_group_17
msgid "Short-term financial investments, except loans"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_27
#: model:account.group,name:l10n_si.2_gd_acc_group_27
#: model:account.group.template,name:l10n_si.gd_acc_group_27
msgid "Short-term financial liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_274000
#: model:account.account,name:l10n_si.2_gd_acc_274000
#: model:account.account.template,name:l10n_si.gd_acc_274000
msgid "Short-term financial liabilities related to bonds"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_276000
#: model:account.account,name:l10n_si.2_gd_acc_276000
#: model:account.account.template,name:l10n_si.gd_acc_276000
msgid "Short-term financial liabilities to individuals"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_113000
#: model:account.account,name:l10n_si.2_gd_acc_113000
#: model:account.account.template,name:l10n_si.gd_acc_113000
msgid ""
"Short-term foreign currency deposits or callable foreign currency deposits"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_141000
#: model:account.account,name:l10n_si.2_gd_acc_141000
#: model:account.account.template,name:l10n_si.gd_acc_141000
msgid "Short-term import receivables for foreign account"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_280000
#: model:account.account,name:l10n_si.2_gd_acc_280000
#: model:account.account.template,name:l10n_si.gd_acc_280000
msgid "Short-term interest liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_150000
#: model:account.account,name:l10n_si.2_gd_acc_150000
#: model:account.account.template,name:l10n_si.gd_acc_150000
msgid "Short-term interest receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_173000
#: model:account.account,name:l10n_si.2_gd_acc_173000
#: model:account.account.template,name:l10n_si.gd_acc_173000
msgid ""
"Short-term investments in shares and interests of associates and jointly "
"controlled entities, allocated and measured at cost"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_172000
#: model:account.account,name:l10n_si.2_gd_acc_172000
#: model:account.account.template,name:l10n_si.gd_acc_172000
msgid ""
"Short-term investments in shares and stakes in group companies, allocated "
"and measured at fair value through equity"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_171000
#: model:account.account,name:l10n_si.2_gd_acc_171000
#: model:account.account.template,name:l10n_si.gd_acc_171000
msgid ""
"Short-term investments in shares and stakes in group companies, allocated "
"and measured at fair value through profit or loss"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_275000
#: model:account.account,name:l10n_si.2_gd_acc_275000
#: model:account.account.template,name:l10n_si.gd_acc_275000
msgid "Short-term lease liabilities"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_2
#: model:account.group,name:l10n_si.2_gd_acc_group_2
#: model:account.group.template,name:l10n_si.gd_acc_group_2
msgid ""
"Short-term liabilities (debt) and short-term accrued and deferred income"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_224000
#: model:account.account,name:l10n_si.2_gd_acc_224000
#: model:account.account.template,name:l10n_si.gd_acc_224000
msgid "Short-term liabilities (debts) for unbilled goods and services"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_22
#: model:account.group,name:l10n_si.2_gd_acc_group_22
#: model:account.group.template,name:l10n_si.gd_acc_group_22
msgid "Short-term liabilities (debts) to suppliers"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_221000
#: model:account.account,name:l10n_si.2_gd_acc_221000
#: model:account.account.template,name:l10n_si.gd_acc_221000
msgid "Short-term liabilities (debts) to suppliers abroad"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_220000
#: model:account.account,name:l10n_si.2_gd_acc_220000
#: model:account.account.template,name:l10n_si.gd_acc_220000
msgid "Short-term liabilities (debts) to suppliers in the country"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_250000
#: model:account.account,name:l10n_si.2_gd_acc_250000
#: model:account.account.template,name:l10n_si.gd_acc_250000
msgid "Short-term liabilities for accrued and unaccounted salaries"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_253000
#: model:account.account,name:l10n_si.2_gd_acc_253000
#: model:account.account.template,name:l10n_si.gd_acc_253000
msgid "Short-term liabilities for contributions from gross wages and salaries"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_256000
#: model:account.account,name:l10n_si.2_gd_acc_256000
#: model:account.account.template,name:l10n_si.gd_acc_256000
msgid ""
"Short-term liabilities for contributions from other benefits from "
"employment, which are not calculated together with salaries"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_251000
#: model:account.account,name:l10n_si.2_gd_acc_251000
#: model:account.account.template,name:l10n_si.gd_acc_251000
msgid "Short-term liabilities for net wages and salaries"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_255000
#: model:account.account,name:l10n_si.2_gd_acc_255000
#: model:account.account.template,name:l10n_si.gd_acc_255000
msgid "Short-term liabilities for other employment benefits"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_252000
#: model:account.account,name:l10n_si.2_gd_acc_252000
#: model:account.account.template,name:l10n_si.gd_acc_252000
msgid ""
"Short-term liabilities for social security contributions by type of "
"contribution"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_267000
#: model:account.account,name:l10n_si.2_gd_acc_267000
#: model:account.account.template,name:l10n_si.gd_acc_267000
msgid ""
"Short-term liabilities for social security contributions of sole proprietors"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_254000
#: model:account.account,name:l10n_si.2_gd_acc_254000
#: model:account.account.template,name:l10n_si.gd_acc_254000
msgid "Short-term liabilities for taxes on gross wages and salaries"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_242000
#: model:account.account,name:l10n_si.2_gd_acc_242000
#: model:account.account.template,name:l10n_si.gd_acc_242000
msgid "Short-term liabilities from commission and consignment sales"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_282000
#: model:account.account,name:l10n_si.2_gd_acc_282000
#: model:account.account.template,name:l10n_si.gd_acc_282000
msgid "Short-term liabilities related to deductions from wages and salaries"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_277000
#: model:account.account,name:l10n_si.2_gd_acc_277000
#: model:account.account.template,name:l10n_si.gd_acc_277000
msgid "Short-term liabilities related to the distribution of profit or loss"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_241000
#: model:account.account,name:l10n_si.2_gd_acc_241000
#: model:account.account.template,name:l10n_si.gd_acc_241000
msgid "Short-term liabilities to importers"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_18
#: model:account.group,name:l10n_si.2_gd_acc_group_18
#: model:account.group.template,name:l10n_si.gd_acc_group_18
msgid "Short-term loans and short-term receivables for unpaid capital"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_184000
#: model:account.account,name:l10n_si.2_gd_acc_184000
#: model:account.account.template,name:l10n_si.gd_acc_184000
msgid "Short-term loans granted by repurchase of bonds"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_186000
#: model:account.account,name:l10n_si.2_gd_acc_186000
#: model:account.account.template,name:l10n_si.gd_acc_186000
msgid "Short-term loans granted by repurchase of other debt securities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_181000
#: model:account.account,name:l10n_si.2_gd_acc_181000
#: model:account.account.template,name:l10n_si.gd_acc_181000
msgid ""
"Short-term loans granted on the basis of loan agreements to associated "
"organizations and jointly controlled entities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_180000
#: model:account.account,name:l10n_si.2_gd_acc_180000
#: model:account.account.template,name:l10n_si.gd_acc_180000
msgid ""
"Short-term loans granted on the basis of loan agreements to group "
"organizations"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_182000
#: model:account.account,name:l10n_si.2_gd_acc_182000
#: model:account.account.template,name:l10n_si.gd_acc_182000
msgid ""
"Short-term loans granted to others, including short-term finance lease "
"receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_271000
#: model:account.account,name:l10n_si.2_gd_acc_271000
#: model:account.account.template,name:l10n_si.gd_acc_271000
msgid ""
"Short-term loans obtained from associates and jointly controlled entities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_273000
#: model:account.account,name:l10n_si.2_gd_acc_273000
#: model:account.account.template,name:l10n_si.gd_acc_273000
msgid "Short-term loans obtained from banks and organizations abroad"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_272000
#: model:account.account,name:l10n_si.2_gd_acc_272000
#: model:account.account.template,name:l10n_si.gd_acc_272000
msgid "Short-term loans obtained from banks and organizations in the country"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_270000
#: model:account.account,name:l10n_si.2_gd_acc_270000
#: model:account.account.template,name:l10n_si.gd_acc_270000
msgid "Short-term loans obtained from group organizations"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_24
#: model:account.group,name:l10n_si.2_gd_acc_group_24
#: model:account.group.template,name:l10n_si.gd_acc_group_24
msgid "Short-term operating liabilities for a foreign account"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_227000
#: model:account.account,name:l10n_si.2_gd_acc_227000
#: model:account.account.template,name:l10n_si.gd_acc_227000
msgid "Short-term operating liabilities to group companies"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_14
#: model:account.group,name:l10n_si.2_gd_acc_group_14
#: model:account.group.template,name:l10n_si.gd_acc_group_14
msgid "Short-term operating receivables for a foreign account"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_127000
#: model:account.account,name:l10n_si.2_gd_acc_127000
#: model:account.account.template,name:l10n_si.gd_acc_127000
msgid "Short-term operating receivables from group companies"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_111000
#: model:account.account,name:l10n_si.2_gd_acc_111000
#: model:account.account.template,name:l10n_si.gd_acc_111000
msgid "Short-term or callable deposits, except foreign currency deposits"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_162000
#: model:account.account,name:l10n_si.2_gd_acc_162000
#: model:account.account.template,name:l10n_si.gd_acc_162000
msgid ""
"Short-term personal income tax receivables from the activities of sole "
"proprietors, including tax paid abroad"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_160000
#: model:account.account,name:l10n_si.2_gd_acc_160000
#: model:account.account.template,name:l10n_si.gd_acc_160000
msgid "Short-term receivables for deductible vat"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_152000
#: model:account.account,name:l10n_si.2_gd_acc_152000
#: model:account.account.template,name:l10n_si.gd_acc_152000
msgid "Short-term receivables for other profit shares"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_126000
#: model:account.account,name:l10n_si.2_gd_acc_126000
#: model:account.account.template,name:l10n_si.gd_acc_126000
msgid "Short-term receivables for unbilled goods and services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_142000
#: model:account.account,name:l10n_si.2_gd_acc_142000
#: model:account.account.template,name:l10n_si.gd_acc_142000
msgid "Short-term receivables from commission and consignment sales"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_121000
#: model:account.account,name:l10n_si.2_gd_acc_121000
#: model:account.account.template,name:l10n_si.gd_acc_121000
msgid "Short-term receivables from customers abroad"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_120000
#: model:account.account,name:l10n_si.2_gd_acc_120000
#: model:account.account.template,name:l10n_si.gd_acc_120000
msgid "Short-term receivables from customers in the country"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_140000
#: model:account.account,name:l10n_si.2_gd_acc_140000
#: model:account.account.template,name:l10n_si.gd_acc_140000
msgid "Short-term receivables from exporters"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_125000
#: model:account.account,name:l10n_si.2_gd_acc_125000
#: model:account.account.template,name:l10n_si.gd_acc_125000
msgid "Short-term receivables from members"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_15
#: model:account.group,name:l10n_si.2_gd_acc_group_15
#: model:account.group.template,name:l10n_si.gd_acc_group_15
msgid "Short-term receivables related to financial income"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_134000
#: model:account.account,name:l10n_si.2_gd_acc_134000
#: model:account.account.template,name:l10n_si.gd_acc_134000
msgid "Short-term securities given"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_231000
#: model:account.account,name:l10n_si.2_gd_acc_231000
#: model:account.account.template,name:l10n_si.gd_acc_231000
msgid "Short-term securities received"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_257000
#: model:account.account,name:l10n_si.2_gd_acc_257000
#: model:account.account.template,name:l10n_si.gd_acc_257000
msgid ""
"Short-term tax liabilities from other employment benefits that are not "
"charged together with salaries"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_12
#: model:account.group,name:l10n_si.2_gd_acc_group_12
#: model:account.group.template,name:l10n_si.gd_acc_group_12
msgid "Short-term trade receivables"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_187000
#: model:account.account,name:l10n_si.2_gd_acc_187000
#: model:account.account.template,name:l10n_si.gd_acc_187000
msgid "Short-term unpaid called-up capital"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_167000
#: model:account.account,name:l10n_si.2_gd_acc_167000
#: model:account.account.template,name:l10n_si.gd_acc_167000
msgid "Short-term vat receivables paid abroad"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_166000
#: model:account.account,name:l10n_si.2_gd_acc_166000
#: model:account.account.template,name:l10n_si.gd_acc_166000
msgid "Short-term vat receivables refunded to foreigners"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_25
#: model:account.group,name:l10n_si.2_gd_acc_group_25
#: model:account.group.template,name:l10n_si.gd_acc_group_25
msgid "Short-term wage liabilities"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_29
#: model:account.group,name:l10n_si.2_gd_acc_group_29
#: model:account.group.template,name:l10n_si.gd_acc_group_29
msgid "Shorthand passive time relations"
msgstr ""

#. module: l10n_si
#: model:ir.ui.menu,name:l10n_si.account_reports_si_statements_menu
msgid "Slovenia"
msgstr ""

#. module: l10n_si
#: model:account.chart.template,name:l10n_si.gd_chart
msgid "Slovenia - National Chart of Accounts"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_041000
#: model:account.account,name:l10n_si.2_gd_acc_041000
#: model:account.account.template,name:l10n_si.gd_acc_041000
msgid "Small inventory"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_484000
#: model:account.account,name:l10n_si.2_gd_acc_484000
#: model:account.account.template,name:l10n_si.gd_acc_484000
msgid "Social security contributions of sole proprietors"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_966000
#: model:account.account,name:l10n_si.2_gd_acc_966000
#: model:account.account.template,name:l10n_si.gd_acc_966000
msgid "State aid received"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_922000
#: model:account.account,name:l10n_si.2_gd_acc_922000
#: model:account.account.template,name:l10n_si.gd_acc_922000
msgid "Statutory reserves"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_61
#: model:account.group,name:l10n_si.2_gd_acc_group_61
#: model:account.group.template,name:l10n_si.gd_acc_group_61
msgid "Stocks of crops (harvested) from biological assets"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_66
#: model:account.group,name:l10n_si.2_gd_acc_group_66
#: model:account.group.template,name:l10n_si.gd_acc_group_66
msgid "Stocks of goods"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_3
#: model:account.group,name:l10n_si.2_gd_acc_group_3
#: model:account.group.template,name:l10n_si.gd_acc_group_3
msgid "Stocks of raw materials"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_312000
#: model:account.account,name:l10n_si.2_gd_acc_312000
#: model:account.account.template,name:l10n_si.gd_acc_312000
msgid "Stocks of raw materials along the way"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_311000
#: model:account.account,name:l10n_si.2_gd_acc_311000
#: model:account.account.template,name:l10n_si.gd_acc_311000
msgid "Stocks of raw materials and supplies in a foreign warehouse"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_310000
#: model:account.account,name:l10n_si.2_gd_acc_310000
#: model:account.account.template,name:l10n_si.gd_acc_310000
msgid "Stocks of raw materials and supplies in the warehouse"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_32
#: model:account.group,name:l10n_si.2_gd_acc_group_32
#: model:account.group.template,name:l10n_si.gd_acc_group_32
msgid "Stocks of small inventory and packaging"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_320000
#: model:account.account,name:l10n_si.2_gd_acc_320000
#: model:account.account.template,name:l10n_si.gd_acc_320000
msgid "Stocks of small inventory and packaging in the warehouse"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_321000
#: model:account.account,name:l10n_si.2_gd_acc_321000
#: model:account.account.template,name:l10n_si.gd_acc_321000
msgid "Stocks of small inventory and packaging put into use"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_755000
#: model:account.account,name:l10n_si.2_gd_acc_755000
#: model:account.account.template,name:l10n_si.gd_acc_755000
msgid ""
"Subsidies, grants and similar expenditure not related to business "
"performance"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_785000
#: model:account.account,name:l10n_si.2_gd_acc_785000
#: model:account.account.template,name:l10n_si.gd_acc_785000
msgid ""
"Subsidies, grants and similar revenues not related to business performance"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_670000
#: model:account.account,name:l10n_si.2_gd_acc_670000
#: model:account.account.template,name:l10n_si.gd_acc_670000
msgid "Tangible fixed assets held for sale"
msgstr ""

#. module: l10n_si
#: model:account.tax.report,name:l10n_si.tax_report
msgid "Tax Report"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_934000
#: model:account.account,name:l10n_si.2_gd_acc_934000
#: model:account.account.template,name:l10n_si.gd_acc_934000
msgid "Transfer from revaluation surplus"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_491000
#: model:account.account,name:l10n_si.2_gd_acc_491000
#: model:account.account.template,name:l10n_si.gd_acc_491000
msgid "Transfer of costs directly to expenses"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_490000
#: model:account.account,name:l10n_si.2_gd_acc_490000
#: model:account.account.template,name:l10n_si.gd_acc_490000
msgid "Transfer of costs to inventories"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_918000
#: model:account.account,name:l10n_si.2_gd_acc_918000
#: model:account.account.template,name:l10n_si.gd_acc_918000
msgid "Transfers of tangible assets in the course of business"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_909000
#: model:account.account,name:l10n_si.2_gd_acc_909000
#: model:account.account.template,name:l10n_si.gd_acc_909000
msgid "Uncalled capital (deductible item)"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_0_sale_no_deduction
#: model:account.tax,description:l10n_si.2_l10n_si_vat_0_sale_no_deduction
#: model:account.tax.template,description:l10n_si.l10n_si_vat_0_sale_no_deduction
msgid "VAT charged at a rate of 0% without deduction"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_gd_taxr_3
#: model:account.tax,description:l10n_si.2_gd_taxr_3
#: model:account.tax.template,description:l10n_si.gd_taxr_3
msgid "VAT charged at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_5_sale
#: model:account.tax,description:l10n_si.2_l10n_si_vat_5_sale
#: model:account.tax.template,description:l10n_si.l10n_si_vat_5_sale
msgid "VAT charged at a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_gd_taxr_2
#: model:account.tax,description:l10n_si.2_gd_taxr_2
#: model:account.tax.template,description:l10n_si.gd_taxr_2
msgid "VAT charged at a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_recipient_22_sale
#: model:account.tax,description:l10n_si.2_l10n_si_vat_recipient_22_sale
#: model:account.tax.template,description:l10n_si.l10n_si_vat_recipient_22_sale
msgid "VAT charged by the recipient at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_recipient_5_sale
#: model:account.tax,description:l10n_si.2_l10n_si_vat_recipient_5_sale
#: model:account.tax.template,description:l10n_si.l10n_si_vat_recipient_5_sale
msgid "VAT charged by the recipient at a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_recipient_9_sale
#: model:account.tax,description:l10n_si.2_l10n_si_vat_recipient_9_sale
#: model:account.tax.template,description:l10n_si.l10n_si_vat_recipient_9_sale
msgid "VAT charged by the recipient at a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_gd_taxr_1
#: model:account.tax,description:l10n_si.2_gd_taxr_1
#: model:account.tax.template,description:l10n_si.gd_taxr_1
msgid ""
"VAT charged from acquisitions of goods and services from other EU Member "
"States at a rate of 0%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_22_sale_installation_eu
#: model:account.tax,description:l10n_si.2_l10n_si_vat_22_sale_installation_eu
#: model:account.tax.template,description:l10n_si.l10n_si_vat_22_sale_installation_eu
msgid ""
"VAT charged from assembly and installation of goods in other EU Member "
"States at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_5_sale_installation_eu
#: model:account.tax,description:l10n_si.2_l10n_si_vat_5_sale_installation_eu
#: model:account.tax.template,description:l10n_si.l10n_si_vat_5_sale_installation_eu
msgid ""
"VAT charged from assembly and installation of goods in other EU Member "
"States at a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_9_sale_installation_eu
#: model:account.tax,description:l10n_si.2_l10n_si_vat_9_sale_installation_eu
#: model:account.tax.template,description:l10n_si.l10n_si_vat_9_sale_installation_eu
msgid ""
"VAT charged from assembly and installation of goods in other EU Member "
"States at a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_self_22_sale
#: model:account.tax,description:l10n_si.2_l10n_si_vat_self_22_sale
#: model:account.tax.template,description:l10n_si.l10n_si_vat_self_22_sale
msgid ""
"VAT charged on the basis of self-assessment as a recipient of goods and "
"services at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_self_5_sale
#: model:account.tax,description:l10n_si.2_l10n_si_vat_self_5_sale
#: model:account.tax.template,description:l10n_si.l10n_si_vat_self_5_sale
msgid ""
"VAT charged on the basis of self-assessment as a recipient of goods and "
"services at a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_self_9_sale
#: model:account.tax,description:l10n_si.2_l10n_si_vat_self_9_sale
#: model:account.tax.template,description:l10n_si.l10n_si_vat_self_9_sale
msgid ""
"VAT charged on the basis of self-assessment as a recipient of goods and "
"services at a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_self_22_sale_76a
#: model:account.tax,description:l10n_si.2_l10n_si_vat_self_22_sale_76a
#: model:account.tax.template,description:l10n_si.l10n_si_vat_self_22_sale_76a
msgid ""
"VAT charged on the basis of self-assessment as a recipient of goods and "
"services specified in Article 76.a at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_self_9_sale_76a
#: model:account.tax,description:l10n_si.2_l10n_si_vat_self_9_sale_76a
#: model:account.tax.template,description:l10n_si.l10n_si_vat_self_9_sale_76a
msgid ""
"VAT charged on the basis of self-assessment as a recipient of goods and "
"services specified in Article 76.a at a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_self_22_sale_imports
#: model:account.tax,description:l10n_si.2_l10n_si_vat_self_22_sale_imports
#: model:account.tax.template,description:l10n_si.l10n_si_vat_self_22_sale_imports
msgid ""
"VAT charged on the basis of self-assessment of imports at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_self_5_sale_imports
#: model:account.tax,description:l10n_si.2_l10n_si_vat_self_5_sale_imports
#: model:account.tax.template,description:l10n_si.l10n_si_vat_self_5_sale_imports
msgid "VAT charged on the basis of self-assessment of imports at a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_self_9_sale_imports
#: model:account.tax,description:l10n_si.2_l10n_si_vat_self_9_sale_imports
#: model:account.tax.template,description:l10n_si.l10n_si_vat_self_9_sale_imports
msgid ""
"VAT charged on the basis of self-assessment of imports at a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_gd_taxp_3
#: model:account.tax,description:l10n_si.2_gd_taxp_3
#: model:account.tax.template,description:l10n_si.gd_taxp_3
msgid "VAT deduction at a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_5_purchase
#: model:account.tax,description:l10n_si.2_l10n_si_vat_5_purchase
#: model:account.tax.template,description:l10n_si.l10n_si_vat_5_purchase
msgid "VAT deduction at a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_gd_taxp_2
#: model:account.tax,description:l10n_si.2_gd_taxp_2
#: model:account.tax.template,description:l10n_si.gd_taxp_2
msgid "VAT deduction at a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_gd_taxp_1
#: model:account.tax,description:l10n_si.2_gd_taxp_1
#: model:account.tax.template,description:l10n_si.gd_taxp_1
msgid ""
"VAT deduction from purchases of goods and services, acquisition of goods and"
" services received from other EU Member States and from imports at a rate of"
" 0%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_0_purchase_fixed
#: model:account.tax,description:l10n_si.2_l10n_si_vat_0_purchase_fixed
#: model:account.tax.template,description:l10n_si.l10n_si_vat_0_purchase_fixed
msgid ""
"VAT deduction from the purchase of other fixed assets at the rate of 0%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_22_purchase_fixed
#: model:account.tax,description:l10n_si.2_l10n_si_vat_22_purchase_fixed
#: model:account.tax.template,description:l10n_si.l10n_si_vat_22_purchase_fixed
msgid ""
"VAT deduction from the purchase of other fixed assets at the rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_5_purchase_fixed
#: model:account.tax,description:l10n_si.2_l10n_si_vat_5_purchase_fixed
#: model:account.tax.template,description:l10n_si.l10n_si_vat_5_purchase_fixed
msgid ""
"VAT deduction from the purchase of other fixed assets at the rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_9_purchase_fixed
#: model:account.tax,description:l10n_si.2_l10n_si_vat_9_purchase_fixed
#: model:account.tax.template,description:l10n_si.l10n_si_vat_9_purchase_fixed
msgid ""
"VAT deduction from the purchase of other fixed assets at the rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_22_purchase_fixed_eu
#: model:account.tax,description:l10n_si.2_l10n_si_vat_22_purchase_fixed_eu
#: model:account.tax.template,description:l10n_si.l10n_si_vat_22_purchase_fixed_eu
msgid ""
"VAT deduction from the purchase of other fixed assets from other EU Member "
"States at the rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_5_purchase_fixed_eu
#: model:account.tax,description:l10n_si.2_l10n_si_vat_5_purchase_fixed_eu
#: model:account.tax.template,description:l10n_si.l10n_si_vat_5_purchase_fixed_eu
msgid ""
"VAT deduction from the purchase of other fixed assets from other EU Member "
"States at the rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_9_purchase_fixed_eu
#: model:account.tax,description:l10n_si.2_l10n_si_vat_9_purchase_fixed_eu
#: model:account.tax.template,description:l10n_si.l10n_si_vat_9_purchase_fixed_eu
msgid ""
"VAT deduction from the purchase of other fixed assets from other EU Member "
"States at the rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_22_purchase_estate
#: model:account.tax,description:l10n_si.2_l10n_si_vat_22_purchase_estate
#: model:account.tax.template,description:l10n_si.l10n_si_vat_22_purchase_estate
msgid "VAT deduction from the purchase of real estate at the rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_5_purchase_estate
#: model:account.tax,description:l10n_si.2_l10n_si_vat_5_purchase_estate
#: model:account.tax.template,description:l10n_si.l10n_si_vat_5_purchase_estate
msgid "VAT deduction from the purchase of real estate at the rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_9_purchase_estate
#: model:account.tax,description:l10n_si.2_l10n_si_vat_9_purchase_estate
#: model:account.tax.template,description:l10n_si.l10n_si_vat_9_purchase_estate
msgid "VAT deduction from the purchase of real estate at the rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_22_purchase_estate_76a
#: model:account.tax,description:l10n_si.2_l10n_si_vat_22_purchase_estate_76a
#: model:account.tax.template,description:l10n_si.l10n_si_vat_22_purchase_estate_76a
msgid ""
"VAT deduction from the purchase of real estate specified in Article 76.a at "
"a rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_5_purchase_estate_76a
#: model:account.tax,description:l10n_si.2_l10n_si_vat_5_purchase_estate_76a
#: model:account.tax.template,description:l10n_si.l10n_si_vat_5_purchase_estate_76a
msgid ""
"VAT deduction from the purchase of real estate specified in Article 76.a at "
"a rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_9_purchase_estate_76a
#: model:account.tax,description:l10n_si.2_l10n_si_vat_9_purchase_estate_76a
#: model:account.tax.template,description:l10n_si.l10n_si_vat_9_purchase_estate_76a
msgid ""
"VAT deduction from the purchase of real estate specified in Article 76.a at "
"a rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_gd_taxp_st_2
#: model:account.tax,description:l10n_si.2_gd_taxp_st_2
#: model:account.tax.template,description:l10n_si.gd_taxp_st_2
msgid ""
"VAT deduction on acquisitions of goods from other EU Member States at the "
"rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_5_purchase_goods_eu
#: model:account.tax,description:l10n_si.2_l10n_si_vat_5_purchase_goods_eu
#: model:account.tax.template,description:l10n_si.l10n_si_vat_5_purchase_goods_eu
msgid ""
"VAT deduction on acquisitions of goods from other EU Member States at the "
"rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_gd_taxp_st_1
#: model:account.tax,description:l10n_si.2_gd_taxp_st_1
#: model:account.tax.template,description:l10n_si.gd_taxp_st_1
msgid ""
"VAT deduction on acquisitions of goods from other EU Member States at the "
"rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_22_purchase_services_eu
#: model:account.tax,description:l10n_si.2_l10n_si_vat_22_purchase_services_eu
#: model:account.tax.template,description:l10n_si.l10n_si_vat_22_purchase_services_eu
msgid ""
"VAT deduction on acquisitions of services from other EU Member States at the"
" rate of 22%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_5_purchase_services_eu
#: model:account.tax,description:l10n_si.2_l10n_si_vat_5_purchase_services_eu
#: model:account.tax.template,description:l10n_si.l10n_si_vat_5_purchase_services_eu
msgid ""
"VAT deduction on acquisitions of services from other EU Member States at the"
" rate of 5%"
msgstr ""

#. module: l10n_si
#: model:account.tax,description:l10n_si.1_l10n_si_vat_9_purchase_services_eu
#: model:account.tax,description:l10n_si.2_l10n_si_vat_9_purchase_services_eu
#: model:account.tax.template,description:l10n_si.l10n_si_vat_9_purchase_services_eu
msgid ""
"VAT deduction on acquisitions of services from other EU Member States at the"
" rate of 9,5%"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_054000
#: model:account.account,name:l10n_si.2_gd_acc_054000
#: model:account.account.template,name:l10n_si.gd_acc_054000
msgid ""
"Value adjustment of biological assets - basic herds due to depreciation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_053000
#: model:account.account,name:l10n_si.2_gd_acc_053000
#: model:account.account.template,name:l10n_si.gd_acc_053000
msgid ""
"Value adjustment of biological assets - perennial crops due to depreciation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_035000
#: model:account.account,name:l10n_si.2_gd_acc_035000
#: model:account.account.template,name:l10n_si.gd_acc_035000
msgid "Value adjustment of buildings due to depreciation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_036000
#: model:account.account,name:l10n_si.2_gd_acc_036000
#: model:account.account.template,name:l10n_si.gd_acc_036000
msgid "Value adjustment of foreign-owned real estate investments"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_008000
#: model:account.account,name:l10n_si.2_gd_acc_008000
#: model:account.account.template,name:l10n_si.gd_acc_008000
msgid "Value adjustment of intangible assets due to amortization"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_015000
#: model:account.account,name:l10n_si.2_gd_acc_015000
#: model:account.account.template,name:l10n_si.gd_acc_015000
msgid "Value adjustment of investment property due to depreciation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_056000
#: model:account.account,name:l10n_si.2_gd_acc_056000
#: model:account.account.template,name:l10n_si.gd_acc_056000
msgid ""
"Value adjustment of investments in property, plant and equipment of foreign "
"ownership"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_032000
#: model:account.account,name:l10n_si.2_gd_acc_032000
#: model:account.account.template,name:l10n_si.gd_acc_032000
msgid "Value adjustment of land (quarries, landfills) due to depreciation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_055000
#: model:account.account,name:l10n_si.2_gd_acc_055000
#: model:account.account.template,name:l10n_si.gd_acc_055000
msgid ""
"Value adjustment of other property, plant and equipment due to depreciation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_959000
#: model:account.account,name:l10n_si.2_gd_acc_959000
#: model:account.account.template,name:l10n_si.gd_acc_959000
msgid ""
"Value adjustment of reserves arising from the measurement at fair value of "
"deferred tax liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_949000
#: model:account.account,name:l10n_si.2_gd_acc_949000
#: model:account.account.template,name:l10n_si.gd_acc_949000
msgid "Value adjustment of revaluation reserves for deferred tax liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_051000
#: model:account.account,name:l10n_si.2_gd_acc_051000
#: model:account.account.template,name:l10n_si.gd_acc_051000
msgid "Value adjustment of small inventory due to depreciation"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_700000
#: model:account.account,name:l10n_si.1_gd_acc_710000
#: model:account.account,name:l10n_si.2_gd_acc_700000
#: model:account.account,name:l10n_si.2_gd_acc_710000
#: model:account.account.template,name:l10n_si.gd_acc_700000
#: model:account.account.template,name:l10n_si.gd_acc_710000
msgid "Value of business effects sold"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_701000
#: model:account.account,name:l10n_si.2_gd_acc_701000
#: model:account.account.template,name:l10n_si.gd_acc_701000
msgid "Value of capitalized own products and services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_650000
#: model:account.account,name:l10n_si.2_gd_acc_650000
#: model:account.account.template,name:l10n_si.gd_acc_650000
msgid "Value of goods according to suppliers' accounts"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_300000
#: model:account.account,name:l10n_si.2_gd_acc_300000
#: model:account.account.template,name:l10n_si.gd_acc_300000
msgid "Value of raw materials and supplies according to suppliers' accounts"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_653000
#: model:account.account,name:l10n_si.2_gd_acc_653000
#: model:account.account.template,name:l10n_si.gd_acc_653000
msgid "Vat and other taxes on goods"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_303000
#: model:account.account,name:l10n_si.2_gd_acc_303000
#: model:account.account.template,name:l10n_si.gd_acc_303000
msgid "Vat and other taxes on raw materials and supplies"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_664000
#: model:account.account,name:l10n_si.2_gd_acc_664000
#: model:account.account.template,name:l10n_si.gd_acc_664000
msgid "Vat included in stocks of goods"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_295000
#: model:account.account,name:l10n_si.2_gd_acc_295000
#: model:account.account.template,name:l10n_si.gd_acc_295000
msgid "Vat on advances given"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_195000
#: model:account.account,name:l10n_si.2_gd_acc_195000
#: model:account.account.template,name:l10n_si.gd_acc_195000
msgid "Vat on advances received"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_925000
#: model:account.account,name:l10n_si.2_gd_acc_925000
#: model:account.account.template,name:l10n_si.gd_acc_925000
msgid "Voluntary cooperative funds"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_924000
#: model:account.account,name:l10n_si.2_gd_acc_924000
#: model:account.account.template,name:l10n_si.gd_acc_924000
msgid "Voluntary cooperative reserves"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_265000
#: model:account.account,name:l10n_si.2_gd_acc_265000
#: model:account.account.template,name:l10n_si.gd_acc_265000
msgid "Withholding tax liabilities"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_600000
#: model:account.account,name:l10n_si.2_gd_acc_600000
#: model:account.account.template,name:l10n_si.gd_acc_600000
msgid "Work in progress"
msgstr ""

#. module: l10n_si
#: model:account.group,name:l10n_si.1_gd_acc_group_60
#: model:account.group,name:l10n_si.2_gd_acc_group_60
#: model:account.group.template,name:l10n_si.gd_acc_group_60
msgid "Work in progress and services"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_048000
#: model:account.account,name:l10n_si.2_gd_acc_048000
#: model:account.account.template,name:l10n_si.gd_acc_048000
msgid ""
"Works of art and other objects of cultural and / or historical value that "
"are not depreciated"
msgstr ""

#. module: l10n_si
#: model:account.account,name:l10n_si.1_gd_acc_404000
#: model:account.account,name:l10n_si.2_gd_acc_404000
#: model:account.account.template,name:l10n_si.gd_acc_404000
msgid "Write-off of small inventory and packaging"
msgstr ""
