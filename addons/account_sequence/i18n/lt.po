# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_sequence
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <gail<PERSON>@vialaurea.lt>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:49+0000\n"
"PO-Revision-Date: 2022-12-16 09:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: account_sequence
#: model:ir.model.constraint,message:account_sequence.constraint_account_move_unique_name
msgid "Another entry with the same name already exists."
msgstr "Jau yra kitas įrašas tuo pačiu pavadinimu."

#. module: account_sequence
#: model:ir.model,name:account_sequence.model_sequence_mixin
msgid "Automatic sequence"
msgstr "Automatinė seka"

#. module: account_sequence
#: model:ir.model,name:account_sequence.model_account_move
msgid "Journal Entry"
msgstr "Žurnalo įrašas"
