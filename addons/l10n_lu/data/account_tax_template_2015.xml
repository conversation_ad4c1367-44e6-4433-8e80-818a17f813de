<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="lu_2011_tax_AB-EC-0" model="account.tax.template">
        <field name="sequence">171</field>
        <field name="description">0%</field>
        <field name="name">EX-EC-P-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax'       }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax'       }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-EC-14" model="account.tax.template">
        <field name="sequence">105</field>
        <field name="description">14%</field>
        <field name="name">14-EC-P-G</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_14_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-EC-17" model="account.tax.template">
        <field name="sequence">111</field>
        <field name="description">17%</field>
        <field name="name">17-EC-P-G</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_17_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-EC-16" model="account.tax.template">
        <field name="sequence">112</field>
        <field name="description">16%</field>
        <field name="name">16-EC-P-G</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_16_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-EC-13" model="account.tax.template">
        <field name="sequence">113</field>
        <field name="description">13%</field>
        <field name="name">13-EC-P-G</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_13_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-EC-7" model="account.tax.template">
        <field name="sequence">114</field>
        <field name="description">7%</field>
        <field name="name">7-EC-P-G</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_7_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AB-EC-3" model="account.tax.template">
        <field name="sequence">114</field>
        <field name="description">3%</field>
        <field name="name">3-EC-P-G</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_3_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-EC-8" model="account.tax.template">
        <field name="sequence">120</field>
        <field name="description">8%</field>
        <field name="name">8-EC-P-G</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_8_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-ECP-0" model="account.tax.template">
        <field name="sequence">123</field>
        <field name="description">0%</field>
        <field name="name">EX-EC(P)-P-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax'       }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax'       }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-ECP-14" model="account.tax.template">
        <field name="sequence">127</field>
        <field name="description">14%</field>
        <field name="name">14-EC(P)-P-G</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_14_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-ECP-17" model="account.tax.template">
        <field name="sequence">133</field>
        <field name="description">17%</field>
        <field name="name">17-EC(P)-P-G</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_17_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-ECP-16" model="account.tax.template">
        <field name="sequence">134</field>
        <field name="description">16%</field>
        <field name="name">16-EC(P)-P-G</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_16_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-ECP-13" model="account.tax.template">
        <field name="sequence">135</field>
        <field name="description">13%</field>
        <field name="name">13-EC(P)-P-G</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_13_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-ECP-7" model="account.tax.template">
        <field name="sequence">136</field>
        <field name="description">7%</field>
        <field name="name">7-EC(P)-P-G</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_7_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-ECP-3" model="account.tax.template">
        <field name="sequence">137</field>
        <field name="description">3%</field>
        <field name="name">3-EC(P)-P-G</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_3_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-ECP-8" model="account.tax.template">
        <field name="sequence">142</field>
        <field name="description">8%</field>
        <field name="name">8-EC(P)-P-G</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_8_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AB-IC-0" model="account.tax.template">
        <field name="sequence">145</field>
        <field name="description">0%</field>
        <field name="name">EX-IC-P-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax'}),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax'}),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-IC-14" model="account.tax.template">
        <field name="sequence">149</field>
        <field name="description">14%</field>
        <field name="name">14-IC-P-G</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_14_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_14_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-IC-17" model="account.tax.template">
        <field name="sequence">155</field>
        <field name="description">17%</field>
        <field name="name">17-IC-P-G</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_17_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_17_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-IC-16" model="account.tax.template">
        <field name="sequence">155</field>
        <field name="description">16%</field>
        <field name="name">16-IC-P-G</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_16_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_16_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-IC-13" model="account.tax.template">
        <field name="sequence">156</field>
        <field name="description">13%</field>
        <field name="name">13-IC-P-G</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_13_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_13_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-IC-7" model="account.tax.template">
        <field name="sequence">157</field>
        <field name="description">7%</field>
        <field name="name">7-IC-P-G</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_7_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_7_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AB-IC-3" model="account.tax.template">
        <field name="sequence">158</field>
        <field name="description">3%</field>
        <field name="name">3-IC-P-G</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_3_tag')]
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_3_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')]
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_3_tag')]
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_3_tag')]
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-IC-8" model="account.tax.template">
        <field name="sequence">164</field>
        <field name="description">8%</field>
        <field name="name">8-IC-P-G</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AB-PA-0" model="account.tax.template">
        <field name="sequence">167</field>
        <field name="description">0%</field>
        <field name="name">0-P-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-PA-14" model="account.tax.template">
        <field name="sequence">169</field>
        <field name="description">14%</field>
        <field name="name">14-P-G</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-PA-17" model="account.tax.template">
        <field name="sequence">101</field>
        <field name="description">17%</field>
        <field name="name">17-P-G</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-PA-16" model="account.tax.template">
        <field name="sequence">102</field>
        <field name="description">16%</field>
        <field name="name">16-P-G</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-PA-13" model="account.tax.template">
        <field name="sequence">103</field>
        <field name="description">13%</field>
        <field name="name">13-P-G</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-PA-8" model="account.tax.template">
        <field name="sequence">174</field>
        <field name="description">8%</field>
        <field name="name">8-P-G</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AB-PA-7" model="account.tax.template">
        <field name="sequence">104</field>
        <field name="description">7%</field>
        <field name="name">7-P-G</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AB-PA-3" model="account.tax.template">
        <field name="sequence">172</field>
        <field name="description">3%</field>
        <field name="name">3-P-G</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AP-EC-0" model="account.tax.template">
        <field name="sequence">175</field>
        <field name="description">0%</field>
        <field name="name">EX-EC-P-S</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-EC-14" model="account.tax.template">
        <field name="sequence">179</field>
        <field name="description">14%</field>
        <field name="name">14-EC-P-S</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_14_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-EC-17" model="account.tax.template">
        <field name="sequence">185</field>
        <field name="description">17%</field>
        <field name="name">17-EC-P-S</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_17_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-EC-16" model="account.tax.template">
        <field name="sequence">185</field>
        <field name="description">16%</field>
        <field name="name">16-EC-P-S</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_16_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-EC-13" model="account.tax.template">
        <field name="sequence">186</field>
        <field name="description">13%</field>
        <field name="name">13-EC-P-S</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_13_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-EC-7" model="account.tax.template">
        <field name="sequence">187</field>
        <field name="description">7%</field>
        <field name="name">7-EC-P-S</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_7_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AP-EC-3" model="account.tax.template">
        <field name="sequence">188</field>
        <field name="description">3%</field>
        <field name="name">3-EC-P-S</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_3_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-EC-8" model="account.tax.template">
        <field name="sequence">194</field>
        <field name="description">8%</field>
        <field name="name">8-EC-P-S</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_8_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AP-IC-0" model="account.tax.template">
        <field name="sequence">197</field>
        <field name="description">0%</field>
        <field name="name">EX-IC-P-S</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_b_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_b_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-IC-14" model="account.tax.template">
        <field name="sequence">201</field>
        <field name="description">14%</field>
        <field name="name">14-IC-P-S</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-IC-17" model="account.tax.template">
        <field name="sequence">207</field>
        <field name="description">17%</field>
        <field name="name">17-IC-P-S</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-IC-16" model="account.tax.template">
        <field name="sequence">208</field>
        <field name="description">16%</field>
        <field name="name">16-IC-P-S</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-IC-13" model="account.tax.template">
        <field name="sequence">209</field>
        <field name="description">13%</field>
        <field name="name">13-IC-P-S</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-IC-7" model="account.tax.template">
        <field name="sequence">210</field>
        <field name="description">7%</field>
        <field name="name">7-IC-P-S</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AP-IC-3" model="account.tax.template">
        <field name="sequence">211</field>
        <field name="description">3%</field>
        <field name="name">3-IC-P-S</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-IC-8" model="account.tax.template">
        <field name="sequence">216</field>
        <field name="description">8%</field>
        <field name="name">8-IC-P-S</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AP-PA-0" model="account.tax.template">
        <field name="sequence">219</field>
        <field name="description">0%</field>
        <field name="name">0-P-S</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-PA-14" model="account.tax.template">
        <field name="sequence">221</field>
        <field name="description">14%</field>
        <field name="name">14-P-S</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-PA-17" model="account.tax.template">
        <field name="sequence">223</field>
        <field name="description">17%</field>
        <field name="name">17-P-S</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-PA-16" model="account.tax.template">
        <field name="sequence">223</field>
        <field name="description">16%</field>
        <field name="name">16-P-S</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-PA-13" model="account.tax.template">
        <field name="sequence">223</field>
        <field name="description">13%</field>
        <field name="name">13-P-S</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-PA-7" model="account.tax.template">
        <field name="sequence">223</field>
        <field name="description">7%</field>
        <field name="name">7-P-S</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_AP-PA-3" model="account.tax.template">
        <field name="sequence">224</field>
        <field name="description">3%</field>
        <field name="name">3-P-S</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_AP-PA-8" model="account.tax.template">
        <field name="sequence">226</field>
        <field name="description">8%</field>
        <field name="name">8-P-S</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_FB-EC-0" model="account.tax.template">
        <field name="sequence">227</field>
        <field name="description">0%</field>
        <field name="name">EX-EC-E-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-EC-14" model="account.tax.template">
        <field name="sequence">231</field>
        <field name="description">14%</field>
        <field name="name">14-EC-E-G</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_14_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-EC-17" model="account.tax.template">
        <field name="sequence">237</field>
        <field name="description">17%</field>
        <field name="name">17-EC-E-G</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_17_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-EC-16" model="account.tax.template">
        <field name="sequence">238</field>
        <field name="description">16%</field>
        <field name="name">16-EC-E-G</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_16_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-EC-13" model="account.tax.template">
        <field name="sequence">239</field>
        <field name="description">13%</field>
        <field name="name">13-EC-E-G</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_13_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-EC-7" model="account.tax.template">
        <field name="sequence">240</field>
        <field name="description">7%</field>
        <field name="name">7-EC-E-G</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_7_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FB-EC-3" model="account.tax.template">
        <field name="sequence">241</field>
        <field name="description">3%</field>
        <field name="name">3-EC-E-G</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_3_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-EC-8" model="account.tax.template">
        <field name="sequence">246</field>
        <field name="description">8%</field>
        <field name="name">8-EC-E-G</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_8_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-ECP-0" model="account.tax.template">
        <field name="sequence">249</field>
        <field name="description">0%</field>
        <field name="name">EX-EC(P)-E-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-ECP-14" model="account.tax.template">
        <field name="sequence">253</field>
        <field name="description">14%</field>
        <field name="name">14-EC(P)-E-G</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_14_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-ECP-17" model="account.tax.template">
        <field name="sequence">259</field>
        <field name="description">17%</field>
        <field name="name">17-EC(P)-E-G</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_17_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-ECP-16" model="account.tax.template">
        <field name="sequence">260</field>
        <field name="description">16%</field>
        <field name="name">16-EC(P)-E-G</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_16_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-ECP-13" model="account.tax.template">
        <field name="sequence">261</field>
        <field name="description">13%</field>
        <field name="name">13-EC(P)-E-G</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_13_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-ECP-7" model="account.tax.template">
        <field name="sequence">262</field>
        <field name="description">7%</field>
        <field name="name">7-EC(P)-E-G</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_7_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-ECP-3" model="account.tax.template">
        <field name="sequence">263</field>
        <field name="description">3%</field>
        <field name="name">3-EC(P)-E-G</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_3_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-ECP-8" model="account.tax.template">
        <field name="sequence">268</field>
        <field name="description">8%</field>
        <field name="name">8-EC(P)-E-G</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_8_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FB-IC-0" model="account.tax.template">
        <field name="sequence">271</field>
        <field name="description">0%</field>
        <field name="name">EX-IC-E-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-IC-14" model="account.tax.template">
        <field name="sequence">275</field>
        <field name="description">14%</field>
        <field name="name">14-IC-E-G</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-IC-17" model="account.tax.template">
        <field name="sequence">281</field>
        <field name="description">17%</field>
        <field name="name">17-IC-E-G</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-IC-16" model="account.tax.template">
        <field name="sequence">282</field>
        <field name="description">16%</field>
        <field name="name">16-IC-E-G</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-IC-13" model="account.tax.template">
        <field name="sequence">283</field>
        <field name="description">13%</field>
        <field name="name">13-IC-E-G</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-IC-7" model="account.tax.template">
        <field name="sequence">284</field>
        <field name="description">7%</field>
        <field name="name">7-IC-E-G</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FB-IC-3" model="account.tax.template">
        <field name="sequence">285</field>
        <field name="description">3%</field>
        <field name="name">3-IC-E-G</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-IC-8" model="account.tax.template">
        <field name="sequence">290</field>
        <field name="description">8%</field>
        <field name="name">8-IC-E-G</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FB-PA-0" model="account.tax.template">
        <field name="sequence">293</field>
        <field name="description">0%</field>
        <field name="name">0-E-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-PA-14" model="account.tax.template">
        <field name="sequence">295</field>
        <field name="description">14%</field>
        <field name="name">14-E-G</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-PA-17" model="account.tax.template">
        <field name="sequence">297</field>
        <field name="description">17%</field>
        <field name="name">17-E-G</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-PA-16" model="account.tax.template">
        <field name="sequence">298</field>
        <field name="description">16%</field>
        <field name="name">16-E-G</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-PA-13" model="account.tax.template">
        <field name="sequence">299</field>
        <field name="description">13%</field>
        <field name="name">13-E-G</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-PA-7" model="account.tax.template">
        <field name="sequence">300</field>
        <field name="description">7%</field>
        <field name="name">7-E-G</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FB-PA-3" model="account.tax.template">
        <field name="sequence">301</field>
        <field name="description">3%</field>
        <field name="name">3-E-G</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FB-PA-8" model="account.tax.template">
        <field name="sequence">302</field>
        <field name="description">8%</field>
        <field name="name">8-E-G</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FP-EC-0" model="account.tax.template">
        <field name="sequence">303</field>
        <field name="description">0%</field>
        <field name="name">0-EC-E-S</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-EC-14" model="account.tax.template">
        <field name="sequence">305</field>
        <field name="description">14%</field>
        <field name="name">14-EC-E-S</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_14_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-EC-17" model="account.tax.template">
        <field name="sequence">311</field>
        <field name="description">17%</field>
        <field name="name">17-EC-E-S</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_17_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-EC-16" model="account.tax.template">
        <field name="sequence">312</field>
        <field name="description">16%</field>
        <field name="name">16-EC-E-S</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_16_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-EC-13" model="account.tax.template">
        <field name="sequence">313</field>
        <field name="description">13%</field>
        <field name="name">13-EC-E-S</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_13_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-EC-7" model="account.tax.template">
        <field name="sequence">314</field>
        <field name="description">7%</field>
        <field name="name">7-EC-E-S</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_7_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FP-EC-3" model="account.tax.template">
        <field name="sequence">315</field>
        <field name="description">3%</field>
        <field name="name">3-EC-E-S</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_3_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-EC-8" model="account.tax.template">
        <field name="sequence">320</field>
        <field name="description">8%</field>
        <field name="name">8-EC-E-S</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_8_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FP-IC-0" model="account.tax.template">
        <field name="sequence">323</field>
        <field name="description">0%</field>
        <field name="name">EX-IC-E-S</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_b_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_b_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-IC-14" model="account.tax.template">
        <field name="sequence">327</field>
        <field name="description">14%</field>
        <field name="name">14-IC-E-S</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-IC-17" model="account.tax.template">
        <field name="sequence">333</field>
        <field name="description">17%</field>
        <field name="name">17-IC-E-S</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-IC-16" model="account.tax.template">
        <field name="sequence">334</field>
        <field name="description">16%</field>
        <field name="name">16-IC-E-S</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-IC-13" model="account.tax.template">
        <field name="sequence">335</field>
        <field name="description">13%</field>
        <field name="name">13-IC-E-S</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-IC-7" model="account.tax.template">
        <field name="sequence">336</field>
        <field name="description">7%</field>
        <field name="name">7-IC-E-S</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FP-IC-3" model="account.tax.template">
        <field name="sequence">337</field>
        <field name="description">3%</field>
        <field name="name">3-IC-E-S</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-IC-8" model="account.tax.template">
        <field name="sequence">342</field>
        <field name="description">8%</field>
        <field name="name">8-IC-E-S</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FP-PA-0" model="account.tax.template">
        <field name="sequence">345</field>
        <field name="description">0%</field>
        <field name="name">0-E-S</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-PA-14" model="account.tax.template">
        <field name="sequence">347</field>
        <field name="description">14%</field>
        <field name="name">14-E-S</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-PA-17" model="account.tax.template">
        <field name="sequence">349</field>
        <field name="description">17%</field>
        <field name="name">17-E-S</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-PA-16" model="account.tax.template">
        <field name="sequence">350</field>
        <field name="description">16%</field>
        <field name="name">16-E-S</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-PA-13" model="account.tax.template">
        <field name="sequence">351</field>
        <field name="description">13%</field>
        <field name="name">13-E-S</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-PA-7" model="account.tax.template">
        <field name="sequence">352</field>
        <field name="description">7%</field>
        <field name="name">7-E-S</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_FP-PA-3" model="account.tax.template">
        <field name="sequence">353</field>
        <field name="description">3%</field>
        <field name="name">3-E-S</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_FP-PA-8" model="account.tax.template">
        <field name="sequence">354</field>
        <field name="description">8%</field>
        <field name="name">8-E-S</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IB-EC-0" model="account.tax.template">
        <field name="sequence">355</field>
        <field name="description">0%</field>
        <field name="name">0-EC-IG</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-EC-14" model="account.tax.template">
        <field name="sequence">357</field>
        <field name="description">14%</field>
        <field name="name">14-EC-IG</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_14_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-EC-17" model="account.tax.template">
        <field name="sequence">363</field>
        <field name="description">17%</field>
        <field name="name">17-EC-IG</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_17_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-EC-16" model="account.tax.template">
        <field name="sequence">364</field>
        <field name="description">16%</field>
        <field name="name">16-EC-IG</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_16_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-EC-13" model="account.tax.template">
        <field name="sequence">365</field>
        <field name="description">13%</field>
        <field name="name">13-EC-IG</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_13_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-EC-7" model="account.tax.template">
        <field name="sequence">366</field>
        <field name="description">7%</field>
        <field name="name">7-EC-IG</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_7_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IB-EC-3" model="account.tax.template">
        <field name="sequence">367</field>
        <field name="description">3%</field>
        <field name="name">3-EC-IG</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_3_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-EC-8" model="account.tax.template">
        <field name="sequence">372</field>
        <field name="description">8%</field>
        <field name="name">8-EC-IG</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_1_tax_8_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-ECP-0" model="account.tax.template">
        <field name="sequence">375</field>
        <field name="description">5%</field>
        <field name="name">0-EC(P)-IG</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-ECP-14" model="account.tax.template">
        <field name="sequence">379</field>
        <field name="description">14%</field>
        <field name="name">14-EC(P)-IG</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_14_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-ECP-17" model="account.tax.template">
        <field name="sequence">385</field>
        <field name="description">17%</field>
        <field name="name">17-EC(P)-IG</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_17_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-ECP-16" model="account.tax.template">
        <field name="sequence">386</field>
        <field name="description">16%</field>
        <field name="name">16-EC(P)-IG</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_16_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-ECP-13" model="account.tax.template">
        <field name="sequence">387</field>
        <field name="description">13%</field>
        <field name="name">13-EC(P)-IG</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_13_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-ECP-7" model="account.tax.template">
        <field name="sequence">388</field>
        <field name="description">7%</field>
        <field name="name">7-EC(P)-IG</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_7_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-ECP-3" model="account.tax.template">
        <field name="sequence">389</field>
        <field name="description">3%</field>
        <field name="name">3-EC(P)-IG</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_3_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-ECP-8" model="account.tax.template">
        <field name="sequence">394</field>
        <field name="description">8%</field>
        <field name="name">8-EC(P)-IG</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_3_due_paid_respect_importation_goods_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2d_2_tax_8_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IB-IC-0" model="account.tax.template">
        <field name="sequence">397</field>
        <field name="description">0%</field>
        <field name="name">0-IC-IG</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-IC-14" model="account.tax.template">
        <field name="sequence">401</field>
        <field name="description">14%</field>
        <field name="name">14-IC-IG</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-IC-17" model="account.tax.template">
        <field name="sequence">407</field>
        <field name="description">17%</field>
        <field name="name">17-IC-IG</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-IC-16" model="account.tax.template">
        <field name="sequence">407</field>
        <field name="description">16%</field>
        <field name="name">16-IC-IG</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-IC-13" model="account.tax.template">
        <field name="sequence">408</field>
        <field name="description">13%</field>
        <field name="name">13-IC-IG</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-IC-7" model="account.tax.template">
        <field name="sequence">409</field>
        <field name="description">7%</field>
        <field name="name">7-IC-IG</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IB-IC-3" model="account.tax.template">
        <field name="sequence">410</field>
        <field name="description">3%</field>
        <field name="name">3-IC-IG</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-IC-8" model="account.tax.template">
        <field name="sequence">416</field>
        <field name="description">8%</field>
        <field name="name">8-IC-IG</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2b_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2b_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_2_due_respect_intra_comm_goods_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IB-PA-0" model="account.tax.template">
        <field name="sequence">419</field>
        <field name="description">0%</field>
        <field name="name">0-IG</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-PA-14" model="account.tax.template">
        <field name="sequence">421</field>
        <field name="description">14%</field>
        <field name="name">14-IG</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-PA-17" model="account.tax.template">
        <field name="sequence">423</field>
        <field name="description">17%</field>
        <field name="name">17-IG</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-PA-16" model="account.tax.template">
        <field name="sequence">424</field>
        <field name="description">16%</field>
        <field name="name">16-IG</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-PA-13" model="account.tax.template">
        <field name="sequence">425</field>
        <field name="description">13%</field>
        <field name="name">13-IG</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-PA-7" model="account.tax.template">
        <field name="sequence">426</field>
        <field name="description">7%</field>
        <field name="name">7-IG</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IB-PA-3" model="account.tax.template">
        <field name="sequence">427</field>
        <field name="description">3%</field>
        <field name="name">3-IG</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IB-PA-8" model="account.tax.template">
        <field name="sequence">428</field>
        <field name="description">8%</field>
        <field name="name">8-IG</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IP-EC-0" model="account.tax.template">
        <field name="sequence">429</field>
        <field name="description">0%</field>
        <field name="name">0-EC-IS</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-EC-14" model="account.tax.template">
        <field name="sequence">431</field>
        <field name="description">14%</field>
        <field name="name">14-EC-IS</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_14_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-EC-17" model="account.tax.template">
        <field name="sequence">437</field>
        <field name="description">17%</field>
        <field name="name">17-EC-IS</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_17_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-EC-16" model="account.tax.template">
        <field name="sequence">438</field>
        <field name="description">16%</field>
        <field name="name">16-EC-IS</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_16_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-EC-13" model="account.tax.template">
        <field name="sequence">439</field>
        <field name="description">13%</field>
        <field name="name">13-EC-IS</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_13_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-EC-7" model="account.tax.template">
        <field name="sequence">440</field>
        <field name="description">7%</field>
        <field name="name">7-EC-IS</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_7_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IP-EC-3" model="account.tax.template">
        <field name="sequence">441</field>
        <field name="description">3%</field>
        <field name="name">3-EC-IS</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_3_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-EC-8" model="account.tax.template">
        <field name="sequence">446</field>
        <field name="description">8%</field>
        <field name="name">8-EC-IS</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_2_tax_8_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IP-IC-0" model="account.tax.template">
        <field name="sequence">449</field>
        <field name="description">0%</field>
        <field name="name">0-IC-IS</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_b_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_b_exempt_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-IC-14" model="account.tax.template">
        <field name="sequence">453</field>
        <field name="description">14%</field>
        <field name="name">14-IC-IS</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_14_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-IC-17" model="account.tax.template">
        <field name="sequence">459</field>
        <field name="description">17%</field>
        <field name="name">17-IC-IS</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_17_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-IC-16" model="account.tax.template">
        <field name="sequence">459</field>
        <field name="description">16%</field>
        <field name="name">16-IC-IS</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_16_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-IC-13" model="account.tax.template">
        <field name="sequence">460</field>
        <field name="description">13%</field>
        <field name="name">13-IC-IS</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_13_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-IC-7" model="account.tax.template">
        <field name="sequence">461</field>
        <field name="description">7%</field>
        <field name="name">7-IC-IS</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_7_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IP-IC-3" model="account.tax.template">
        <field name="sequence">462</field>
        <field name="description">3%</field>
        <field name="name">3-IC-IS</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_3_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-IC-8" model="account.tax.template">
        <field name="sequence">468</field>
        <field name="description">8%</field>
        <field name="name">8-IC-IS</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_base_8_tag')],
      }),
      (0,0, {
          'factor_percent': -100,
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2e_1_a_tax_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_5_due_under_reverse_charge_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IP-PA-0" model="account.tax.template">
        <field name="sequence">471</field>
        <field name="description">0%</field>
        <field name="name">0-IS</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-PA-14" model="account.tax.template">
        <field name="sequence">473</field>
        <field name="description">14%</field>
        <field name="name">14-IS</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-PA-17" model="account.tax.template">
        <field name="sequence">475</field>
        <field name="description">17%</field>
        <field name="name">17-IS</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-PA-16" model="account.tax.template">
        <field name="sequence">476</field>
        <field name="description">16%</field>
        <field name="name">16-IS</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-PA-13" model="account.tax.template">
        <field name="sequence">477</field>
        <field name="description">13%</field>
        <field name="name">13-IS</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-PA-7" model="account.tax.template">
        <field name="sequence">478</field>
        <field name="description">7%</field>
        <field name="name">7-IS</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_IP-PA-3" model="account.tax.template">
        <field name="sequence">479</field>
        <field name="description">3%</field>
        <field name="name">3-IS</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_IP-PA-8" model="account.tax.template">
        <field name="sequence">480</field>
        <field name="description">8%</field>
        <field name="name">8-IS</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'plus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_421611'),
          'minus_report_expression_ids': [ref('account_tax_report_line_3a_1_invoiced_by_other_taxable_person_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_V-ART-43_60b" model="account.tax.template">
        <field name="sequence">489</field>
        <field name="description">0%</field>
        <field name="name">0-E-Art.43&amp;60b</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_3_other_exemptions_art_43_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_3_other_exemptions_art_43_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_V-ART-44_56q" model="account.tax.template">
        <field name="sequence">480</field>
        <field name="description">0%</field>
        <field name="name">0-E-Art.44&amp;56q</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_4_other_exemptions_art_44_et_56quater_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_4_other_exemptions_art_44_et_56quater_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_VB-EC-0" model="account.tax.template">
        <field name="sequence">481</field>
        <field name="description">0%</field>
        <field name="name">0-EC-S-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_2_export_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_2_export_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_VB-EC-Tab" model="account.tax.template">
        <field name="sequence">482</field>
        <field name="description">0%</field>
        <field name="name">0-EC-ST-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_5_manufactured_tobacco_vat_collected_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_5_manufactured_tobacco_vat_collected_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_VB-IC-0" model="account.tax.template">
        <field name="sequence">483</field>
        <field name="description">0%</field>
        <field name="name">0-IC-S-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_1_intra_community_goods_pi_vat_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_1_intra_community_goods_pi_vat_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_VB-IC-Tab" model="account.tax.template">
        <field name="sequence">484</field>
        <field name="description">0%</field>
        <field name="name">0-IC-ST-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_5_manufactured_tobacco_vat_collected_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_5_manufactured_tobacco_vat_collected_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_VB-PA-0" model="account.tax.template">
        <field name="sequence">485</field>
        <field name="description">0%</field>
        <field name="name">0-S-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_0_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_0_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_VB-PA-14" model="account.tax.template">
        <field name="sequence">487</field>
        <field name="description">14%</field>
        <field name="name">14-S-G</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_14_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_VB-PA-17" model="account.tax.template">
        <field name="sequence">502</field>
        <field name="description">17%</field>
        <field name="name">17-S-G</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_17_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_VB-PA-16" model="account.tax.template">
        <field name="sequence">503</field>
        <field name="description">16%</field>
        <field name="name">16-S-G</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_16_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_VB-PA-13" model="account.tax.template">
        <field name="sequence">504</field>
        <field name="description">13%</field>
        <field name="name">13-S-G</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_13_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_VB-PA-7" model="account.tax.template">
        <field name="sequence">505</field>
        <field name="description">7%</field>
        <field name="name">7-S-G</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_7_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_VB-PA-3" model="account.tax.template">
        <field name="sequence">490</field>
        <field name="description">3%</field>
        <field name="name">3-S-G</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_3_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_VB-PA-8" model="account.tax.template">
        <field name="sequence">492</field>
        <field name="description">8%</field>
        <field name="name">8-S-G</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_8_tag')],
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2011_tax_VB-PA-Tab" model="account.tax.template">
        <field name="sequence">493</field>
        <field name="description">0%</field>
        <field name="name">0-ST-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_5_manufactured_tobacco_vat_collected_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_5_manufactured_tobacco_vat_collected_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="active" eval="False"/>
    </record>
    <record id="lu_2015_tax_VB-TR-0" model="account.tax.template">
        <field name="sequence">494</field>
        <field name="description">0%</field>
        <field name="name">0-ICT-S-G</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_6_a_subsequent_to_intra_community_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_6_a_subsequent_to_intra_community_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_VP-EC-0" model="account.tax.template">
        <field name="sequence">495</field>
        <field name="description">0%</field>
        <field name="name">0-EC-S-S</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_6_d_supplies_other_referred_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_6_d_supplies_other_referred_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_VP-IC-0" model="account.tax.template">
        <field name="sequence">496</field>
        <field name="description">0%</field>
        <field name="name">0-IC-S-S</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_6_b1_non_exempt_customer_vat_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_6_b1_non_exempt_customer_vat_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_VP-IC-EX" model="account.tax.template">
        <field name="sequence">497</field>
        <field name="description">0%</field>
        <field name="name"> EX-IC-S-S</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1b_6_b2_exempt_ms_customer_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1b_6_b2_exempt_ms_customer_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_VP-PA-0" model="account.tax.template">
        <field name="sequence">498</field>
        <field name="description">0%</field>
        <field name="name">0-S-S</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_0_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_0_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_VP-PA-14" model="account.tax.template">
        <field name="sequence">500</field>
        <field name="description">14%</field>
        <field name="name">14-S-S</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_14"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_14_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_14_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_14_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_VP-PA-17" model="account.tax.template">
        <field name="sequence">479</field>
        <field name="description">17%</field>
        <field name="name">17-S-S</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_17_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_VP-PA-16" model="account.tax.template">
        <field name="sequence">480</field>
        <field name="description">16%</field>
        <field name="name">16-S-S</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_16_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_VP-PA-13" model="account.tax.template">
        <field name="sequence">481</field>
        <field name="description">13%</field>
        <field name="name">13-S-S</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_13_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_13_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_13_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_VP-PA-7" model="account.tax.template">
        <field name="sequence">482</field>
        <field name="description">7%</field>
        <field name="name">7-S-S</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_7_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_7_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_7_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2011_tax_VP-PA-3" model="account.tax.template">
        <field name="sequence">503</field>
        <field name="description">3%</field>
        <field name="name">3-S-S</field>
        <field name="amount">3</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_3_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_3_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_3_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_VP-PA-8" model="account.tax.template">
        <field name="sequence">505</field>
        <field name="description">8%</field>
        <field name="name">8-S-S</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_base_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_8_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_base_8_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_8_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_SANS" model="account.tax.template">
        <field name="sequence">506</field>
        <field name="description">0%</field>
        <field name="name">0-P-Tax-Free</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_SANS_sale" model="account.tax.template">
        <field name="sequence">507</field>
        <field name="description">0%</field>
        <field name="name">0-S-Tax-Free</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
      }),
      (0,0, {
          'repartition_type': 'tax',
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_ATN_sale" model="account.tax.template">
        <field name="sequence">510</field>
        <field name="description">17%</field>
        <field name="name">17-ATN</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1a_non_bus_gs_tag'), ref('account_tax_report_line_2a_base_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_17_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1a_non_bus_gs_tag'), ref('account_tax_report_line_2a_base_17_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_17_tag')],
      }),
  ]"/>
    </record>
    <record id="lu_2015_tax_ATN_sale_16" model="account.tax.template">
        <field name="sequence">511</field>
        <field name="description">16%</field>
        <field name="name">16-ATN</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="active" eval="False"/>
        <field name="chart_template_id" ref="lu_2011_chart_1"/>
        <field name="tax_group_id" ref="tax_group_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'plus_report_expression_ids': [ref('account_tax_report_line_1a_non_bus_gs_tag'), ref('account_tax_report_line_2a_base_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'plus_report_expression_ids': [ref('account_tax_report_line_2a_tax_16_tag')],
      }),
  ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
      (0,0, {
          'repartition_type': 'base',
          'minus_report_expression_ids': [ref('account_tax_report_line_1a_non_bus_gs_tag'), ref('account_tax_report_line_2a_base_16_tag')],
      }),
      (0,0, {
          'repartition_type': 'tax',
          'account_id': ref('lu_2020_account_461411'),
          'minus_report_expression_ids': [ref('account_tax_report_line_2a_tax_16_tag')],
      }),
  ]"/>
    </record>
</odoo>
