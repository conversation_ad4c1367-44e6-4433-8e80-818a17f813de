# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_price_diff
# 
# Translators:
# <PERSON>go <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-02 10:33+0000\n"
"PO-Revision-Date: 2023-02-02 12:25+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_price_diff
#: model:ir.model,name:purchase_price_diff.model_account_move_line
msgid "Journal Item"
msgstr "Andmiku kanderida"

#. module: purchase_price_diff
#: model:ir.model.fields,field_description:purchase_price_diff.field_product_category__property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase_price_diff.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase_price_diff.field_product_template__property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "Hinnavahe konto"

#. module: purchase_price_diff
#: model:ir.model,name:purchase_price_diff.model_product_template
msgid "Product"
msgstr "Toode"

#. module: purchase_price_diff
#: model:ir.model,name:purchase_price_diff.model_product_category
msgid "Product Category"
msgstr "Toote kategooria"

#. module: purchase_price_diff
#: model:ir.model.fields,help:purchase_price_diff.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase_price_diff.field_product_template__property_account_creditor_price_difference
msgid ""
"This account is used in automated inventory valuation to record the price "
"difference between a purchase order and its related vendor bill when "
"validating this vendor bill."
msgstr ""
"Seda kontot kasutatakse automatiseeritud laoseisu hindamisel ostutellimuse "
"ja sellega seotud hankija arve hinnavahe registreerimiseks selle müüja arve "
"kinnitamisel."

#. module: purchase_price_diff
#: model:ir.model.fields,help:purchase_price_diff.field_product_category__property_account_creditor_price_difference_categ
msgid ""
"This account will be used to value price difference between purchase price "
"and accounting cost."
msgstr ""
"Seda kontot kasutatakse ostuhinna ja raamatupidamisliku väärtuse "
"hinnaerinevuse kajastamiseks."
