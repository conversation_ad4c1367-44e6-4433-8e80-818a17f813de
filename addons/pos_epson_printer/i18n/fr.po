# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 05:51+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "Tiroir-caisse"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr ""
"Vérifiez le paramètre 'ID Appareil' dans la configuration de l'imprimante. "
"Il doit être défini sur : "

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "Échec de la connexion à l'imprimante"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr "IP de l'imprimante Epson "

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Epson Receipt Printer IP Address"
msgstr "Adresse IP de l'imprimante de reçus Epson"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s"
msgstr ""
"Si vous êtes sur un serveur sécurisé (HTTPS), veuillez vous assurer "
"d'accepter manuellement le certificat en accédant à %s"

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "Adresse IP locale d'une imprimante de reçus Epson."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "No paper was detected by the printer"
msgstr "L'imprimante n'a pas détecté de papier"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer has enough paper and is ready to print."
msgstr ""
"Veuillez vérifier si l'imprimante a assez de papier et est prête à imprimer."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue"
msgstr ""
"Veuillez vérifier si l'imprimante est toujours connectée.\n"
"Certains navigateurs n'autorisent pas les appels HTTP depuis des sites web vers des appareils du réseau (pour des raisons de sécurité). Si c'est le cas, vous devrez suivre la documentation d'Odoo pour 'Certificat auto-signé pour les imprimantes ePOS' et 'Connexion sécurisée (HTTPS)' pour résoudre le problème"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuration du point de vente"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_res_config_settings__pos_epson_printer_ip
msgid "Pos Epson Printer Ip"
msgstr "IP de l'imprimante Espon du PdV"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Printing failed"
msgstr "Impression échouée "

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr ""
"L'imprimante de reçus Epson sera utilisée à la place de l'imprimante de "
"reçus connectée à l'IoT Box."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The following error code was given by the printer:"
msgstr "L'imprimante a affiché le code d'erreur suivant :"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr "L'imprimante a été jointe avec succès, mais elle n'a pas pu imprimer."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "To find more details on the error reason, please search online for:"
msgstr ""
"Pour trouver plus de détails sur la raison de l'erreur, veuillez rechercher "
"en ligne :"
