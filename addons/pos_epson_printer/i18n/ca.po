# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# j<PERSON><PERSON><PERSON>, 2022
# ma<PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# martioodo hola, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 05:51+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: martioodo hola, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "Calaix"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr ""
"Comprova la configuració de la impressora en la 'Configuració ID del "
"dispositiu'. Hauria d'estar configurat com a:"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustos de configuració"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "Ha fallat la connexió a la impressora"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr "Impressora Epson IP"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Epson Receipt Printer IP Address"
msgstr "Adreça IP de la impressora de rebuts Epson"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s"
msgstr ""
"Si sou en un servidor segur (HTTPS) assegureu-vos que heu acceptat "
"manualment el certificat accedint %s"

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "Adreça IP local d'una impressora de recepció d'Epson."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "No paper was detected by the printer"
msgstr "La impressora no ha detectat paper"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer has enough paper and is ready to print."
msgstr ""
"Comproveu si la impressora té prou paper i està preparada per imprimir."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue"
msgstr ""
"Comproveu si la impressora encara està connectada. \n"
"Alguns navegadors no permeten trucades HTTP des de llocs web a dispositius de la xarxa (per raons de seguretat). Si és el cas, haureu de seguir la documentació de l'Odoo per a 'Certificat d'EPOS signats soles' i 'Connexió segura (HTTPS)' per a resoldre el problema"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuració del Punt de Venda"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_res_config_settings__pos_epson_printer_ip
msgid "Pos Epson Printer Ip"
msgstr "Pos Epson Printer Ip"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Printing failed"
msgstr "Ha fallat la impressió"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr ""
"La impressora de recepció d'Epson s'utilitzarà en lloc de la impressora de "
"recepció connectada a la caixa IoT."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The following error code was given by the printer:"
msgstr "La impressora ha proporcionat el següent codi d'error:"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr ""
"La connexió a la impressora s'ha establit amb èxit, però no ha conseguit "
"imprimir."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "To find more details on the error reason, please search online for:"
msgstr "Per a trobar més detalls sobre el motiu d'error, cerqueu en línia:"
