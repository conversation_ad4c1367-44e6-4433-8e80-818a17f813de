# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_automation
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Paraguay) (https://www.transifex.com/odoo/teams/41243/es_PY/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_PY\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_help
msgid "Action Description"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_name
msgid "Action Name"
msgstr ""

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation_line_test
msgid "Action Rule Line Test"
msgstr ""

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation_lead_test
msgid "Action Rule Test"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_state
msgid "Action To Do"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_type
msgid "Action Type"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_active
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_active
msgid "Active"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_channel_ids
msgid "Add Channels"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_partner_ids
msgid "Add Followers"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_filter_domain
msgid "Apply on"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_is_assigned_to_admin
msgid "Assigned to admin user"
msgstr ""

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
msgid "Automated Action"
msgstr ""

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
msgid "Automated Actions"
msgstr ""

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation"
msgstr ""

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
#: model:ir.cron,cron_name:base_automation.ir_cron_data_base_automation_check
#: model:ir.cron,name:base_automation.ir_cron_data_base_automation_check
msgid "Base Action Rule: check and execute"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_recursive
#: model:ir.actions.server,name:base_automation.test_rule_recursive_ir_actions_server
msgid "Base Automation: test recursive rule"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_on_create
#: model:ir.actions.server,name:base_automation.test_rule_on_create_ir_actions_server
msgid "Base Automation: test rule on create"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_on_recompute
#: model:ir.actions.server,name:base_automation.test_rule_on_recompute_ir_actions_server
msgid "Base Automation: test rule on recompute"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_on_line
#: model:ir.actions.server,name:base_automation.test_rule_on_line_ir_actions_server
msgid "Base Automation: test rule on secondary model"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_on_write
#: model:ir.actions.server,name:base_automation.test_rule_on_write_ir_actions_server
msgid "Base Automation: test rule on write"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_on_write_check_context
#: model:ir.actions.server,name:base_automation.test_rule_on_write_check_context_ir_actions_server
msgid "Base Automation: test rule on write check context"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "Based on Form Modification"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "Based on Timed Condition"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_filter_pre_domain
msgid "Before Update Domain"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_binding_model_id
msgid "Binding Model"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_binding_type
msgid "Binding Type"
msgstr ""

#. module: base_automation
#: selection:base.automation.lead.test,state:0
msgid "Cancelled"
msgstr "Cancelado"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_lead_test_customer
msgid "Check this box if this contact is a customer."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_child_ids
msgid "Child Actions"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_child_ids
msgid ""
"Child server actions that will be executed. Note that the last return "
"returned action value will be used as global return value."
msgstr ""

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Click to setup a new automated automation."
msgstr ""

#. module: base_automation
#: selection:base.automation.lead.test,state:0
msgid "Closed"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_on_change_fields
msgid "Comma-separated list of field names that triggers the onchange."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_crud_model_id
msgid "Create/Write Target Model"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_create_uid
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_create_uid
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_create_date
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_create_date
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test_create_date
msgid "Created on"
msgstr "Creado en"

#. module: base_automation
#: selection:base.automation,trg_date_range_type:0
msgid "Days"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_deadline
msgid "Deadline"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_trg_date_range
msgid ""
"Delay after the trigger date.\n"
"                                    You can put a negative number if you need a delay before the\n"
"                                    trigger date, like sending a reminder 15 minutes before a meeting."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_trg_date_range
msgid "Delay after trigger date"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_trg_date_range_type
msgid "Delay type"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_display_name
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_display_name
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test_display_name
msgid "Display Name"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_template_id
msgid "Email Template"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_xml_id
msgid "External ID"
msgstr ""

#. module: base_automation
#: selection:base.automation,trg_date_range_type:0
msgid "Hours"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_id
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_id
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test_id
msgid "ID"
msgstr "ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_filter_domain
msgid ""
"If present, this condition must be satisfied before executing the action "
"rule."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record."
msgstr ""

#. module: base_automation
#: selection:base.automation.lead.test,state:0
msgid "In Progress"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_customer
msgid "Is a Customer"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_date_action_last
msgid "Last Action"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation___last_update
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test___last_update
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test___last_update
msgid "Last Modified on"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_last_run
msgid "Last Run"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_write_uid
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test_write_uid
#: model:ir.model.fields,field_description:base_automation.field_base_automation_write_uid
msgid "Last Updated by"
msgstr "Ultima actualización por"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_write_date
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test_write_date
#: model:ir.model.fields,field_description:base_automation.field_base_automation_write_date
msgid "Last Updated on"
msgstr "Ultima actualización en"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test_lead_id
msgid "Lead"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_line_ids
msgid "Line"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_link_field_id
msgid "Link using field"
msgstr ""

#. module: base_automation
#: selection:base.automation,trg_date_range_type:0
msgid "Minutes"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_model_id
#: model:ir.model.fields,field_description:base_automation.field_base_automation_model_name
msgid "Model"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_crud_model_name
msgid "Model Description"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_crud_model_id
msgid ""
"Model for record creation / update. Set this field only to specify a "
"different model than the base model."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_model_id
msgid "Model on which the server action runs."
msgstr ""

#. module: base_automation
#: selection:base.automation,trg_date_range_type:0
msgid "Months"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test_name
msgid "Name"
msgstr ""

#. module: base_automation
#: selection:base.automation.lead.test,state:0
msgid "New"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_on_change_fields
msgid "On Change Fields Trigger"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "On Creation"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "On Creation & Update"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "On Deletion"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "On Update"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_help
msgid ""
"Optional help text for the users with a description of the target view, such"
" as its usage and purpose."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_partner_id
msgid "Partner"
msgstr "Empresa"

#. module: base_automation
#: selection:base.automation.lead.test,state:0
msgid "Pending"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_priority
msgid "Priority"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_link_field_id
msgid ""
"Provide the field used to link the newly created recordon the record on used"
" by the server action."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_code
msgid "Python Code"
msgstr ""

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Remove Action"
msgstr ""

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Remove the contextual action related to this server action"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_user_id
msgid "Responsible"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_action_server_id
msgid "Server Actions"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_binding_model_id
msgid ""
"Setting a value makes this action available in the sidebar for the given "
"model."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_state
msgid "Status"
msgstr "Estado"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test_name
msgid "Subject"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_trigger
msgid "Trigger Condition"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_trg_date_id
msgid "Trigger Date"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create or Copy a new Record': create a new record with new values, or copy an existing record in your database\n"
"- 'Write on a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Add Followers': add followers to a record (available in Discuss)\n"
"- 'Send Email': automatically send an email (available in email_template)"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_usage
msgid "Usage"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_trg_date_calendar_id
msgid "Use Calendar"
msgstr ""

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Use automated actions to automatically trigger actions for\n"
"                various screens. Example: a lead created by a specific user may\n"
"                be automatically set to a specific sales channel, or an\n"
"                opportunity which still has status pending after 14 days might\n"
"                trigger an automatic reminder email."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test_user_id
msgid "User"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_fields_lines
msgid "Value Mapping"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possible to use a "
"calendar to compute the date based on working days."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_sequence
msgid ""
"When dealing with multiple actions, the execution order is based on the "
"sequence. Low number means high priority."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                                  If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_code
msgid ""
"Write Python code that the action will execute. Some variables are available"
" for use; help about pyhon expression is given in the help tab."
msgstr ""

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "ir.actions.server"
msgstr ""
