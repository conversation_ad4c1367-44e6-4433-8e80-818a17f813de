# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_bot
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Aaaaaw that's really cute but, you know, bots don't work that way. You're "
"too human for me! Let's keep it professional ❤️"
msgstr "ああ、なんと可愛いらしい。でもね、ボットはそうはいかないのです。ボットにとっては、あなたは人間的すぎる！プロに徹しましょう❤️"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__disabled
msgid "Disabled"
msgstr "無効"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_channel
msgid "Discussion Channel"
msgstr "ディスカッションチャンネル"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_thread
msgid "Email Thread"
msgstr "Eメールスレッド"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Great! 👍<br/>To access special commands, <b>start your sentence with</b> "
"<span class=\"o_odoobot_command\">/</span>. Try getting help."
msgstr ""
"素晴らしい！👍<br/>特別コマンドにアクセスするには<b>文章の始まりを</b><span "
"class=\"o_odoobot_command\">/</span>にして下さい。助けを借りてみましょう。"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_ir_http
msgid "HTTP Routing"
msgstr "HTTPルーティング"

#. module: mail_bot
#: code:addons/mail_bot/models/res_users.py:0
#, python-format
msgid ""
"Hello,<br/>Odoo's chat helps employees collaborate efficiently. I'm here to "
"help you discover its features.<br/><b>Try to send me an emoji</b> <span "
"class=\"o_odoobot_command\">:)</span>"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "Hmmm..."
msgstr "うーん..."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"I am a simple bot, but if that's a dog, he is the cutest 😊 "
"<br/>Congratulations, you finished this tour. You can now <b>close this "
"conversation</b> or start the tour again with typing <span "
"class=\"o_odoobot_command\">start the tour</span>. Enjoy discovering Odoo!"
msgstr ""
"私は単純なロボットですが、これが犬なら最高にかわいいでしょう😊 "
"<br/>おめでとうございます。これでツアーは終了です。<b>この会話を閉じる</b>または <span "
"class=\"o_odoobot_command\">ツアーを開始する</span>を入力してツアーを再開できます。Odooをお楽しみ下さい!"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "I'm afraid I don't understand. Sorry!"
msgstr "理解できません。ごめんなさい！"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"I'm not smart enough to answer your question.<br/>To follow my guide, ask: "
"<span class=\"o_odoobot_command\">start the tour</span>."
msgstr ""
"私はあなたの質問に答えられるほど賢くありません。<br/>私のガイドに従うには: <span "
"class=\"o_odoobot_command\">ツアーを開始する</span>をお願いして下さい。"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__idle
msgid "Idle"
msgstr "アイドル"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_bot
msgid "Mail Bot"
msgstr "メールボット"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not exactly. To continue the tour, send an emoji: <b>type</b> <span "
"class=\"o_odoobot_command\">:)</span> and press enter."
msgstr ""
"正確ではありません。ツアーを続けるには、絵文字を送って下さい: <b>入力</b> <span "
"class=\"o_odoobot_command\">:)</span>そしてエンターを押して下さい。"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__not_initialized
msgid "Not initialized"
msgstr "未初期化"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not sure what you are doing. Please, type <span "
"class=\"o_odoobot_command\">/</span> and wait for the propositions. Select "
"<span class=\"o_odoobot_command\">help</span> and press enter"
msgstr ""
"何をしているかわかりません。 <span "
"class=\"o_odoobot_command\">/</span>を入力して提案をお待ちください。<span "
"class=\"o_odoobot_command\">ヘルプ</span>を選択して、エンターを押して下さい。"

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_state
msgid "OdooBot Status"
msgstr "OdooBotステータス"

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_failed
msgid "Odoobot Failed"
msgstr "Odoobotに失敗しました"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_attachement
msgid "Onboarding attachment"
msgstr "オンボーディング添付"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_command
msgid "Onboarding command"
msgstr "オンボーディングコマンド"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_emoji
msgid "Onboarding emoji"
msgstr "オンボーディング絵文字"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_ping
msgid "Onboarding ping"
msgstr "オンボーディングping"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Sorry I'm sleepy. Or not! Maybe I'm just trying to hide my unawareness of "
"human language...<br/>I can show you features if you write: <span "
"class=\"o_odoobot_command\">start the tour</span>."
msgstr ""
"ごめんなさい、眠たいです。いや、眠くはない！もしかしたら、人の言葉を知らないことを隠そうとしているだけかもしれません...。<br/>もしこう書いてもらえたら、機能をお見せできます:"
" <span class=\"o_odoobot_command\">ツアーを始める</span>"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Sorry, I am not listening. To get someone's attention, <b>ping him</b>. "
"Write <span class=\"o_odoobot_command\">@OdooBot</span> and select me."
msgstr ""
"ごめんなさい、聞いていません。注意を引くには、 <b>その人をping</b>します。 <span "
"class=\"o_odoobot_command\">@OdooBot</span>と書いて私を選択して下さい。"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "That's not nice! I'm a bot but I have feelings... 💔"
msgstr "それは優しくない！私はボットだけど感情はあります... 💔"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"To <b>send an attachment</b>, click on the <i class=\"fa fa-paperclip\" "
"aria-hidden=\"true\"></i> icon and select a file."
msgstr ""
"<b>添付を送信する</b>には、 <i class=\"fa fa-paperclip\" aria-"
"hidden=\"true\"></i>アイコンをクリックしてファイルを選択して下さい。"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "To start, try to send me an emoji :)"
msgstr "始めるには、私に絵文字を送って下さい :)"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Unfortunately, I'm just a bot 😞 I don't understand! If you need help "
"discovering our product, please check <a "
"href=\"https://www.odoo.com/documentation\" target=\"_blank\">our "
"documentation</a> or <a href=\"https://www.odoo.com/slides\" "
"target=\"_blank\">our videos</a>."
msgstr ""
"残念ながら、私はただのボットです😞 理解できません！もし当社のプロダクトについて知りたいならば、 <a "
"href=\"https://www.odoo.com/documentation\" target=\"_blank\">当社のドキュメント</a> "
"または <a href=\"https://www.odoo.com/slides\" "
"target=\"_blank\">当社の動画</a>をチェックしてみて下さい。"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_res_users
msgid "User"
msgstr "ユーザ"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Wow you are a natural!<br/>Ping someone with @username to grab their "
"attention. <b>Try to ping me using</b> <span "
"class=\"o_odoobot_command\">@OdooBot</span> in a sentence."
msgstr ""
"わあ、才能がありますね！<br/>誰かの注意を引くには@usernameでPingしましょう。<b>私をPingするには</b> <span "
"class=\"o_odoobot_command\">@OdooBot</span>を文章の中で使って下さい。"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Yep, I am here! 🎉 <br/>Now, try <b>sending an attachment</b>, like a picture"
" of your cute dog..."
msgstr "はい、ここにいます！ 🎉 <br/>では <b>添付を送信</b>してみて下さい、あなたの可愛い犬の写真とか..."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "fuck"
msgstr "くそー"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "help"
msgstr "ヘルプ"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "i love you"
msgstr "大好きです"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "love"
msgstr "ラブ"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "start the tour"
msgstr "ツアーを開始"
