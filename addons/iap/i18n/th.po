# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON>iam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "ข้อมูลเกี่ยวกับบัญชี"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "โทเค็นบัญชี"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Buy credits"
msgstr "ซื้อเครดิต"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Cancel"
msgstr "ยกเลิก"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "บริษัทเดียว"

#. module: iap
#: model:ir.model,name:iap.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Documentation"
msgstr "เอกสารประกอบ"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "IAP"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "บัญชี IAP"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "บัญชี IAP"

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr "API การเพิ่มข้อมูลลูกค้าเป้าหมาย IAP"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
msgid "ID"
msgstr "ไอดี"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Insufficient credit to perform this service."
msgstr "เครดิตไม่เพียงพอที่จะดำเนินการบริการนี้"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Odoo IAP"

#. module: iap
#: model:ir.actions.server,name:iap.open_iap_account
msgid "Open IAP Account"
msgstr "เปิดบัญชี IAP"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
msgid "Service Name"
msgstr "ชื่อบริการ"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
#, python-format
msgid "Start a Trial at Odoo"
msgstr "เริ่มการทดลองใช้งานที่ Odoo"

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
#, python-format
msgid ""
"The request to the service timed out. Please contact the author of the app. "
"The URL it tried to contact was %s"
msgstr ""
"การร้องขอไปยังบริการหมดเวลา โปรดติดต่อผู้สร้างแอป URL ที่พยายามติดต่อคือ %s"

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app. The url it tried to contact was %s"
msgstr ""
"URL ที่บริการนี้ร้องขอแสดงข้อผิดพลาด โปรดติดต่อผู้เขียนแอป URL "
"ที่พยายามติดต่อคือ %s"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
#, python-format
msgid "View My Services"
msgstr "ดูการบริการของฉัน"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr "ดูบริการ IAP ของคุณและเติมเครดิตของคุณ"
