# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* privacy_lookup
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <gclau<PERSON><EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-09 14:06+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__additional_note
msgid "Additional Note"
msgstr "Extra notitie"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__anonymized_email
msgid "Anonymized Email"
msgstr "Geanonimiseerde e-mail"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__anonymized_name
msgid "Anonymized Name"
msgstr "Geanonimiseerde naam"

#. module: privacy_lookup
#: model:ir.actions.server,name:privacy_lookup.ir_actions_server_archive_all
msgid "Archive Selection"
msgstr "Selectie archiveren"

#. module: privacy_lookup
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
#, python-format
msgid "Archived"
msgstr "Gearchiveerd"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Can be archived"
msgstr "Kan gearchiveerd worden"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__create_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__create_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__create_uid
msgid "Created by"
msgstr "Gemaakt door"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__create_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__create_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__create_date
msgid "Created on"
msgstr "Gemaakt op"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__date
msgid "Date"
msgstr "Datum"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "Delete"
msgstr "Verwijderen"

#. module: privacy_lookup
#: model:ir.actions.server,name:privacy_lookup.ir_actions_server_unlink_all
msgid "Delete Selection"
msgstr "Selectie verwijderen"

#. module: privacy_lookup
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "Deleted"
msgstr "Verwijderen"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__display_name
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__display_name
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__display_name
msgid "Display Name"
msgstr "Weergavenaam"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_model
msgid "Document Model"
msgstr "Documentmodel"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__email
msgid "Email"
msgstr "E-mail"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__execution_details
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__execution_details
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__execution_details
msgid "Execution Details"
msgstr "Uitvoeringsdetails"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__records_description
msgid "Found Records"
msgstr "Records gevonden"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Group By"
msgstr "Groeperen op"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__user_id
msgid "Handled By"
msgstr "Behandeld door"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__has_active
msgid "Has Active"
msgstr "Heeft actieve"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__id
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__id
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__id
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "ID"
msgstr "ID"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__is_active
msgid "Is Active"
msgstr "Is Actief"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__is_unlinked
msgid "Is Unlinked"
msgstr "Is ontkoppeld"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log____last_update
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard____last_update
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__write_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__write_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__write_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__write_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__write_date
msgid "Last Updated on"
msgstr "Laatst geupdate op"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__line_ids
msgid "Line"
msgstr "Regel"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__line_count
msgid "Line Count"
msgstr "Aantal regels"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__log_id
msgid "Log"
msgstr "Logboek"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "Lookup"
msgstr "Lookup"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Model"
msgstr "Model"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__name
msgid "Name"
msgstr "Naam"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "Open Record"
msgstr "Record openen"

#. module: privacy_lookup
#: model:ir.ui.menu,name:privacy_lookup.privacy_menu
msgid "Privacy"
msgstr "Privacy"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_log
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_log_view_form
msgid "Privacy Log"
msgstr "Privacy Logboek"

#. module: privacy_lookup
#: model:ir.actions.act_window,name:privacy_lookup.privacy_log_action
#: model:ir.actions.act_window,name:privacy_lookup.privacy_log_form_action
#: model:ir.ui.menu,name:privacy_lookup.pricacy_log_menu
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_log_view_list
msgid "Privacy Logs"
msgstr "Privacy Logboeken"

#. module: privacy_lookup
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#: model:ir.actions.act_window,name:privacy_lookup.action_privacy_lookup_wizard
#: model:ir.actions.server,name:privacy_lookup.ir_action_server_action_privacy_lookup_partner
#: model:ir.actions.server,name:privacy_lookup.ir_action_server_action_privacy_lookup_user
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
#, python-format
msgid "Privacy Lookup"
msgstr "Privacy Lookup"

#. module: privacy_lookup
#: model:ir.actions.act_window,name:privacy_lookup.action_privacy_lookup_wizard_line
msgid "Privacy Lookup Line"
msgstr "Privacy Lookup regel"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_lookup_wizard
msgid "Privacy Lookup Wizard"
msgstr "Privacy Lookup Wizard"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_lookup_wizard_line
msgid "Privacy Lookup Wizard Line"
msgstr "Privacy Lookup Wizard regel"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__resource_ref
msgid "Record"
msgstr "Record"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__records_description
msgid "Records Description"
msgstr "Records omschrijving"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "References"
msgstr "Referenties"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_model_id
msgid "Related Document Model"
msgstr "Gerelateerde documentmodel"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_id
msgid "Resource ID"
msgstr "Resource-ID"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_name
msgid "Resource name"
msgstr "Naam resource"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Search References"
msgstr "Referenties opzoeken"

#. module: privacy_lookup
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "The record is already unlinked."
msgstr "Het record is al ontkoppeld."

#. module: privacy_lookup
#: code:addons/privacy_lookup/models/privacy_log.py:0
#, python-format
msgid "This email address is not valid (%s)"
msgstr "Dit e-mailadres is niet geldig (%s)"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid ""
"This operation is irreversible. Do you wish to proceed to the record "
"deletion ?"
msgstr ""
"Deze operatie is onomkeerbaar. Wil je doorgaan met het verwijderen van het "
"record?"

#. module: privacy_lookup
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "Unarchived"
msgstr "Niet gearchiveerd"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__wizard_id
msgid "Wizard"
msgstr "Wizard"
