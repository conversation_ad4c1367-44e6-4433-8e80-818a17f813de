# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payumoney
# 
# Translators:
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2019
# <PERSON>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Last-Translator: <PERSON>, 2019\n"
"Language-Team: Croatian (https://www.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_key
msgid "Merchant Key"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_salt
msgid "Merchant Salt"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields.selection,name:payment_payumoney.selection__payment_acquirer__provider__payumoney
msgid "PayUmoney"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:0
#, python-format
msgid "PayUmoney: invalid shasign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:0
#, python-format
msgid "PayUmoney: received data for reference %s; multiple orders found"
msgstr "PayUmoney: zaprimljeni podaci za referencu %s; pronađeno više naloga"

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:0
#, python-format
msgid "PayUmoney: received data for reference %s; no order found"
msgstr "PayUmoney: zaprimljeni podaci za referencu %s; nema pronađenih naloga"

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment.py:0
#, python-format
msgid ""
"PayUmoney: received data with missing reference (%s) or pay_id (%s) or "
"shashign (%s)"
msgstr ""

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Stjecatelj plaćanja"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcija plaćanja"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__provider
msgid "Provider"
msgstr "Davatelj "
