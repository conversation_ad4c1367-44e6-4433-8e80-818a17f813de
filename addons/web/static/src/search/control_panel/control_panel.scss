.o_control_panel {
    border-bottom: 1px solid $border-color;
    @include o-webclient-padding($top: map-get($spacers, 2), $bottom: map-get($spacers, 2));
    background-color: $o-control-panel-background-color;

    > div {
        display: flex;
        min-height: $o-statusbar-height;
    }

    @include media-breakpoint-up(md) {
        .o_cp_top_left, .o_cp_top_right,
        .o_cp_bottom_left, .o_cp_bottom_right {
            width: 50%;
        }
    }

    @include media-breakpoint-down(md) {
        > div.o_cp_bottom {
            justify-content: space-between;
            min-height: auto;
        }

        &.o_mobile_sticky {
            @include o-position-sticky();
            z-index: 10;
        }

        .o_cp_action_menus {
            .o_dropdown_title {
                @include visually-hidden;
            }

            .o_dropdown_chevron, .o_dropdown_caret {
                display: none;
            }
        }

        .o_cp_searchview {
            min-height: 35px;

            &.o_searchview_quick {
                display: flex;
                align-items: flex-start;
                width: 100%;

                > .o_searchview {
                    display: flex;
                    flex: 1 1 auto;
                    align-items: center;
                    border-bottom: 1px solid $o-brand-secondary;

                    > .o_searchview_input_container {
                        flex: 1 1 auto;
                    }
                    > .o_enable_searchview {
                        margin: 0;
                        padding-left: 0;
                        &, &:hover {
                            color: $gray-600;
                        }
                    }
                }
            }
        }

        .o_cp_switch_buttons {
            &.show > .dropdown-menu {
                display: inline-flex;
                min-width: 0px;
            }
        }
    }

    .breadcrumb {
        font-size: 18px;
        min-width: 0;

        > li {
            @include o-text-overflow();
        }

        @include media-breakpoint-down(md) {
            width: 100%;
            overflow: hidden;
            flex-wrap: nowrap;

            > li {
                margin: 0;

                &:before {
                    display: none;
                }

                &.o_back_button {
                    flex-shrink: 0;
                    &, & ~ li {
                        margin: auto 0;
                    }

                    &:before {
                        font-family: FontAwesome;
                        content: ""; // fa-arrow-left
                        display: inline-block;

                        padding: 0; // override bootstrap
                        color: inherit;
                    }

                    &.btn {
                        padding: $o-cp-button-sm-no-border-padding;
                    }

                    > a {
                        display: none;
                    }
                }
            }
        }
    }

    .o_cp_top_right {
        min-height: $o-cp-breadcrumb-height;
    }

    .o_cp_bottom_left {
        display: flex;
        flex-wrap: wrap;
        gap: map-get($spacers, 3) map-get($spacers, 2);

        > .o_cp_action_menus {
            margin-left: auto;
            padding-right: 0;

            @include media-breakpoint-up(md) {
                padding-right: 10px;
            }

            @include media-breakpoint-up(sm) {
                display: flex;
                padding-right: 25px;

                > .btn-group {
                    margin: auto 0;
                    display: block;
                }

                .dropdown-toggle {
                    margin-right: 0;
                }
            }

            .o_hidden_input_file {
                position: relative;
                input.o_input_file {
                    position: absolute;
                    top: 1px;
                    opacity: 0;
                    width: 100%;
                    height: 26px;
                }
                .o_form_binary_form span {
                    padding: 3px 25px;
                    color: $o-brand-primary;
                }
                .o_form_binary_form:hover {
                    background-color: $table-hover-bg;
                }
            }
            .o_sidebar_delete_attachment {
                padding: 0px;
                position: absolute;
                top: 5px;
                right: 10px;
            }
            .dropdown-toggle {
                margin-right: 0;

                @include media-breakpoint-up(md) {
                    margin-right: 15px;
                }
            }
        }
    }

    .o_cp_bottom_right {
        display: flex;
        flex-wrap: wrap;

        @include media-breakpoint-up(md) {
            column-gap: $o-horizontal-padding;
        }

        > .o_form_status_indicator {
            @include media-breakpoint-down(md) {
                .o_form_status_indicator_buttons > .btn {
                    padding: $o-cp-button-sm-no-border-padding;
                }
            }
        }

        > .o_cp_pager {
            padding-left: 5px;
            text-align: center;
            user-select: none;
            .o_pager_limit_fetch:not(.disabled), .o_pager_value {
                cursor: pointer;
            }
        }
    }
}

.o_x2m_control_panel {
    display: flex;
    flex-flow: row wrap;

    .o_cp_buttons {
        display: flex;
        margin-right: auto;
        > div {
            margin-top: 5px;
        }
        .o-kanban-button-new {
            margin-left: $o-kanban-record-margin;
        }
    }
    .o_cp_pager {
        display: flex;
        margin-left: auto;
    }
}

@include media-breakpoint-down(md) {
    .o_rtl .o_control_panel .o_back_button:before {
        transform: rotate(180deg);
    }
}

@media print {
    .o_control_panel {
        display: none;
    }
}
