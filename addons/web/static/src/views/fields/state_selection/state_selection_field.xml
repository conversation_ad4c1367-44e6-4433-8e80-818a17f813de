<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.StateSelectionField" owl="1">
        <t t-if="isReadonly">
            <button class="d-flex align-items-center btn fw-normal p-0">
                <span t-attf-class="o_status {{ statusColor(currentValue) }} "/>
                <span t-if="showLabel" class="o_status_label ms-1" t-esc="label"/>
            </button>
        </t>
        <t t-else="">
            <Dropdown tooltip="label" togglerClass="'btn btn-link d-flex p-0'">
                <t t-set-slot="toggler">
                    <div class="d-flex align-items-center">
                        <span t-attf-class="o_status {{ statusColor(currentValue) }} "/>
                        <span t-if="showLabel" class="o_status_label ms-1" t-esc="label"/>
                    </div>
                </t>
                <t t-foreach="availableOptions" t-as="option" t-key="option[0]">
                    <DropdownItem onSelected="() => props.update(option[0], { save: props.autosave })">
                        <div class="d-flex align-items-center">
                            <span t-attf-class="o_status {{ statusColor(option[0]) }} "/>
                            <span class="ms-2" t-esc="option[1]"/>
                        </div>
                    </DropdownItem>
                </t>
            </Dropdown>
        </t>
    </t>

</templates>
