<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.KanbanColumnQuickCreate" owl="1">
        <div class="o_column_quick_create flex-shrink-0 flex-grow-1 flex-md-grow-0" t-ref="root">
            <div t-if="props.folded" class="o_quick_create_folded m-3 text-nowrap fw-bold opacity-75 opacity-100-hover" t-on-click="unfold">
                <button class="o_kanban_add_column btn btn-outline-secondary w-100">
                    <i class="fa fa-plus me-2" role="img" aria-label="Add column" title="Add column"/><t t-out="relatedFieldName"/>
                </button>
            </div>
            <div t-else="" class="o_quick_create_unfolded pt-3 px-2 pb-2">
                <div class="o_kanban_header top-0 pb-3">
                    <div class="input-group">
                        <input type="text"
                            class="form-control o_input bg-transparent fs-4"
                            t-attf-placeholder="{{ relatedFieldName }}..."
                            t-ref="autofocus"
                            t-on-focus="() => state.hasInputFocused = true"
                            t-on-blur="() => state.hasInputFocused = false"
                            t-on-keydown="onInputKeydown"
                        />
                        <span class="input-group-append">
                            <button class="btn btn-primary o_kanban_add" type="button" t-on-click="validate">
                                Add
                            </button>
                        </span>
                    </div>
                    <small t-if="!env.isSmall and state.hasInputFocused" class="o_discard_msg text-muted float-end">
                        Esc to discard
                    </small>
                    <t t-if="canShowExamples and !env.isSmall">
                        <button type="button" class="btn btn-link o_kanban_examples p-0" t-on-click="showExamples">See examples</button>
                    </t>
                </div>
                <div t-foreach="[,,,]" t-as="i" t-key="i_index" class="o_kanban_muted_record mt-3 py-5 bg-300" />
            </div>
        </div>
    </t>

</templates>
