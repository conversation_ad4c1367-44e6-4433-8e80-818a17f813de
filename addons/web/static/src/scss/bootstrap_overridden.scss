///
/// This file is a copy of the bootstrap _variables.scss file where all the
/// left-untouched variables definition have been removed.
///

//
// Color system
//

//Restore BS4 Colors
$blue: #007bff !default;
$pink: #e83e8c !default;
$green: #28a745 !default;
$cyan: #17a2b8 !default;
//End Restore BS4 Colors

// All of those are BS default
$white:    $o-white !default;
$gray-100: $o-gray-100 !default;
$gray-200: $o-gray-200 !default;
$gray-300: $o-gray-300 !default;
$gray-400: $o-gray-400 !default;
$gray-500: $o-gray-500 !default;
$gray-600: $o-gray-600 !default;
$gray-700: $o-gray-700 !default;
$gray-800: $o-gray-800 !default;
$gray-900: $o-gray-900 !default;

$black:    $o-black !default;

$primary: $o-brand-primary !default;
$secondary: $white !default;
$dark: $o-gray-900 !default;

$success: $o-success !default;
$info: $o-info !default;
$warning: $o-warning !default;
$danger: $o-danger !default;

$theme-colors: () !default;
$theme-colors: map-merge(
    (
        "odoo": $o-brand-odoo,
    ),
    $theme-colors
);

// The contrast ratio to reach against white, to determine if color changes from "light" to "dark". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.
// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast
$min-contrast-ratio: 3 !default;

// Options
//
// Quickly modify global styling by enabling or disabling optional features.
$enable-smooth-scroll: false !default;

// Prefix for :root CSS variables
$variable-prefix: '' !default;

// Restore negative margins disabled in BS5 by default
$enable-negative-margins: true !default;

// Enable CSS Grid classes
$enable-cssgrid: true !default;

// Spacing
$spacer: 1rem !default;

// Body
//
// Settings for the `<body>` element.

$body-bg: $o-webclient-background-color !default;
$body-color: $o-main-text-color !default;

// Links
//
// Style anchor elements.

$link-color: $o-main-link-color !default;
$link-decoration: none !default;
$link-hover-decoration: none !default;
$link-shade-percentage: 15% !default;

// Muted
//
// Style .text-muted elements

$text-muted: $o-main-color-muted !default;

// Grid columns
//
// Set the number of columns and specify the width of the gutters.

$grid-gutter-width: $o-horizontal-padding * 2 !default;

// Components
//
// Define common padding and border radius sizes and more.

$border-color: $o-border-color!default;

$border-radius: $o-border-radius !default;
$border-radius-sm: $o-border-radius-sm !default;
$border-radius-lg: $o-border-radius-lg !default;
$border-color: $o-gray-300 !default;

$component-active-color: $o-brand-primary !default;
$component-active-bg: $gray-200 !default;

$caret-width: 4px !default;

// Typography
//
// Font, line-height, and color for body text, headings, and more.

// Desired weights, not necessarily matching the final result on screen.
// Check primary_variables.scss for more info.
$font-weight-normal: $o-font-weight-normal !default;
$font-weight-bold: $o-font-weight-medium !default;

$font-family-sans-serif: $o-font-family-sans-serif !default;

$font-size-base: $o-font-size-base !default;
$line-height-base: $o-line-height-base !default;

$h1-font-size: $font-size-base * 2.0 !default;
$h2-font-size: $font-size-base * 1.5 !default;
$h3-font-size: $font-size-base * 1.3 !default;
$h4-font-size: $font-size-base * 1.2 !default;
$h5-font-size: $font-size-base * 1.1 !default;

$headings-font-family: $o-headings-font-family !default;
$headings-font-weight: $font-weight-bold !default;
$headings-color: $o-main-headings-color !default;

// Tables
//
// Customizes the `.table` component with basic values, each used across all table variations.

$table-striped-color: inherit !default;
$table-striped-bg-factor: .01 !default;
$table-hover-bg-factor: .055 !default;
$table-border-color: $gray-200 !default;
$table-group-separator-color: $gray-200 !default;
$table-cell-padding-x: .75rem !default;
$table-cell-padding-y: .75rem !default;
$table-cell-padding-x-sm: .3rem !default;
$table-cell-padding-y-sm: .5rem !default;
$table-striped-order: even !default;

$table-active-bg-factor: .05 !default;
$table-active-color: $headings-color !default;
$table-th-font-weight: $headings-font-weight !default;

// Buttons
//

$btn-font-weight: $font-weight-bold !default;
$btn-disabled-opacity: $o-opacity-disabled !default;
$btn-border-radius-lg: $border-radius !default;

// Dropdowns
//
// Dropdown menu container and contents.

$dropdown-border-color: $gray-300 !default;

$dropdown-link-color: $o-main-text-color !default;
$dropdown-link-hover-color: $gray-900 !default;
$dropdown-link-hover-bg: rgba($black, 0.08) !default;

$dropdown-link-active-color: $gray-900 !default;
$dropdown-link-active-bg: transparent !default;

$dropdown-link-disabled-color: $o-main-color-muted !default;

$dropdown-item-padding-y: $o-dropdown-vpadding !default;
$dropdown-item-padding-x: $o-dropdown-hpadding !default;

// List group

$list-group-active-color: $o-list-group-active-color !default;
$list-group-active-bg: $o-list-group-active-bg !default;
$list-group-action-hover-color: $gray-900 !default;

// Z-index master list

// Change the z-index of the modal-backdrop elements to be equal to the
// modal elements' ones. Bootstrap does not support multi-modals, and without
// this rule all the modal-backdrops are below all the opened modals.
// Indeed, bootstrap forces them to a lower z-index as the modal-backdrop
// element (unique in their supported cases) might be put after the modal
// element (if the modal is already in the DOM, hidden, then opened). This
// cannot happen in odoo though as modals are not hidden but removed from
// the DOM and are always put at the end of the body when opened.
//
// TODO the following code was disabled because it is saas-incompatible
//
// $zindex-modal-backdrop: $zindex-modal;

// Navbar
$navbar-padding-x: $spacer !default;

// Navs

$nav-tabs-link-active-bg: $white !default;

$nav-pills-border-radius: 0 !default;
$nav-pills-link-active-color: $white !default;
$nav-pills-link-active-bg: $o-brand-primary !default;

// Popovers

$popover-border-radius: $border-radius !default;

// Toasts

$toast-max-width: 320px !default;
$toast-padding-x: 1.5rem !default;
$toast-padding-y: 0.5rem !default;
$toast-font-size: $font-size-base !default;
$toast-background-color: rgba($white, .7) !default;
$toast-header-background-color: $toast-background-color !default;

// Modals

// Padding applied to the modal body
$modal-inner-padding: $o-horizontal-padding !default;
$modal-footer-margin-between: 1px !default;

$modal-lg: $o-modal-lg !default;
$modal-md: $o-modal-md !default;

$modal-content-border-radius: $border-radius !default;

$modal-scale-transform: none !default;

// Breadcrumbs

$breadcrumb-padding-y: 0 !default;
$breadcrumb-padding-x: 0 !default;
$breadcrumb-margin-bottom: 0 !default;

$breadcrumb-bg: $o-control-panel-background-color !default;
$breadcrumb-item-padding: .2em !default;

// Code

$code-color: $o-main-code-color!default;

// User input typically entered via keyboard

$kbd-color: $o-gray-700 !default;
$kbd-bg: $o-gray-100 !default;
$kbd-box-shadow: 0px 1px 1px rgba($o-black, 0.2), inset 0px -1px 1px 1px rgba($o-gray-200, 0.8), inset 0px 2px 0px 0px rgba($o-white, 0.8) !default;

// Input
$input-bg: transparent !default;
$input-focus-border-color: $o-brand-primary !default;
$form-check-input-checked-color: $o-brand-lightsecondary !default;
$form-check-input-checked-border-color: $o-brand-primary !default;
$form-check-input-checked-bg-color: $o-brand-primary !default;
$form-switch-checked-color: $o-white !default;

$form-range-thumb-bg: $primary !default;

// Badge
$badge-color: inherit !default;
$badge-padding-y: 0.25em !default;
$badge-padding-x: 0.4em !default;

// Placeholder color 
$input-placeholder-color: $gray-600 !default;

// Card

$card-spacer-y: $spacer !default; // BS Default
$card-cap-padding-y: $card-spacer-y !default;
