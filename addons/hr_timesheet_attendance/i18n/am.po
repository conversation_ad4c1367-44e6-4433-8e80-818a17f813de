# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet_attendance
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Language-Team: Amharic (https://app.transifex.com/odoo/teams/41243/am/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: am\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__attendance_cost
msgid "Attendance Cost"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__total_attendance
msgid "Attendance Hours"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__company_id
msgid "Company"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.actions.act_window,help:hr_timesheet_attendance.action_hr_timesheet_attendance_report
msgid "Compare the time recorded by your employees with their attendance."
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__cost_difference
msgid "Cost Difference"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__date
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "Date"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__display_name
msgid "Display Name"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "Employee"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__total_difference
msgid "Hours Difference"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__id
msgid "ID"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report____last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model,name:hr_timesheet_attendance.model_ir_ui_menu
msgid "Menu"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.actions.act_window,help:hr_timesheet_attendance.action_hr_timesheet_attendance_report
msgid "No data yet!"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
msgid "Sum of Total Attendance"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
msgid "Sum of Total Difference"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
msgid "Sum of Total Timesheet"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.actions.act_window,name:hr_timesheet_attendance.action_hr_timesheet_attendance_report
#: model:ir.ui.menu,name:hr_timesheet_attendance.menu_hr_timesheet_attendance_report
msgid "Timesheet / Attendance"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "Timesheet Attendance"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model,name:hr_timesheet_attendance.model_hr_timesheet_attendance_report
msgid "Timesheet Attendance Report"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__timesheets_cost
msgid "Timesheet Cost"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__total_timesheet
msgid "Timesheets Hours"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__user_id
msgid "User"
msgstr ""
