# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_outlook
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:27+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connect your Outlook account"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        قم بتوصيل حساب Outlook الخاص بك "

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"
msgstr "<i class=\"fa fa-cog\" title=\"تحرير الإعدادات \"/>"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('server_type', '!=', 'outlook'), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge text-bg-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('server_type', '!=', 'outlook'), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge text-bg-success\">\n"
"                        رمز Outlook صالح\n"
"                    </span>"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('smtp_authentication', '!=', 'outlook'), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge text-bg-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('smtp_authentication', '!=', 'outlook'), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge text-bg-success\">\n"
"                        رمز Outlook صالح\n"
"                    </span>"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "An error occurred when fetching the access token. %s"
msgstr "وقع خطأ أثناء جلب رمز الوصول. %s "

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__smtp_authentication
msgid "Authenticate with"
msgstr "المصادقة مع "

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "Authentication URI"
msgstr "رابط المصادقة "

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Connect your Outlook account with the OAuth Authentication process.  \n"
"By default, only a user with a matching email address will be able to use this server. To extend its use, you should set a \"mail.default.from\" system parameter."
msgstr ""
"قم بتوصيل حساب Outlook الخاص بك مع عملية مصادقة OAuth. \n"
"افتراضياً، سيُسمح فقط للمستخدم الذي يملك عنوان البريد الإلكتروني المطابق استخدام هذا الخادم. لزيادة إمكانية استخدامه، عليك تعيين معيار نظام \"mail.default.from\". "

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/fetchmail_server.py:0
#, python-format
msgid ""
"Connect your personal Outlook account using OAuth. \n"
"You will be redirected to the Outlook login page to accept the permissions."
msgstr ""
"قم بتوصيل حساب Outlook الشخصي باستخدام OAuth. \n"
"ستتم إعادة توجيهك إلى صفحة تسجيل الدخول إلى Outlook لقبول الأذونات. "

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.microsoft_outlook_oauth_error
msgid "Go back to your mail server"
msgstr "العودة إلى خادم بريدك "

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "ID"
msgstr "المُعرف"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "ID of your Outlook app"
msgstr "معرّف تطبيق Outlook "

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "خادم البريد الوارد "

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Incorrect Connection Security for Outlook mail server %r. Please set it to "
"\"TLS (STARTTLS)\"."
msgstr ""
"أمان الاتصال غير صالح لخادم بريد Outlook الإلكتروني %r. يرجى تعيينه لـ \"TLS"
" (STARTTLS)\". "

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__is_microsoft_outlook_configured
msgid "Is Outlook Credential Configured"
msgstr "تمت تهيئة بيانات اعتماد Outlook "

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_ir_mail_server
msgid "Mail Server"
msgstr "خادم البريد الإلكتروني "

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_microsoft_outlook_mixin
msgid "Microsoft Outlook Mixin"
msgstr "مجموعة مخصصات Microsoft Outlook "

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Only the administrator can link an Outlook mail server."
msgstr "وحده المدير بوسعه ربط خادم بريد Outlook. "

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token
msgid "Outlook Access Token"
msgstr "رمز وصول Outlook "

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token_expiration
msgid "Outlook Access Token Expiration Timestamp"
msgstr "الطابع الزمني لانتهاء صلاحية رمز وصول Outlook "

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_identifier
msgid "Outlook Client Id"
msgstr "معرف عميل Outlook "

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_secret
msgid "Outlook Client Secret"
msgstr "سر عميل Outlook "

#. module: microsoft_outlook
#: model:ir.model.fields.selection,name:microsoft_outlook.selection__fetchmail_server__server_type__outlook
#: model:ir.model.fields.selection,name:microsoft_outlook.selection__ir_mail_server__smtp_authentication__outlook
msgid "Outlook OAuth Authentication"
msgstr "مصادقة Outlook OAuth "

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_refresh_token
msgid "Outlook Refresh Token"
msgstr "رمز تحديث Outlook "

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please configure your Outlook credentials."
msgstr "يرجى تهيئة بيانات اعتماد Otlook الخاصة بك. "

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please connect with your Outlook account before using it."
msgstr "يرجى الاتصال بحساب Outlook الخاص بك قبل استخدامه. "

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please fill the \"Username\" field with your Outlook/Office365 username "
"(your email address). This should be the same account as the one used for "
"the Outlook OAuthentication Token."
msgstr ""
"يرجى تعبئة حقل \"اسم المستخدم\" باسم المستخدم من حساب Outlook/Office365الخاص"
" بك (عنوان البريد الإلكتروني). يجب أن يكون ذلك نفس الحساب المستخدَم لرمز "
"مصادقة Outlook. "

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please leave the password field empty for Outlook mail server %r. The OAuth "
"process does not require it"
msgstr ""
"يرجى ترك حقل كلمة السر فارغاً لخادم بريد Outlook الإلكتروني %r. لا تتطلب "
"عملية المصادقة ذلك "

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "Read More"
msgstr "قراءة المزيد"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/fetchmail_server.py:0
#, python-format
msgid "SSL is required for the server %r."
msgstr "SSL مطلوب للخادم %r. "

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Secret"
msgstr "سر"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Secret of your Outlook app"
msgstr "سر تطبيق Outlook "

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "نوع الخادم "

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"Setup your Outlook API credentials in the general settings to link a Outlook"
" account."
msgstr ""
"قم بضبط بيانات اعتماد الواجهة البرمجية للتطبيق لـ Outlook في الإعدادات "
"العامة لربط حساب Outlook. "

#. module: microsoft_outlook
#: model:ir.model.fields,help:microsoft_outlook.field_fetchmail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "The URL to generate the authorization code from Outlook"
msgstr "رابط URL لإنشاء رمز المصادقة من Outlook "

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Unknown error."
msgstr "خطأ غير معروف. "
