# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_pos_hr
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-29 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Average order"
msgstr "Orden promedio"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Current"
msgstr "Actual"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Date"
msgstr "Fecha"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Employee"
msgstr "Empleado"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Order"
msgstr "Orden"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Orders"
msgstr "Órdenes"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Orders by Month"
msgstr "Órdenes por mes"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Orders by Total"
msgstr "Órdenes por total"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Periodo"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Point of Sale"
msgstr "Punto de venta"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Point of Sale Analysis by Point of Sale"
msgstr "Análisis de Punto de venta por punto de venta"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Point of Sale Analysis by Product"
msgstr "Análisis de Punto de venta por producto"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Point of Sale Analysis by Session"
msgstr "Análisis de Punto de venta por sesión"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Point of Sale Analysis by User"
msgstr "Análisis de Punto de venta por usuario"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Previous"
msgstr "Anterior"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Producto"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Responsible"
msgstr "Responsable"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Revenue"
msgstr "Ingresos"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Session"
msgstr "Sesión"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Sessions"
msgstr "Sesiones"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Top Orders"
msgstr "Órdenes principales"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Top Points of Sale"
msgstr "Puntos de venta principales"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Top Products"
msgstr "Productos principales"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Top Responsibles"
msgstr "Responsables principales"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Top Sessions"
msgstr "Sesiones principales"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "since last period"
msgstr "desde el último periodo"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "stats - current"
msgstr "estadísticas - actuales"

#. module: spreadsheet_dashboard_pos_hr
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_pos_hr/data/files/pos_dashboard.json:0
#, python-format
msgid "stats - previous"
msgstr "estadísticas - anteriores"
