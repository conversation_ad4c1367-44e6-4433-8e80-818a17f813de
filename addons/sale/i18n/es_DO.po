# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale
#
# Translators:
# <PERSON> <crodrig<PERSON><EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015-2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-06-21 16:52+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"\n"
"% set access_action = object.get_access_action()\n"
"% set doc_name = 'quotation' if object.state in ('draft', 'sent') else 'order confirmation'\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions.act_url'\n"
"% set access_name = is_online and object.template_id and 'Accept and pay %s online' % doc_name or 'View %s' % doc_name\n"
"% set access_url = is_online and access_action['url'] or object.get_signup_url()\n"
"\n"
"<p>Dear\n"
"% if object.partner_id.is_company and object.child_ids:\n"
"    ${object.partner_id.child_ids[0].name}\n"
"% else :\n"
"    ${object.partner_id.name}\n"
"% endif\n"
",</p>\n"
"<p>Thank you for your inquiry.<br />\n"
"Here is your ${doc_name} <strong>${object.name}</strong>\n"
"% if object.origin:\n"
"(with reference: ${object.origin} )\n"
"% endif\n"
"amounting <strong>${object.amount_total} ${object.pricelist_id.currency_id.name}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"<p style=\"margin-left: 30px; margin-top: 10 px; margin-bottom: 10px;\">\n"
"    <a href=\"${access_url}\" style=\"padding: 5px 10px; font-size: 12px; line-height: 18px; color: #FFFFFF; border-color:#a24689; text-decoration: none; display: inline-block; margin-bottom: 0px; font-weight: 400; text-align: center; vertical-align: middle; cursor: pointer; white-space: nowrap; background-image: none; background-color: #a24689; border: 1px solid #a24689; border-radius:3px\" class=\"o_default_snippet_text\">${access_name}</a>\n"
"</p>\n"
"<p>If you have any question, do not hesitate to contact us.</p>\n"
"<p>Best regards,</p>\n"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_sales_count
#: model:ir.model.fields,field_description:sale.field_product_template_sales_count
msgid "# Sales"
msgstr "Nº ventas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_count
msgid "# of Invoices"
msgstr "Nº de facturas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_nbr
msgid "# of Lines"
msgstr "# de líneas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_count
msgid "# of Orders"
msgstr "Nº de pedido de venta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_product_uom_qty
msgid "# of Qty"
msgstr "Nº de ctdad"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner_sale_order_count
msgid "# of Sales Order"
msgstr "Nº de pedido de venta"

#. module: sale
#: model:mail.template,report_name:sale.email_template_edi_sale
msgid ""
"${(object.name or '').replace('/','_')}_${object.state == 'draft' and "
"'draft' or ''}"
msgstr ""
"${(object.name or '').replace('/','_')}_${object.state == 'draft' and "
"'borrador' or ''}"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
#, fuzzy
msgid ""
"${object.company_id.name} ${object.state in ('draft', 'sent') and "
"'Quotation' or 'Order'} (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.company_id.name|safe} ${object.state in ('draft', 'sent') and "
"'Presupuesto' or 'Pedido'} (Ref ${object.name or 'n/a' })"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "(update)"
msgstr "(actualizar)"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"<i>Example: pre-paid service offers for which the customer have\n"
"                to buy an extra pack of hours, because he used all his support\n"
"                hours.</i>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Posición fiscal</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Payment Term:</strong>"
msgstr "<strong>Término de Pago:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>Vendedor:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Dirección de Envío</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Total Without Taxes</strong>"
msgstr "<strong>Total base</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong>"
msgstr "<strong>Su Referencia:</strong>"

#. module: sale
#: selection:sale.config.settings,sale_pricelist_setting:0
msgid "A single sale price per product"
msgstr "Un único precio de venta por producto"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_deposit_account_id
msgid "Account used for deposits"
msgstr "Cuenta utilizada para depósitos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_sale_delivery_address
msgid "Addresses"
msgstr "Direcciones"

#. module: sale
#: model:res.groups,name:sale.group_delivery_invoice_address
msgid "Addresses in Sales Orders"
msgstr "Direcciones en los pedidos de venta"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:139
#, python-format
msgid "Advance: %s"
msgstr "Anticipo: %s"

#. module: sale
#: selection:sale.config.settings,sale_pricelist_setting:0
msgid "Advanced pricing based on formula"
msgstr "Precio avanzado basado en fórmula"

#. module: sale
#: selection:sale.config.settings,group_discount_per_so_line:0
msgid "Allow discounts on sales order lines"
msgstr "Permite establecer un descuento en las líneas de los pedidos de venta"

#. module: sale
#: selection:sale.config.settings,auto_done_setting:0
msgid ""
"Allow to edit sales order from the 'Sales Order' menu (not from the "
"Quotation menu)"
msgstr ""
"Permitir editar pedidos de ventas desde el menú 'Órdenes de Ventas' (no "
"desde el menú de Presupuestos)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_sale_pricelist
msgid ""
"Allows to manage different prices based on rules per category of customers.\n"
"                    Example: 10% for retailers, promotion of 5 EUR on this product, etc."
msgstr ""
"Permite gestionar diferentes precios basados en reglas por categoría de proveedor.\n"
"Ejemplo: 10% para minoristas, promoción de 5 EUR en este producto, etc."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_uom
msgid ""
"Allows you to select and maintain different units of measure for products."
msgstr ""
"Permite seleccionar y configurar diferentes unidades de medida para los "
"productos."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_sales_to_invoice_amount
msgid "Amount of sales to invoice"
msgstr "Importe de ventas para facturar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_project_id
#: model:ir.model.fields,field_description:sale.field_sale_report_analytic_account_id
msgid "Analytic Account"
msgstr "Cuenta analítica"

#. module: sale
#: model:res.groups,name:sale.group_analytic_accounting
msgid "Analytic Accounting for Sales"
msgstr "Contabilidad analítica para las ventas"

#. module: sale
#: model:ir.filters,name:sale.filter_isale_report_product
msgid "By Product"
msgstr "Por producto"

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_salespersons
msgid "By Salespersons"
msgstr "Por vendedor"

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_salesteam
msgid "By Salesteam"
msgstr "Por vendedor"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_delivered_updateable
msgid "Can Edit Delivered"
msgstr "Puede Modificar Entregado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Cancelar"

#. module: sale
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Cancelled"
msgstr "Cancelada"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_use_invoices
msgid "Check this box to manage invoices in this sales team."
msgstr "Marcar esta casilla para gestionar facturas en este equipo de ventas."

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_use_quotations
msgid "Check this box to manage quotations in this sales team."
msgstr ""
"Marque esta casilla para gestionar cotizaciones en este equipo de ventas."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Click to define a team target"
msgstr "Haga clic aquí para definir una meta por equipo."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_commercial_partner_id
msgid "Commercial Entity"
msgstr "Entidad comercial"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_company_id
#: model:ir.model.fields,field_description:sale.field_sale_report_company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Compañía"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm Sale"
msgstr "Confirmar venta"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Create Invoice"
msgstr "Crear factura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Invoices"
msgstr "Crear facturas"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid "Create a Quotation, the first step of a new sale."
msgstr "Crea un Presupuesto, el primer paso de una nueva venta."

#. module: sale
#: selection:product.template,track_service:0
msgid "Create a task and track hours"
msgstr "Crear una tarea y da seguimiento a las horas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create and View Invoices"
msgstr "Crear y ver facturas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line_create_date
msgid "Created on"
msgstr "Creado en"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_create_date
msgid "Creation Date"
msgstr "Fecha creación"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_currency_id
msgid "Currency"
msgstr "Moneda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Cliente"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_client_order_ref
msgid "Customer Reference"
msgstr "Referencia cliente"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_deposit_taxes_id
msgid "Customer Taxes"
msgstr "Impuestos cliente"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Customer portal"
msgstr "Portal del Cliente"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_date
msgid "Date Order"
msgstr "Fecha pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Date Ordered:"
msgstr "Fecha pedido:"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_create_date
msgid "Date on which sales order is created."
msgstr "Fecha en la que se crea el pedido de venta."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_default_invoice_policy
msgid "Default Invoicing"
msgstr "Facturación por Defecto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company_sale_note
msgid "Default Terms and Conditions"
msgstr "Plazo y condiciones por defecto"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_deposit_product_id_setting
msgid "Default product used for payment advances"
msgstr "Producto usado por defecto para pago de anticipos."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_company_inherit_form2
msgid "Default terms & conditions..."
msgstr "Plazo y condiciones por defecto..."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_delivered
msgid "Delivered"
msgstr "Entregado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered Quantity"
msgstr "Cantidad Entregada"

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Delivered quantities"
msgstr "Cantidades entregadas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_shipping_id
msgid "Delivery Address"
msgstr "Dirección de entrega"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_customer_lead
msgid "Delivery Lead Time"
msgstr "Tiempo inicial entrega"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_partner_shipping_id
msgid "Delivery address for current sales order."
msgstr "Dirección de envío para el pedido de ventas actual."

#. module: sale
#: model:product.product,name:sale.advance_product_0
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "Depósito"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_deposit_product_id_setting
msgid "Deposit Product"
msgstr "Producto de Depósito"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Description"
msgstr "Descripción"

#. module: sale
#: selection:sale.config.settings,sale_pricelist_setting:0
msgid "Different prices per customer segment"
msgstr "Diferentes precios por segmento de clientes"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#, fuzzy
msgid "Disc.(%)"
msgstr "Descuento (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_discount_per_so_line
msgid "Discount"
msgstr "Descuento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_discount
msgid "Discount (%)"
msgstr "Descuento (%)"

#. module: sale
#: model:res.groups,name:sale.group_discount_per_so_line
msgid "Discount on lines"
msgstr "Descuentos en líneas"

#. module: sale
#: selection:sale.config.settings,group_sale_delivery_address:0
msgid ""
"Display 3 fields on sales orders: customer, invoice address, delivery "
"address"
msgstr ""
"Mostrar 3 campos en los pedidos de venta: cliente, dirección de factura, "
"dirección de envío"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line_display_name
#: model:ir.model.fields,field_description:sale.field_sale_report_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: sale
#: model:res.groups,name:sale.group_display_incoterm
msgid "Display incoterms on Sales Order and related invoices"
msgstr "Mostrar incoterms en los Pedidos de Ventas y en facturas relacionadas"

#. module: sale
#: selection:sale.config.settings,module_sale_margin:0
msgid "Display margins on quotations and sales orders"
msgstr "Mostrar márgenes en cotizaciones y pedidos de venta"

#. module: sale
#: selection:sale.config.settings,module_sale_layout:0
#, fuzzy
msgid "Do not personalize sale orders and invoice reports"
msgstr "Mostrar incoterms en los pedidos de ventas y en facturas"

#. module: sale
#: selection:sale.order,state:0
msgid "Done"
msgstr "Realizado"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:83
#, python-format
msgid "Down Payment"
msgstr "Depósito"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_amount
msgid "Down Payment Amount"
msgstr "Cantidad del Depósito"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_product_id
msgid "Down Payment Product"
msgstr "Producto de Depósito"

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Down payment (fixed amount)"
msgstr "Depósito (cantidad fija)"

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Down payment (percentage)"
msgstr "Depósito (porcentaje)"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:80
#, python-format
msgid "Down payment of %s%%"
msgstr "Depósito de %s%%"

#. module: sale
#: selection:sale.report,state:0
msgid "Draft Quotation"
msgstr "Cotización borrador"

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Asistente de redacción de correo electrónico."

#. module: sale
#: model:ir.model,name:sale.model_survey_mail_compose_message
#, fuzzy
msgid "Email composition wizard for Survey"
msgstr "Asistente de redacción de correo electrónico."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_website_portal
msgid "Enable customer portal to track orders, delivery and invoices"
msgstr ""
"Habilitar el portal del cliente para dar seguimiento a pedidos, entregas y "
"facturas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_validity_date
msgid "Expiration Date"
msgstr "Fecha de Expiración"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Filtro avanzado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_fiscal_position_id
msgid "Fiscal Position"
msgstr "Posición fiscal"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_sale_pricelist_setting
msgid ""
"Fix Price: all price manage from products sale price.\n"
"Different prices per Customer: you can assign price on buying of minimum quantity in products sale tab.\n"
"Advanced pricing based on formula: You can have all the rights on pricelist"
msgstr ""
"Precio fijo: todos los precios se gestionan desde el precio de venta del producto.\n"
"Diferentes precios por Cliente: Puedes asignar precios comprando una cantidad minima en la pestaña de ventas de los productos.\n"
"Precio avanzado en base a una fórmula: Usted puede tener todos los derechos sobre la lista de precios "

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Desde este reporte, puede obtener una vista general de la cantidad facturada"
" a su cliente. La herramienta de búsqueda también puede ser usada para "
"personalizar su reporte de Facturas y de esa forma, alinearse a sus "
"necesidades."

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Fully Invoiced"
msgstr "Facturado"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_weight
msgid "Gross Weight"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_id
#: model:ir.model.fields,field_description:sale.field_sale_order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_sale_report_id
msgid "ID"
msgstr "ID (identificación)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"If a sale order is done, you cannot modify it manually anymore. However, you"
" will still be able to invoice or deliver. This is used to freeze the sale "
"order."
msgstr ""
"Si un pedido de venta se encuentra realizado, usted ya no puede modificarlo manualmente.\n"
"Sin embargo, si puede facturarlo y entregarlo. Esto se utiliza para congelar el pedido de venta."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_deposit_account_id
msgid "Income Account"
msgstr "Cuenta de ingresos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_display_incoterm
msgid "Incoterms"
msgstr "Incoterms"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice
msgid "Invoice"
msgstr "Factura"

#. module: sale
#: code:addons/sale/sale.py:840
#, python-format
msgid "Invoice %s paid"
msgstr "Factura %s pagada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_invoice_id
msgid "Invoice Address"
msgstr "Dirección de factura"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Factura Confirmada"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Factura creada"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_line
msgid "Invoice Line"
msgstr "Línea factura"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_invoice_lines
msgid "Invoice Lines"
msgstr "Líneas de factura"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Invoice Order"
msgstr "Orden de facturación"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "Facturar pedido de venta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line_invoice_status
msgid "Invoice Status"
msgstr "Estado factura"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_invoiced_target
msgid "Invoice Target"
msgstr "Objetivo de facturación"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_partner_invoice_id
msgid "Invoice address for current sales order."
msgstr "Dirección de facturación para el pedido de venta actual."

#. module: sale
#: selection:sale.config.settings,default_invoice_policy:0
msgid "Invoice based on costs (time and material, expenses)"
msgstr "Factura basada en costos (tiempo y materiales, gastos)"

#. module: sale
#: selection:sale.config.settings,default_invoice_policy:0
msgid "Invoice delivered quantities"
msgstr "Facturar cantidades entregadas"

#. module: sale
#: selection:sale.config.settings,default_invoice_policy:0
msgid "Invoice ordered quantities"
msgstr "Facturar cantidades ordenadas"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales team has"
" invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"Previsión de los ingresos por facturación del mes actual. Este es el monto "
"que el equipo de ventas ha facturado este mes. Se usa para calcular el ratio"
" de progresión del ingreso objetivo y actual en la vista kanban."

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Invoiceable lines"
msgstr "Líneas de factura"

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Invoiceable lines (deduct down payments)"
msgstr "Líneas facturables (deducir el pago inicial)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_invoiced
msgid "Invoiced"
msgstr "Facturado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced Quantity"
msgstr "Cantidad Facturada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_invoiced
msgid "Invoiced This Month"
msgstr "Facturado este mes"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_crm_team_use_invoices
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Facturas"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Análisis de facturas"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Estadísticas de facturas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"Invoices will be created in draft so that you can update\n"
"                        them before validation."
msgstr ""
"Las Facturas se crearán en borrador para que pueda actualizarlas\n"
"antes de su validación"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "Facturar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template_invoice_policy
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_invoice_policy
msgid "Invoicing Policy"
msgstr "Política de Facturación"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing address:"
msgstr "Dirección de facturación:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing and shipping address:"
msgstr "Dirección de facturación y de envío:"

#. module: sale
#: selection:sale.config.settings,group_sale_delivery_address:0
msgid ""
"Invoicing and shipping addresses are always the same (Example: services "
"companies)"
msgstr ""
"La dirección de facturación y envío es siempre la misma (Ejemplo: compañías "
"de servicios)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "Invoicing/Progression Ratio"
msgstr "Facturación/progresión de la relación"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv___last_update
#: model:ir.model.fields,field_description:sale.field_sale_order___last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_line___last_update
#: model:ir.model.fields,field_description:sale.field_sale_report___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line_write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line_write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_sale_contract
msgid "Manage subscriptions and recurring invoicing"
msgstr "Gestionar suscripciones y facturas recurrentes"

#. module: sale
#: selection:product.template,track_service:0
msgid "Manually set quantities on order"
msgstr "Establecer cantidades manualmente en pedidos"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product_track_service
#: model:ir.model.fields,help:sale.field_product_template_track_service
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sale order validation and track the work hours."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_validity_date
msgid ""
"Manually set the expiration date of your quotation (offer), or it will set "
"the date automatically based on the template if online quotation is "
"installed."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_sale_margin
msgid "Margins"
msgstr "Márgenes"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "Mis pedidos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "Mis lineas de pedidos de venta"

#. module: sale
#: selection:sale.config.settings,auto_done_setting:0
msgid "Never allow to modify a confirmed sale order"
msgstr "Nunca permitir modificar un pedido de venta confirmado"

#. module: sale
#: code:addons/sale/sale.py:101
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "Nueva cotización"

#. module: sale
#: selection:sale.config.settings,group_discount_per_so_line:0
msgid "No discount on sales order lines, global discount only"
msgstr "Sin descuentos en las líneas de pedido de ventas, solo "

#. module: sale
#: selection:sale.config.settings,group_display_incoterm:0
msgid "No incoterm on reports"
msgstr "No incoterm en reportes"

#. module: sale
#: selection:sale.config.settings,group_product_variant:0
msgid "No variants on products"
msgstr "Sin variantes en productos"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Note that once a Quotation becomes a Sale Order, it will be moved \n"
"                from the Quotations list to the Sales Order list."
msgstr ""
"Tenga en cuenta que una vez que una cotización se convierte en una orden de "
"venta, se moverán desde la lista de cotizaciones a la lista de orden de "
"ventas."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
msgid ""
"Note that once a Quotation becomes a Sale Order, it will be moved from the "
"Quotations list to the Sales Order list."
msgstr ""
"Tenga en cuenta que una vez que una cotización se convierte en una orden de "
"venta, se moverán desde la lista de cotizaciones a la lista de orden de "
"ventas."

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Nothing to Invoice"
msgstr "Nada que Facturar"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line_customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Número de días entre la confirmación del pedido y la entrega de los "
"productos al cliente"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.\n"
"                    You'll be able to invoice it and collect payments.\n"
"                    From the <i>Sales Orders</i> menu, you can track delivery\n"
"                    orders or services."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_website_quote
msgid "Online Quotations"
msgstr "Cotizaciones en Línea"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:26
#, python-format
msgid "Only Integer Value should be valid."
msgstr "El único valor válido es un entero."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "Pedido #"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_date_order
msgid "Order Date"
msgstr "Fecha orden"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Líneas del pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Order Month"
msgstr "Mes del pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
msgid "Order Number"
msgstr "Número de pedido"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_name
msgid "Order Reference"
msgstr "Referencia del pedido"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_state
msgid "Order Status"
msgstr "Estado del pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Ordered Qty"
msgstr "Ctdad pedida"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Ordered Quantity"
msgstr "Cantidad Pedida"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product_invoice_policy
#: model:ir.model.fields,help:sale.field_product_template_invoice_policy
msgid ""
"Ordered Quantity: Invoice based on the quantity the customer ordered.\n"
"Delivered Quantity: Invoiced based on the quantity the vendor delivered.\n"
"Reinvoice Costs: Invoice with some additional charges (product transfer, labour charges,...)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Ordered date of the sales order"
msgstr "Fecha ordenada de los pedidos de venta"

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Ordered quantities"
msgstr "Cantidades pedidas"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "Pedidos para aumentar las ventas"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"Orders to upsell are orders having products with an invoicing\n"
"                policy based on <i>ordered quantities</i> for which you have\n"
"                delivered more than what have been ordered."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Information"
msgstr "Otra información"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
#: model:ir.model.fields,field_description:sale.field_sale_report_partner_id
msgid "Partner"
msgstr "Empresa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_country_id
msgid "Partner Country"
msgstr "Socio País"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Partner's Country"
msgstr "Socio País"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_payment_term_id
msgid "Payment Term"
msgstr "Plazo de pago"

#. module: sale
#: selection:sale.config.settings,module_sale_layout:0
msgid ""
"Personnalize the sale orders and invoice report with separators, page-breaks"
" or subtotals"
msgstr ""

#. module: sale
#: code:addons/sale/sale.py:246
#, python-format
msgid "Please define an accounting sale journal for this company."
msgstr "Defina por favor un diario de ventas para esta compañía."

#. module: sale
#: code:addons/sale/sale.py:703
#, python-format
msgid ""
"Please define income account for this product: \"%s\" (id:%d) - or for its "
"category: \"%s\"."
msgstr ""
"Por favor definir cuenta de ingreso para este producto: “%s” (id: %d) - o "
"para su categoría: “%s”."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Price"
msgstr "Precio"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_reduce
msgid "Price Reduce"
msgstr "Reducción de precio"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report_pricelist_id
msgid "Pricelist"
msgstr "Tarifa"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_pricelist_id
msgid "Pricelist for current sales order."
msgstr "Tarifa para el pedido de venta actual."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Print"
msgstr "Imprimir"

#. module: sale
#: selection:sale.config.settings,module_website_quote:0
msgid "Print quotes or send by email"
msgstr "Imprimir cotización o enviar por correo"

#. module: sale
#: model:ir.model,name:sale.model_procurement_order
msgid "Procurement"
msgstr "Abastecimiento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_procurement_group_id
msgid "Procurement Group"
msgstr "Grupo de abastecimiento"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_procurement_ids
msgid "Procurements"
msgstr "Abastecimientos"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_id
#: model:ir.model.fields,field_description:sale.field_sale_order_product_id
#: model:ir.model.fields,field_description:sale.field_sale_report_product_id
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Producto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Categoría de producto"

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_sale_report_product_tmpl_id
msgid "Product Template"
msgstr "Plantilla producto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_product_variant
msgid "Product Variants"
msgstr "Variantes de Producto"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Products"
msgstr "Productos"

#. module: sale
#: selection:sale.config.settings,group_product_variant:0
msgid ""
"Products can have several attributes, defining variants (Example: size, "
"color,...)"
msgstr ""
"Los productos pueden tener diversos atributos, definiendo variantes "
"(Ejemplo: tamaño, color,...)"

#. module: sale
#: selection:sale.config.settings,group_uom:0
msgid "Products have only one unit of measure (easier)"
msgstr "Los productos tienen una sola unidad de medida (más fácil)"

#. module: sale
#: model:res.groups,name:sale.group_mrp_properties
msgid "Properties on lines"
msgstr "Propiedades en las líneas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "Ctdad"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_delivered
msgid "Qty Delivered"
msgstr "Ctdad Enviada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_invoiced
msgid "Qty Invoiced"
msgstr "Ctdad Facturada"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_to_invoice
msgid "Qty To Invoice"
msgstr "Ctdad para Facturar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quantity"
msgstr "Cantidad"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
#: selection:sale.order,state:0
msgid "Quotation"
msgstr "Cotización"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "Cotización #"

#. module: sale
#: model:ir.actions.report.xml,name:sale.report_sale_order
msgid "Quotation / Order"
msgstr "Cotización / Pedido"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation Date:"
msgstr "Fecha de Cotización:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotation Number"
msgstr "Número de Cotización"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation Send"
msgstr "Cotización enviada"

#. module: sale
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Quotation Sent"
msgstr "Cotización enviada"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Cotización confirmada"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
msgid "Quotation sent"
msgstr "Cotización enviada"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.model.fields,field_description:sale.field_crm_team_use_quotations
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Quotations"
msgstr "Cotizaciones"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Quotations & Sales"
msgstr "Cotizaciones y pedidos"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Análisis de Cotizaciones"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#, fuzzy
msgid "Quotations Sent"
msgstr "Cotización enviada"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Cotizaciones y pedidos"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_origin
msgid "Reference of the document that generated this sales order request."
msgstr ""
"Referencia del documento que ha generado esta solicitud de pedido de venta."

#. module: sale
#: selection:product.template,invoice_policy:0
#, fuzzy
msgid "Reinvoice Costs"
msgstr "Estado factura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Reporting"
msgstr "Informes"

#. module: sale
#: selection:sale.order,state:0
msgid "Sale Order"
msgstr "Pedido de venta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line_so_line
#: model:ir.model.fields,field_description:sale.field_procurement_order_sale_line_id
msgid "Sale Order Line"
msgstr "Línea pedido de venta"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_product_sale_list
#: model:ir.model.fields,field_description:sale.field_account_invoice_line_sale_line_ids
msgid "Sale Order Lines"
msgstr "Líneas de pedido de venta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_auto_done_setting
msgid "Sale Order Modification"
msgstr "Modificación del Pedido de Venta"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Sale Price"
msgstr "Precio de venta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_sale_layout
msgid "Sale Reports Layout"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_report_product_all
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_company_inherit_form2
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales"
msgstr "Ventas"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Ventas. Anticipo pago factura"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales Analysis"
msgstr "Análisis de ventas"

#. module: sale
#: selection:sale.report,state:0
msgid "Sales Done"
msgstr "Orden de venta completa"

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_sales_funnel
msgid "Sales Funnel"
msgstr "Canal de ventas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales Information"
msgstr "Información de ventas"

#. module: sale
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner_sale_order_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model:res.request.link,name:sale.req_link_sale_order
#: selection:sale.report,state:0
msgid "Sales Order"
msgstr "Aviso para pedido de venta"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Pedido de venta confirmado"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea pedido de venta"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Líneas pedido de ventas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "Líneas de pedidos de venta listas para facturar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "Líneas de pedidos de ventas relacionadas con un pedido de venta mío"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Sales Order that haven't yet been confirmed"
msgstr "Pedidos de venta sin confirmar"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "Pedidos de ventas"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Orders Statistics"
msgstr "Estadísticas pedidos de venta"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_invoice_report_team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_team_id
#: model:ir.model.fields,field_description:sale.field_sale_order_team_id
#: model:ir.model.fields,field_description:sale.field_sale_report_team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "Equipo de ventas"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Sales to Invoice"
msgstr "Ventas a facturar"

#. module: sale
#: selection:sale.config.settings,module_sale_margin:0
msgid "Salespeople do not need to view margins when quoting"
msgstr "Los vendedores no necesitan ver márgenes cuando están cotizando"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_order_user_id
#: model:ir.model.fields,field_description:sale.field_sale_report_user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Vendedor"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Buscar pedido de venta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_website_sale_digital
msgid ""
"Sell digital products - provide downloadable content on your customer portal"
msgstr ""
"Vender productos digitales - proporcionar contenido descargaste en el portal"
" de cliente"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Enviar por correo electrónico"

#. module: sale
#: selection:sale.config.settings,module_website_quote:0
msgid "Send online quotations based on templates (advanced)"
msgstr "Enviar cotizaciones en línea basado en plantillas (avanzado)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:19
#, python-format
msgid "Set an invoicing target: "
msgstr "Establecer un objetivo de facturación:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Done"
msgstr "Cambiar a finalizado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Convertir a cotización"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Setup default terms and conditions in your company settings."
msgstr "Plazo y condiciones por defecto para las cotizaciones"

#. module: sale
#: selection:sale.config.settings,group_display_incoterm:0
msgid "Show incoterms on sale orders and invoices"
msgstr "Mostrar incoterms en los pedidos de ventas y en facturas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_product_pricelist
msgid "Show pricelists On Products"
msgstr "Mostrar tarifas en productos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_pricelist_item
msgid "Show pricelists to customers"
msgstr "Mostrar tarifas a los clientes"

#. module: sale
#: selection:sale.config.settings,group_uom:0
#, fuzzy
msgid ""
"Some products may be sold/purchased in different units of measure (advanced)"
msgstr ""
"Algunos productos pueden ser vendidos/comprados en diferentes unidades de "
"medidas (avanzado)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_origin
msgid "Source Document"
msgstr "Documento origen"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_state
#: model:ir.model.fields,field_description:sale.field_sale_report_state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Estado"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Subscriptions"
msgstr "Suscripciones"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_subtotal
msgid "Subtotal"
msgstr "Subtotal"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_invoiced_target
msgid ""
"Target of invoice revenue for the current month. This is the amount the "
"sales team estimates to be able to invoice this month."
msgstr ""
"Destino de los ingresos de la factura del mes actual. Éste es el equipo de "
"las ventas las estimaciones para poder facturar este mes."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line_tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "Impuestos"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_deposit_taxes_id
msgid "Taxes used for deposits"
msgstr "Utilizado para los depósitos de impuestos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_note
msgid "Terms and conditions"
msgstr "Términos y condiciones"

#. module: sale
#: code:addons/sale/sale_analytic.py:56
#, python-format
msgid ""
"The Sale Order %s linked to the Analytic Account must be validated before "
"registering expenses."
msgstr ""
"La orden de venta %s vinculada a la cuenta analítica debe ser validada antes"
" de registrar los gastos."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_amount
msgid "The amount to be invoiced in advance, taxes excluded."
msgstr "La cantidad a ser facturado por adelantado, impuestos excluidos."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_project_id
msgid "The analytic account related to a sales order."
msgstr "La cuenta analítica relacionada con un pedido de venta."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_display_incoterm
msgid ""
"The printed reports will display the incoterms for the sale orders and the "
"related invoices"
msgstr ""
"Los informes impresos mostrará los incoterms para las órdenes de venta y las"
" correspondientes facturas"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:137
#, python-format
msgid ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."
msgstr ""
"El producto de un pago de factura debe ser del tipo ‘Servicio’. Por favor "
"utilice otro producto o actualización de este producto."

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:135
#, python-format
msgid ""
"The product used to invoice a down payment should have an invoice policy set"
" to \"Ordered quantities\". Please update your deposit product to be able to"
" create a deposit invoice."
msgstr ""
"El producto de un pago de factura debe tener una política de factura "
"establece en «Ordenadas cantidades». Por favor actualice su producto de "
"depósito para poder crear una factura de depósito."

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:77
#, python-format
msgid "The value of the down payment amount must be positive."
msgstr "El valor de la cantidad de pago inicial debe ser positivo."

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:73
#, python-format
msgid ""
"There is no income account defined for this product: \"%s\". You may have to"
" install a chart of account from Accounting app, settings menu."
msgstr ""
"No hay ninguna cuenta de ingresos definida para este producto: “%s”. Deberá "
"instalar un cuadro de cuenta de contabilidad de la aplicación, menú de "
"configuración."

#. module: sale
#: code:addons/sale/sale.py:330 code:addons/sale/sale.py:334
#, python-format
msgid "There is no invoicable line."
msgstr "No hay línea facturable"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "This Year"
msgstr "Este año"

#. module: sale
#: model:web.tip,description:sale.sale_tip_1
msgid ""
"This progress bar shows the stages your quotation will go through.\n"
"                Use buttons on the left to move forward to the next stages."
msgstr ""
"Esta barra de progreso muestra las etapas de su cotización atravesarán.\n"
"Utilice los botones de la izquierda para avanzar a las siguientes etapas."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"Este informe realiza un análisis de sus cotizaciones y pedidos de venta. El "
"análisis verifica los ingresos de sus ventas y las ordena por diferentes "
"grupos de criterios (vendedores, empresa, producto, etc.). Utilice este "
"informe para realizar un análisis sobre sus ventas todavía no facturadas. Si"
" desea analizar sus ingresos, debería utilizar el informe de análisis de "
"facturas en la aplicación de Contabilidad."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Este informe realiza un análisis sobre sus cotizaciones. El análisis "
"comprueba sus ingresos por ventas y los ordena por diferentes criterios de "
"agrupación (vendedores, empresa, producto, etc). Use este informe para "
"realizar un análisis de las ventas aún no facturadas. Si quiere analizar su "
"volumen de negocios, debe usar el informe de análisis de facturas en la "
"aplicación de Contabilidad"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Este informe realiza el análisis de sus pedidos de venta. El análisis "
"comprueba su ingresos por ventas y los ordena por diferentes criterios de "
"agrupación (vendedores, cliente, producto, etc). Use este informe para "
"analizar las ventas que aún no han sido facturadas. Si lo que quiere "
"analizar es su volumen de negocios, debería usar el informe de análisis de "
"facturas en la aplicación de Contabilidad."

#. module: sale
#: selection:product.template,track_service:0
#, fuzzy
msgid "Timesheets on project"
msgstr "Horas de contrato"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_to_invoice
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "To Invoice"
msgstr "Para facturar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_total
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Total"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_price_total
msgid "Total Price"
msgstr "Precio total"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Included"
msgstr "Total impuestos incluidos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_track_service
#: model:ir.model.fields,field_description:sale.field_product_template_track_service
msgid "Track Service"
msgstr "Servicio del servicio"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Unit Price"
msgstr "Precio unidad"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report_product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_uom
#, fuzzy
msgid "Units of Measure"
msgstr "Unidad de medida"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_untaxed
msgid "Untaxed Amount"
msgstr "Base imponible"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_price_subtotal
msgid "Untaxed Total Price"
msgstr "Precio Total sin Impuestos"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "Upselling"
msgstr "Ventas adicionales"

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Upselling Opportunity"
msgstr "Oportunidad de incrementar"

#. module: sale
#: model:web.tip,description:sale.sale_tip_2
msgid ""
"Use pivot and graph views to analyze your sales pipeline.\n"
"                Select measures, filter and group dimensions to get the perfect report according to your needs."
msgstr ""
"Utilizar las vistas de pivote y gráfico para analizar el flujo de ventas.\n"
"Seleccionar medidas, filtros y dimensiones de grupo para obtener el informe perfecto de acuerdo a sus necesidades."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_sale_pricelist
msgid "Use pricelists to adapt your price per customers"
msgstr "Usar tarifas para adaptar los precios a cada cliente"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "VAT:"
msgstr "RNC/Cédula:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_volume
msgid "Volume"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_advance_payment_method
msgid "What do you want to invoice?"
msgstr "¿Qué quiere facturar?"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""
"Trabaja con variante de producto le permite definir alguna variante de los "
"mismos productos , una facilidad de la gestión de productos en el comercio "
"electrónico , por ejemplo."

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:26
#, python-format
msgid "Wrong value entered!"
msgstr "Valor introducido no válido"

#. module: sale
#: code:addons/sale/sale.py:163
#, python-format
msgid ""
"You can not delete a sent quotation or a sales order! Try to cancel it "
"before."
msgstr ""

#. module: sale
#: code:addons/sale/sale.py:797
#, python-format
msgid ""
"You can not remove a sale order line.\n"
"Discard changes and try setting the quantity to 0."
msgstr ""
"No puede quitar una línea de orden de venta.\n"
"Descartar los cambios y tratar de establecer la cantidad a 0."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch, or check\n"
"                every order and invoice them one by one."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
#, fuzzy
msgid "You will find here all orders that are ready to be invoiced."
msgstr "Líneas de pedidos de venta listas para facturar"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Your next actions should flow efficiently: confirm the Quotation \n"
"                to a Sale Order, then create the Invoice and collect the Payment."
msgstr ""
"Sus próximas acciones deben fluir eficientemente: confirmar la cita a una "
"orden de venta, y luego crear la factura y cobrar el pago."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
msgid ""
"Your next actions should flow efficiently: confirm the Quotation to a Sale "
"Order, then create the Invoice and collect the Payment."
msgstr ""
"Sus próximas acciones deben fluir eficientemente: confirmar la cita a una "
"orden de venta, y luego crear la factura y cobrar el pago."

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
#, fuzzy
msgid "account analytic line"
msgstr "Línea Analítica"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "días"

#. module: sale
#: model:ir.model,name:sale.model_sale_config_settings
msgid "sale.config.settings"
msgstr "sale.config.settings"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_sale_pricelist_setting
msgid "unknown"
msgstr "desconocido"

#~ msgid ""
#~ "\n"
#~ "<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; \">\n"
#~ "\n"
#~ "    <p>Hello ${object.partner_id.name},</p>\n"
#~ "    \n"
#~ "    <p>Here is your ${object.state in ('draft', 'sent') and 'quotation' or 'order confirmation'} from ${object.company_id.name}: </p>\n"
#~ "\n"
#~ "    <p style=\"border-left: 1px solid #8e0000; margin-left: 30px;\">\n"
#~ "       &nbsp;&nbsp;<strong>REFERENCES</strong><br />\n"
#~ "       &nbsp;&nbsp;Order number: <strong>${object.name}</strong><br />\n"
#~ "       &nbsp;&nbsp;Order total: <strong>${object.amount_total} ${object.pricelist_id.currency_id.name}</strong><br />\n"
#~ "       &nbsp;&nbsp;Order date: ${object.date_order}<br />\n"
#~ "       % if object.origin:\n"
#~ "       &nbsp;&nbsp;Order reference: ${object.origin}<br />\n"
#~ "       % endif\n"
#~ "       % if object.client_order_ref:\n"
#~ "       &nbsp;&nbsp;Your reference: ${object.client_order_ref}<br />\n"
#~ "       % endif\n"
#~ "       % if object.user_id:\n"
#~ "       &nbsp;&nbsp;Your contact: <a href=\"mailto:${object.user_id.email or ''}?subject=Order%20${object.name}\">${object.user_id.name}</a>\n"
#~ "       % endif\n"
#~ "    </p>\n"
#~ "    <p>\n"
#~ "        You can view your quotation online:\n"
#~ "    </p>\n"
#~ "    <a style=\"display:block; width: 150px; height:20px; margin-left: 120px; color: #DDD; font-family: 'Lucida Grande', Helvetica, Arial, sans-serif; font-size: 13px; font-weight: bold; text-align: center; text-decoration: none !important; line-height: 1; padding: 5px 0px 0px 0px; background-color: #8E0000; border-radius: 5px 5px; background-repeat: repeat no-repeat;\"\n"
#~ "        href=\"/quote/${object.id}/${object.access_token}\">View ${object.state in ('draft', 'sent') and 'Quotation' or 'Order'}</a>\n"
#~ "\n"
#~ "    % if object.paypal_url:\n"
#~ "    <br/>\n"
#~ "    <p>It is also possible to directly pay with Paypal:</p>\n"
#~ "        <a style=\"margin-left: 120px;\" href=\"${object.paypal_url}\">\n"
#~ "            <img class=\"oe_edi_paypal_button\" src=\"/sale/static/img/btn_paynowcc_lg.gif\"/>\n"
#~ "        </a>\n"
#~ "    % endif\n"
#~ "\n"
#~ "    <br/>\n"
#~ "    <p>If you have any question, do not hesitate to contact us.</p>\n"
#~ "    <p>Thank you for choosing ${object.company_id.name or 'us'}!</p>\n"
#~ "    <br/>\n"
#~ "    <br/>\n"
#~ "    <div style=\"width: 375px; margin: 0px; padding: 0px; background-color: #8E0000; border-top-left-radius: 5px 5px; border-top-right-radius: 5px 5px; background-repeat: repeat no-repeat;\">\n"
#~ "        <h3 style=\"margin: 0px; padding: 2px 14px; font-size: 12px; color: #DDD;\">\n"
#~ "            <strong style=\"text-transform:uppercase;\">${object.company_id.name}</strong></h3>\n"
#~ "    </div>\n"
#~ "    <div style=\"width: 347px; margin: 0px; padding: 5px 14px; line-height: 16px; background-color: #F2F2F2;\">\n"
#~ "        <span style=\"color: #222; margin-bottom: 5px; display: block; \">\n"
#~ "            ${object.company_id.partner_id.sudo().with_context(show_address=True, html_format=True).name_get()[0][1] | safe}\n"
#~ "        </span>\n"
#~ "        % if object.company_id.phone:\n"
#~ "            <div style=\"margin-top: 0px; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; \">\n"
#~ "                Phone:&nbsp; ${object.company_id.phone}\n"
#~ "            </div>\n"
#~ "        % endif\n"
#~ "        % if object.company_id.website:\n"
#~ "            <div>\n"
#~ "                Web :&nbsp;<a href=\"${object.company_id.website}\">${object.company_id.website}</a>\n"
#~ "            </div>\n"
#~ "        %endif\n"
#~ "        <p></p>\n"
#~ "    </div>\n"
#~ "</div>\n"
#~ "            "
#~ msgstr ""
#~ "\n"
#~ "<div style=\"font-family: 'Lucida Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: #FFF; \">\n"
#~ "\n"
#~ "    <p>Hola ${object.partner_id.name},</p>\n"
#~ "    \n"
#~ "    <p>Aquí está su ${object.state in ('draft', 'sent') y o \"confirmación de la orden '' cotizada '} de ${object.company_id.name}: </p>\n"
#~ "\n"
#~ "    <p style=\"border-left: 1px solid #8e0000; margin-left: 30px;\">\n"
#~ "       &nbsp;&nbsp;<strong>REFERENCIA</strong><br />\n"
#~ "       &nbsp;&nbsp;Orden numero: <strong>${object.name}</strong><br />\n"
#~ "       &nbsp;&nbsp;Orden total: <strong>${object.amount_total} ${object.pricelist_id.currency_id.name}</strong><br />\n"
#~ "       &nbsp;&nbsp;Fecha de la orden: ${object.date_order}<br />\n"
#~ "       % if object.origin:\n"
#~ "       &nbsp;&nbsp;Referencia de la orden: ${object.origin}<br />\n"
#~ "       % endif\n"
#~ "       % if object.client_order_ref:\n"
#~ "       &nbsp;&nbsp;Su referencia: ${object.client_order_ref}<br />\n"
#~ "       % endif\n"
#~ "       % if object.user_id:\n"
#~ "       &nbsp;&nbsp;Su contacto: <a href=\"mailto:${object.user_id.email or ''}?subject=Order%20${object.name}\">${object.user_id.name}</a>\n"
#~ "       % endif\n"
#~ "    </p>\n"
#~ "    <p>\n"
#~ "        Puede ver su cotización en línea:\n"
#~ "    </p>\n"
#~ "    <a style=\"display:block; width: 150px; height:20px; margin-left: 120px; color: #DDD; font-family: 'Lucida Grande', Helvetica, Arial, sans-serif; font-size: 13px; font-weight: bold; text-align: center; text-decoration: none !important; line-height: 1; padding: 5px 0px 0px 0px; background-color: #8E0000; border-radius: 5px 5px; background-repeat: repeat no-repeat;\"\n"
#~ "        href=\"/quote/${object.id}/${object.access_token}\">Ver ${object.state in ('draft', 'sent') de la 'Cotizacion' o 'Orden'}</a>\n"
#~ "\n"
#~ "    % if object.paypal_url:\n"
#~ "    <br/>\n"
#~ "    <p>También es posible pagar directamente con PayPal:</p>\n"
#~ "        <a style=\"margin-left: 120px;\" href=\"${object.paypal_url}\">\n"
#~ "            <img class=\"oe_edi_paypal_button\" src=\"/sale/static/img/btn_paynowcc_lg.gif\"/>\n"
#~ "        </a>\n"
#~ "    % endif\n"
#~ "\n"
#~ "    <br/>\n"
#~ "    <p>Si usted tiene cualquier pregunta, no dude en contactar con nosotros.</p>\n"
#~ "    <p>Gracias por preferinos ${object.company_id.name or ''}!</p>\n"
#~ "    <br/>\n"
#~ "    <br/>\n"
#~ "    <div style=\"width: 375px; margin: 0px; padding: 0px; background-color: #8E0000; border-top-left-radius: 5px 5px; border-top-right-radius: 5px 5px; background-repeat: repeat no-repeat;\">\n"
#~ "        <h3 style=\"margin: 0px; padding: 2px 14px; font-size: 12px; color: #DDD;\">\n"
#~ "            <strong style=\"text-transform:uppercase;\">${object.company_id.name}</strong></h3>\n"
#~ "    </div>\n"
#~ "    <div style=\"width: 347px; margin: 0px; padding: 5px 14px; line-height: 16px; background-color: #F2F2F2;\">\n"
#~ "        <span style=\"color: #222; margin-bottom: 5px; display: block; \">\n"
#~ "            ${object.company_id.partner_id.sudo().with_context(show_address=True, html_format=True).name_get()[0][1] | safe}\n"
#~ "        </span>\n"
#~ "        % if object.company_id.phone:\n"
#~ "            <div style=\"margin-top: 0px; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; \">\n"
#~ "                Phone:&nbsp; ${object.company_id.phone}\n"
#~ "            </div>\n"
#~ "        % endif\n"
#~ "        % if object.company_id.website:\n"
#~ "            <div>\n"
#~ "                Web :&nbsp;<a href=\"${object.company_id.website}\">${object.company_id.website}</a>\n"
#~ "            </div>\n"
#~ "        %endif\n"
#~ "        <p></p>\n"
#~ "    </div>\n"
#~ "</div>\n"
#~ "            "

#~ msgid "<span groups=\"sale.group_discount_per_so_line\">Disc.(%)</span>"
#~ msgstr "<span groups=\"sale.group_discount_per_so_line\">Desc.(%)</span>"

#~ msgid "Action Needed"
#~ msgstr "Acción Requerida"

#~ msgid "Allows you to specify an analytic account on sales orders."
#~ msgstr "Permite especificar una cuenta analítica en pedidos de venta."

#~ msgid "Analytic accounting for sales"
#~ msgstr "Contabilidad analítica para ventas"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid "Expense Account"
#~ msgstr "Cuenta de gastos"

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Empresas)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención."

#~ msgid "Invoice based on time and material"
#~ msgstr "Factura basada en tiempo y materiales"

#~ msgid "Is Follower"
#~ msgstr "Es un seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del último mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Mensajes e historial de comunicación"

#~ msgid "Number of Actions"
#~ msgstr "Número de acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"

#~ msgid "Open Sale Menu"
#~ msgstr "Abrir menú de ventas"

#~ msgid ""
#~ "This account will be used for invoices instead of the default one to value "
#~ "expenses for the current product."
#~ msgstr ""
#~ "Esta cuenta se utilizará para las facturas en lugar de la opción por defecto"
#~ " para los gastos de valor para el producto actual."

#~ msgid ""
#~ "This account will be used for invoices instead of the default one to value "
#~ "sales for the current product."
#~ msgstr ""
#~ "Esta cuenta se utilizará para las facturas en lugar de la predeterminada "
#~ "para ventas de valor para el producto actual."

#~ msgid "Unit of Measures"
#~ msgstr "Unidad de medida"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de mensajes no leidos"

#~ msgid "Website Messages"
#~ msgstr "Mensajes del sitio web"

#~ msgid "Website communication history"
#~ msgstr "Historial de comunicaciones del sitio web"

#~ msgid "You can only delete draft quotations!"
#~ msgstr "Sólo puede eliminar cotizaciones en borrador!"

#~ msgid "account.config.settings"
#~ msgstr "Parámetros de configuración contable"
