# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * im_livechat
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-08-24 09:19+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "# Messages"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "# των Συνεδριών"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "# των ομιλητών"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% Ευχαριστημένοι"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Display the button' displays the chat button on the pages.\n"
"* 'Auto popup' displays the button and automatically open the conversation pane.\n"
"* 'Hide the button' hides the chat button on the pages."
msgstr ""
"* 'Εμφάνιση κουμπιού' εμφανίζει το κουμπί συνομιλίας στις σελίδες.\n"
"* Το 'Αυτόματα αναδυόμενο παράθυρο' εμφανίζει το κουμπί και ανοίγει αυτόματα το παράθυρο συνομιλίας.\n"
"* 'Απόκρυψη κουμπιού' κρύβει το κουμπί συζήτησης στις σελίδες."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"<span>Define rules for your live support channel. You can apply an action "
"(disable the support, automatically open your support, or just make the "
"button available) for the given URL, and per country.<br/>To identify the "
"country, GeoIP must be installed on your server, otherwise, the countries of"
" the rule will not be taken into account.</span>"
msgstr ""
"Ορίστε κανόνες για το ζωντανό κανάλι υποστήριξης. Μπορείτε να εφαρμόσετε μια"
" ενέργεια (απενεργοποιήστε την υποστήριξη, ανοίξτε αυτόματα την υποστήριξή "
"σας ή απλά κάντε το κουμπί διαθέσιμο) για τη συγκεκριμένη διεύθυνση URL και "
"ανά χώρα.<br/>Για να προσδιορίσετε τη χώρα, πρέπει να εγκατασταθεί το GeoIP "
"στον διακομιστή σας, οι χώρες του κανόνα δεν θα ληφθούν υπόψη."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Action"
msgstr "Ενέργεια"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Anonymous"
msgstr "Ανώνυμος"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__anonymous_name
msgid "Anonymous Name"
msgstr "Όνομα Ανώνυμου"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "Είσαι εντός του πίνακα;"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/im_livechat.js:60
#, python-format
msgid "Ask something ..."
msgstr "Ρωτήστε κάτι ..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Attendees"
msgstr "Συμμετέχοντες"

#. module: im_livechat
#: selection:im_livechat.channel.rule,action:0
msgid "Auto popup"
msgstr "Αυτόματα αναδυόμενο παράθυρο"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Auto popup timer"
msgstr "Χρονομετρητής για Αυτόματα αναδυόμενο παράθυρο"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_ir_autovacuum
msgid "Automatic Vacuum"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Avatar"
msgstr "Άβαταρ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "Μέση διάρκεια"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "Μέσο μήνυμα"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "Μέσος χρόνος για να δοθεί η πρώτη απάντηση στον επισκέπτη"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:13
#, python-format
msgid "Bad"
msgstr "Κακό"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_canned_response_action
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "Έτοιμες Απαντήσεις"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""
"Οι έτοιμες απαντήσεις σας επιτρέπουν να εισάγετε προδακτυλογραφημένες "
"απαντήσεις στα μηνύματά σας πληκτρολογώντας <i>:συντόμευση</i>. Η συντόμευση"
" αντικαθίσταται απευθείας στο μήνυμά σας, έτσι ώστε να μπορείτε να την "
"επεξεργαστείτε πριν την αποστολή."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
#: selection:mail.channel,channel_type:0
msgid "Channel"
msgstr "Κανάλι"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "Όνομα Καναλιού"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "Κανόνας Καναλιού"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "Κανόνες Καναλιού"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Τύπος Καναλιού"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
msgid "Channels"
msgstr "Κανάλια"

#. module: im_livechat
#: selection:mail.channel,channel_type:0
msgid "Chat Discussion"
msgstr "Συζήτηση Chat"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "Κείμενο κράτησης θέσης"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/im_livechat.js:62
#, python-format
msgid "Chat with one of our collaborators"
msgstr "Συνομιλήστε με έναν από τους συνεργάτες μας"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "Κωδικός"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "Διαμόρφωση"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "Συζήτηση"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr ""
"Αντιγράψτε και επικολλήστε αυτόν τον κώδικα στην ιστοσελίδα σας, μέσα στην "
"ετικέτα &lt;head&gt; :"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
msgid "Country"
msgstr "Χώρα"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid "Create a new canned response"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Creation Date"
msgstr "Ημερομηνία Δημιουργίας"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "Ημερομηνία δημιουργίας (ώρα)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "Αξιολογήσεις Πελατών"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr ""
"Το προεπιλεγμένο κείμενο που θα εμφανίζεται στο κουμπί υποστήριξης της "
"Ζωντανής Συνομιλίας"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Auto popup' otherwise this parameter will not be "
"taken into account."
msgstr ""
"Καθυστέρηση (σε δευτερόλεπτα) για να ανοίξει αυτόματα το παράθυρο "
"συνομιλίας. Σημείωση: η επιλεγμένη ενέργεια πρέπει να είναι \"Αυτόματη "
"αναδυόμενη προβολή\", διαφορετικά αυτή η παράμετρος δεν θα ληφθεί υπόψη."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:7
#, python-format
msgid "Did we correctly answer your question ?"
msgstr "Απαντήσαμε σωστά στην ερώτησή σας;"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel
msgid "Discussion channel"
msgstr "Κανάλι Συζήτησης"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: im_livechat
#: selection:im_livechat.channel.rule,action:0
msgid "Display the button"
msgstr "Εμφάνιση κουμπιού"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "Διάρκεια της συνομιλίας (σε δευτερόλεπτα)"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"Each channel has it's own URL that you can send by email to\n"
"                your customers in order to start chatting with you."
msgstr ""
"Κάθε κανάλι έχει τη δική του διεύθυνση URL, την οποία μπορείτε να στείλετε "
"μέσω email στους πελάτες σας, προκειμένου να αρχίσουν να συζητούν μαζί σας."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Empty Sessions"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:19
#, python-format
msgid "Explain your note"
msgstr "Εξηγήστε τη σημείωσή σας"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, please install the website_livechat "
"module. Then go to Website &gt; Configuration &gt; Settings and select the "
"Website Live Chat Channel you want to add on your website."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""
"Λαμβάνοντας υπόψη την σειρά των κανόνων αντιστοίχησης, εάν δύο κανόνες "
"ταιριάζουν για τη δεδομένη διεύθυνση URL/χώρα, θα επιλεγεί ο κανόνας "
"χαμηλότερη ακολουθία."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:11
#, python-format
msgid "Good"
msgstr "Καλό"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Group By..."
msgstr "Ομαδοποίηση κατά..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Happy face"
msgstr ""

#. module: im_livechat
#: selection:im_livechat.channel.rule,action:0
msgid "Hide the button"
msgstr "Απόκρυψη κουμπιού"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "History"
msgstr "Ιστορικό"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "Ώρα έναρξης συνεδρίας"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/im_livechat.js:63
#, python-format
msgid "How may I help you?"
msgstr "Πώς μπορώ να σας βοηθήσω;"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr ""
"Πώς να χρησιμοποιήσετε το γραφικό στοιχείο της Ζωντανής Συζήτησης Ιστότοπου."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:16
#, python-format
msgid "I don't want to rate this conversation"
msgstr "Δεν θέλω να αξιολογήσω αυτή τη συζήτηση"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "Κωδικός"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image
msgid "Image"
msgstr "Εικόνα"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "Συμμετοχή"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "Συμμετοχή σε Κανάλι"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "Τελευταίες 24 ώρες"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "Αποχώρηση"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "Αποχώρηση από Κανάλι"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "Ακροατές ενός Καναλιού"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
msgid "Live Chat"
msgstr "Ζωντανή Συνομιλία"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
msgid "Live Support"
msgstr "Ζωντανή Υποστήριξη"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat_backend.xml:9
#: code:addons/im_livechat/static/src/xml/im_livechat_backend.xml:21
#, python-format
msgid "Livechat"
msgstr "Ζωντανή Συνομιλία"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "Κανάλι Ζωντανής Συνομιλίας"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr ""

#. module: im_livechat
#: selection:mail.channel,channel_type:0
msgid "Livechat Conversation"
msgstr "Ζωντανή Συνομιλία"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audiance, the duration of a session, etc."
msgstr ""
"Η Αναφορά Καναλιού Ζωντανής Συνομιλίας σας επιτρέπει να ελέγχετε και να "
"αναλύετε εύκολα την απόδοση των συνεδριών της επιχείρησής σας. Μπορείτε να "
"εξάγετε πληροφορίες σχετικά με τις αναπάντητες συνεδρίες, την ακρόαση, τη "
"διάρκεια μιας περιόδου σύνδεσης κ.λπ."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
msgid "Livechat Support Report Channel"
msgstr "Αναφορά Καναλιού Ζωντανής Συνομιλίας"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "Στατιστικά Ζωντανής Συνομιλίας"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Manager"
msgstr "Διευθυντής"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "Σειρά Αντιστοιχίας"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_medium
msgid "Medium"
msgstr "Μέσο"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__image_medium
msgid ""
"Medium-sized photo of the group. It is automatically resized as a 128x128px "
"image, with aspect ratio preserved. Use this field in form views or some "
"kanban views."
msgstr ""
"Μεσαίου μεγέθους φωτογραφία της ομάδας. Αλλάζει αυτόματα ως εικόνα 128x128 "
"εικονοστοιχεία, με διατήρηση της αναλογίας διαστάσεων. Χρησιμοποιήστε αυτό "
"το πεδίο για τις προβολές φόρμας ή μερικές όψεις Kanban."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "Αναπάντητες συνεδρίες"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
msgid "Name"
msgstr "Περιγραφή"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Neutral face"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:119
#, python-format
msgid "No history found"
msgstr "Δεν βρέθηκε ιστορικό"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/im_livechat.js:226
#, python-format
msgid ""
"None of our collaborators seems to be available, please try again later."
msgstr ""
"Κανένας από τους συνεργάτες μας δεν είναι διαθέσιμος, παρακαλώ δοκιμάστε "
"ξανά αργότερα."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "Number of conversation"
msgstr "Πλήθος συνομιλιών"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "Πλήθος διαφορετικών ομιλητών"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "Πλήθος μηνυμάτων συνεδρίας"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:12
#, python-format
msgid "OK"
msgstr "OK"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "Χειριστής"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "Ανάλυση Χειριστή"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "Χειριστές"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"Operators\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""
"Οι χειριστές που δεν εμφανίζουν καμία δραστηριότητα στο Odoo για περισσότερο"
" από 30 λεπτά θα θεωρούνται αποσυνδεδεμένοι."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "Επιλογές"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings over the past 7 days"
msgstr "Ποσοστό ευχαριστημένων βαθμολογήσεων τις τελευταίες 7 ημέρες"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Rating"
msgstr "Βαθμολόγηση"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Τελευταίο Σχόλιο Βαθμολογίας"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Τελευταία Εικόνα Βαθμολογίας"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Τελευταία Τιμή Βαθμολογίας"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_count
msgid "Rating count"
msgstr "Πλήθος Βαθμολογιών"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/im_livechat.js:420
#, python-format
msgid "Rating: %s"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Rating: Bad"
msgstr "Βαθμολογία: Κακή"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Rating: Great"
msgstr "Βαθμολογία: Καλή"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Rating: Okay"
msgstr "Βαθμολογία: Μέτρια"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_view_livechat_rating
msgid "Ratings for livechat channel"
msgstr "Αξιολογήσεις για κανάλι Ζωντανής Συνομιλίας"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr "Λόγος για την βαθμολόγηση"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""
"Ορίστε κάποιο Regular Expression που καθορίζει τις ιστοσελίδες στις οποίες "
"θα εφαρμοστεί.."

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "Αναφορά"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "Κανόνες"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sad face"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "Δέσμη ενεργειών (εξωτερική)"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Search history"
msgstr "Ιστορικό αναζήτησης"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "Αναφορά αναζήτησης"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:107
#, python-format
msgid "See 15 last visited pages"
msgstr "Δείτε τις 15 τελευταίες σελίδες που επισκέφτηκαν"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Session Date"
msgstr "Ημερομηνία Συνεδρίας"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Session Form"
msgstr "Από Συνεδρίας"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "Στατιστικά Συνεδρίας"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "Συνεδρίες"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "Ιστορικό Συνεδρίας"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__image_small
msgid ""
"Small-sized photo of the group. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Μικρού μεγέθους φωτογραφία της ομάδας. Αλλάζει αυτόματα ως εικόνα 64x64 "
"εικονοστοιχεία, με διατήρηση της αναλογίας διαστάσεων. Χρησιμοποιήστε αυτό "
"το πεδίο οπουδήποτε απαιτείται μια μικρή εικόνα."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Start Date"
msgstr "Ημερομηνία Έναρξης"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "Ημερομηνία Έναρξης της Συνεδρίας"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start date of the conversation"
msgstr "Ημερομηνία Έναρξης της Συζήτησης"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "Κείμενο του Κουμπιού"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "Το κανάλι του κανόνα"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__name
msgid "The name of the channel"
msgstr "Το όνομα του καναλιού"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide Button', "
"the chat button will be hidden on the specified URL from the visitors "
"located in these 2 countries. This feature requires GeoIP installed on your "
"server."
msgstr ""
"Ο κανόνας ισχύει μόνο για αυτές τις χώρες. Παράδειγμα: εάν επιλέξετε "
"'Ελλάδα' και 'Βέλγιο' και ορίσετε τη δράση σε 'Απόκρυψη κουμπιού', το κουμπί"
" συζήτησης θα είναι κρυμμένο στη συγκεκριμένη διεύθυνση URL από τους "
"επισκέπτες που βρίσκονται από αυτές τις 2 χώρες. Αυτή η λειτουργία απαιτεί "
"την εγκατάσταση του GeoIP στον διακομιστή σας."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "Ο χρήστης θα είναι σε θέση να διαγράψετε κανάλια υποστήριξης."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "Ο χρήστης θα είναι θέση να συμμετάσχει σε κανάλια υποστήριξης."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_view_livechat_rating
msgid "There is no rating for this channel at the moment"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__image
msgid ""
"This field holds the image used as photo for the group, limited to "
"1024x1024px."
msgstr ""
"Το πεδίο αυτό αποθηκεύει την εικόνα που χρησιμοποιείται ως φωτογραφία για "
"την ομάδα, με μέγιστο μέγεθος τα 1024x1024 εικονοστοιχεία."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""
"Αυτό είναι ένα αυτοματοποιημένο μήνυμα καλωσορίσματος που ο επισκέπτης θα "
"δει όταν ξεκινήσει μια νέα συζήτηση."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_small
msgid "Thumbnail"
msgstr "Μικρογραφία"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "Ώρα για απάντηση"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "Επεξεργασμένες συνεδρίες"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "Διεύθυνση URL Regex"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""
"Διεύθυνση URL σε μια στατική σελίδα όπου μπορείτε να συζητήσετε με τον "
"χειριστή του καναλιού."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model:res.groups,name:im_livechat.im_livechat_group_user
msgid "User"
msgstr "Χρήστης"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/controllers/main.py:37
#: code:addons/im_livechat/static/src/js/im_livechat.js:61
#, python-format
msgid "Visitor"
msgstr "Επισκέπτης"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "Ιστοσελίδα"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "Κανάλια Ζωντανής Συνομιλίας Ιστότοπου"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "Μήνυμα Καλωσορίσματος"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "Γραφικό Στοιχείο"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "With Messages"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""
"Μπορείτε να δημιουργήσετε κανάλια για κάθε ιστότοπο στον οποίο θέλετε να "
"ενσωματώσετε το γραφικό στοιχείο ζωντανής συνομιλίας ιστότοπου, επιτρέποντας"
" στους επισκέπτες του ιστοτόπου σας να μιλούν σε πραγματικό χρόνο με τους "
"χειριστές σας."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Your chatter history is empty"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "π.χ. Γεια σας, πώς μπορώ να σας βοηθήσω;"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "π.χ. YourWebsite.com"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr ""
"ή αντιγράψτε την διεύθυνση URL και στείλτε την με email στους πελάτες ή τους"
" προμηθευτές σας:"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "δευτερόλεπτα"
