# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON> <dragan.vuk<PERSON><PERSON><PERSON>@gmail.com>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON><PERSON> <dragan.vukosavl<PERSON><EMAIL>>, 2024\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#, python-format
msgid " (copy)"
msgstr " (kopija)"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "\"%s\" is not a valid email."
msgstr "\"%s\" nije ispravan email."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "# Messages"
msgstr "# Poruka"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr "# Ocena"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "# Sesija"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "# sagovornika"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% Srećnih"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr "% sreće"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/models/public_livechat.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s i %s kucaju..."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "%s has joined"
msgstr "%s se pridružio/la"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/models/public_livechat.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s kuca..."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/models/public_livechat.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s i drugi kucaju..."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid ""
"'%(input_email)s' does not look like a valid email. Can you please try "
"again?"
msgstr ""
"'%(input_email)s' ne izglada kao ispravan email. Molimo vas da pokušate "
"ponovo?"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Show' displays the chat button on the pages.\n"
"* 'Show with notification' is 'Show' in addition to a floating text just next to the button.\n"
"* 'Open automatically' displays the button and automatically opens the conversation pane.\n"
"* 'Hide' hides the chat button on the pages.\n"
msgstr ""
"* 'Prikaži' prikazuje chat dugme na stranicama.\n"
"* 'Prikaži sa notifikacijom' je 'Prikaži' uz dodatak lebdećeg teksta odmah pored dugmeta.\n"
"* 'Otvori automatski' prikazuje dugme i automatski otvara prozor za konverzaciju.\n"
"* 'Sakrij' krije chat dugme na stranicama.\n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ", na"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "-------- Show older messages --------"
msgstr "-------- Prikaži starije poruke --------"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "1 is Monday, 7 is Sunday"
msgstr "1 je Ponedeljak, 7 je Nedelja"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Back to edit mode"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Nazad na uređivački mod"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"
msgstr "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr "<span style=\"font-size: 10px;\">Livechat konverzacija</span><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr "<span>Srdačan pozdrav,</span><br/><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr "<span>Zdravo,</span><br/>Evo kopije vaše konverzacije sa"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Reminder: This step will only be played if no operator is "
"available.</span>"
msgstr ""
"<span>Podsetnik: Ovaj korak će biti izveden samo ako nema dostupnih "
"operatera.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before the Bot can perform more complex actions (Forward to an Operator, ...). </span>\n"
"                    <span>Use Channel Rules if you want the Bot to interact with visitors only when no operator is available.</span>"
msgstr ""
"<span>Savet: Najmanje jedna interakcija (Pitanje, email, ...) je potrebna pre no što Bot može da izvede kompleksnije akcije (Prosledi operateru, ...). </span>\n"
"                    <span>Koristite Pravila kanala ako želite da Bot ima interakciju sa posetiocima samo kada nema dostupnih operatera.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before "
"the Bot can perform more complex actions (Forward to an Operator, "
"...).</span>"
msgstr ""
"<span>Savet: Najmanje jedna interakcija (Pitanje, email, ...) je potrebna "
"pre no što Bot može da izvede kompleksnije akcije (Prosledi operateru, "
"...).</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Tip: Plan further steps for the Bot in case no operator is "
"available.</span>"
msgstr ""
"<span>Savet: Planirajte naredne korake za Bot-a u slučaju da nema dostupnih "
"operatera.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<span>You are currently testing</span>"
msgstr "<span>Vi trenutno testirate</span>"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_chatbot_message__unique_mail_message_id
msgid "A mail.message can only be linked to a single chatbot message"
msgstr "Jedna mail.message može biti povezana sa samo jednom chatbot porukom"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""
"Sesija je bez odgovora, ako operate nije odgovorio. \n"
"                                       Ako je posetilaci ujedno i operater, sesija će uvek biti odgovorena."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__active
msgid "Active"
msgstr "Aktivno"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "Administrator"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_redirect
msgid "And tadaaaa here you go! 🌟"
msgstr "I tadaaaa izvolite! 🌟"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Anonymous"
msgstr "Anoniman"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__anonymous_name
msgid "Anonymous Name"
msgstr "Ime anonimnog/ne"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__name
msgid "Answer"
msgstr "Odgovor"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__answer_ids
msgid "Answers"
msgstr "Odgovori"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Archived"
msgstr "Arhivirano"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "Da li ste u matriksu?"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "Ask something ..."
msgstr "Pitajte nešto..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Attendees"
msgstr "Učesnici"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Avatar"
msgstr "Avatar"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_avg
msgid "Average Rating"
msgstr "Prosečna ocena"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "Prosečna ocena (%)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "Prosečno trajanje"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "Prosečna poruka"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr "Prosečno vreme u sekundama za davanje prvog odgovora posetiocu"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "Prosečno vreme za davanje prvog odgovora posetiocu"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Bad"
msgstr "Loše"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__operator_partner_id
msgid "Bot Operator"
msgstr "Bot Operater"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr "Pozadinska boja dugmeta"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr "Boja teksta dugmeta"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_canned_response_action
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "Pripremljeni odgovori"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""
"Unapred pripremljeni odgovori vam omogućavaju da umetnete spremne odgovore u\n"
"                vaše poruke tako što ćete ukucati <i>:shortcut</i>. Prečica je\n"
"                zamenena direktno uva šoj poruci, tako da još uvek možete da je uređujete\n"
"                pre slanja."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Channel"
msgstr "Kanal"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr "Boja zaglavlja kanala"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "Ime kanala"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "Pravilo kanala"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "Pravila kanala"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Vrsta kanala"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Channels"
msgstr "Kanali"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "Chat prostor za unos"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"Chat je privatan i jedinstven između 2 osobe. Grupa je privatne između "
"pozvanih osoba. Kanalu se može slobodno pristupiti (u zavisnosti od njegovih"
" podešavanja)."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "Chat with one of our collaborators"
msgstr "Chat-ujte sa nekim od naših saradnika"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.chatbot_script_action
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_script_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Chatbot"
msgstr "Chatbot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__chatbot_current_step_id
msgid "Chatbot Current Step"
msgstr "Trenutni korak Chatbot-a"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_message
msgid "Chatbot Message"
msgstr "Chatbot poruka"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__chatbot_message_ids
msgid "Chatbot Messages"
msgstr "Chatbot poruke"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Chatbot Name"
msgstr "Chatbot ime"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "Chatbot skripta"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_answer
msgid "Chatbot Script Answer"
msgstr "Chatbot odgovor skripte"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "Korak skripte za Chatbot"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__script_step_id
msgid "Chatbot Step"
msgstr "Chatbot kjorak"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.chatbot_config
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Chatbots"
msgstr "Chatbot-ovi"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "Close"
msgstr "Zatvori"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Close conversation"
msgstr "Zatvori konverzaciju"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "Kod"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "Podešavanje"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "Konverzacija"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Conversation Sent"
msgstr "Konverzacija je poslata"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#, python-format
msgid "Conversation ended..."
msgstr "Konverzacija je završena..."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Conversation with %s"
msgstr "Konverzacija sa %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr "Obrađene konverzacije"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr ""
"Kopirajte i nalepite ovaj kod u svoj website, između &lt;head&gt; oznaka:"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__country_id
msgid "Country"
msgstr "Zemlja"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr "Zemlja posetioca"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__country_id
msgid "Country of the visitor of the channel"
msgstr "Zemlja posetioca kanala"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid "Create a Chatbot"
msgstr "Kreiraj Chatbot"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr ""
"Kreirajte kanal i počnite da chat-ujete da biste popunili svoju istoriju."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid "Create a new canned response"
msgstr "Kreiraj novi pripremljeni odgovor"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation Date"
msgstr "Datum kreiranja"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "Datum kreiranja"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "Datum kreiranja (sat)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "Ocene korisnika"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr "Broj dana"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr "Dani aktivnosti"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr "Podrazumevana pozadinska boja dugmeta za Livechat"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr "Podrazumevana pozadinska boja zaglavlja kanala kada se otvori"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr "Podrazumevana boja teksta na Livechat dugmetu"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr "Podrazumevani tekst prikazan na dugmetu za Livechat podršku"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr "Podrazumevana boja naslova kanala kada se otvori"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr "Definišite novi live chat kanala za website"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Define rules for your live support channel. You can apply an action for the "
"given URL, and per country.<br/>To identify the country, GeoIP must be "
"installed on your server, otherwise, the countries of the rule will not be "
"taken into account."
msgstr ""
"Definišite pravila za svoj kanal podrške. Možete primeniti radnju za dati "
"URL i po zemlji. <br/>Da biste identifikovali zemlju, GeoIP mora biti "
"instaliran na vašem serveru, u suprotnom zemlje pravila neće biti uzete u "
"obzir."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Open automatically' otherwise this parameter will "
"not be taken into account."
msgstr ""
"Odloži (u sekundama) za automatsko otvaranje prozora razgovora. Napomena: "
"izabrana radnja mora biti 'Otvori automatski', inače ovaj parametar neće "
"biti uzet u obzir."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Did we correctly answer your question ?"
msgstr "Da li smo tačno odgovorili na vaše pitanje ?"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "Pregled"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__mail_channel_id
msgid "Discussion Channel"
msgstr "Kanal diskusije"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "Trajanje konverzacije (u sekundama)"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_email
msgid "Email"
msgstr "E-mail"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
msgid "Enable the bot only if there is no operator available"
msgstr "Omogući bot-a samo ako nema dostupnih operatera"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Enabled only if no operator"
msgstr "Omogući samo ako nema operatera"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Explain your note"
msgstr "Objasnite svoju belešku"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_invalid
msgid "First Step Invalid"
msgstr "Prvi korak je nevažeći"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_operator
msgid "First Step Operator"
msgstr "Operater prvog koraka"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__first_step_warning
msgid "First Step Warning"
msgstr "Upozorenje prvog koraka"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr ""
"Za website-ove napravljene pomoću Odoo CMS-a, idite na Website &gt; "
"Konfiguracija &gt; Podešavanja i izaberite Website Live Chat kanal koji "
"želite da dodate na svoj website."

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__forward_operator
msgid "Forward to Operator"
msgstr "Prosledi operateru"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_single
msgid "Free Input"
msgstr "Slobodan unos"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_multi
msgid "Free Input (Multi-Line)"
msgstr "Slobodan unos (Više linija)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""
"Dat je redosled za pronalaženje odgovarajućeg pravila. Ako se 2 pravila "
"poklapaju za dati url/zemlju, biće izabrano ono sa najnižim rednim brojem."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Good"
msgstr "Dobro"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Group By..."
msgstr "Grupiši po..."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#, python-format
msgid "Have a Question? Chat with us."
msgstr "Imate pitanje? Chat-ujte sa nama."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr "Pozadinska boja zaglavlja"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide"
msgstr "Sakrij"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "History"
msgstr "Istorija"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing
msgid ""
"Hmmm, let me check if I can find someone that could help you with that..."
msgstr ""
"Hmmm, samo da proverim da li mogu da nađem nekoga ko bi mogao da vam pomogne"
" u vezi sa tim..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "Sat početka Datum sesije"

#. module: im_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "How may I help you?"
msgstr "Kako mogu da vam pomognem?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr "Kako da koristite website Live Chat vidžet?"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr "Uh-uh, izgleda da nijedan od naših operatera nije dostupan 🙁"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_just_looking
msgid "I am just looking around"
msgstr "Samo razgledam"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_documentation
msgid "I am looking for your documentation"
msgstr "Tražim vašu dokumentaciju"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_pricing
msgid "I have a pricing question"
msgstr "Imam pitanje o ceni"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_exit
msgid "If you need anything else, feel free to get back in touch"
msgstr "Ako vam treba još nešto, slobodno se javite ponovo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1920
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
msgid "Image"
msgstr "Slika"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1024
msgid "Image 1024"
msgstr "Slika 1024"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_128
msgid "Image 128"
msgstr "Slika 128"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_256
msgid "Image 256"
msgstr "Slika 256"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_512
msgid "Image 512"
msgstr "Slika 512"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.js:0
#, python-format
msgid "Invalid email address"
msgstr "Neispravna email adresa"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__is_forward_operator_child
msgid "Is Forward Operator Child"
msgstr "Da li je Prosledi operateru podređeno"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__is_discuss_sidebar_category_livechat_open
msgid "Is category livechat open"
msgstr "Da li je livechat kategorija otvorena"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr "Da li je livechat u toku?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr "Da li je posetilac anoniman"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "Pridruži se"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "Pridruži se kanalu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr "Kpi vrednost Livechat konverzacija"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr "Kpi vrednost Livechat ocena"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr "Kpi vrednost Livechat odgovora"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "Poslednjih 24h"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message____last_update
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script____last_update
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer____last_update
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator____last_update
msgid "Last Modified on"
msgstr "Poslednja izmena dana"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "Poslednje izmenio/la"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "Poslednje ažuriranje dana"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "Napusti"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "Napusti kanal"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel_member
msgid "Listeners of a Channel"
msgstr "Slušaoci kanala"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "Live Chat"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Live Chat Button"
msgstr "Live Chat dugme"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr "Pretraga LiveChat kanala"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/im_livechat/static/src/models/discuss_sidebar_category.js:0
#: code:addons/im_livechat/static/src/models/mobile_messaging_navbar_view.js:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#, python-format
msgid "Livechat"
msgstr "Livechat"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr "Livechat dugme"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr "Boja Livechat dugmeta"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "Livechat kanal"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__livechat_channel_count
msgid "Livechat Channel Count"
msgstr "Brojač Livechat kanala"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr "Pravila Livechat kanala"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__mail_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "Livechat konverzacija"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_mail_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr "ID Livechat operatera je obavezan za kanal tipa livechat."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr "Izveštaj Livechat kanala podrške"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audience, the duration of a session, etc."
msgstr ""
"Statistika kanala Livechat podrške vam omogućava da lako proverite i "
"analizirate performanse sesija livechat-ova svoje kompanije. Izvucite "
"informacije o propuštenim sesijama, publici, trajanju sesije itd."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr "Izveštaj operatera Livechat podrške"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "Statistika Livechat podrške"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
msgid "Livechat Username"
msgstr "Livechat korisničko ime"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr "Livechat prozor"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_active
msgid "Livechat session is active until visitor leaves the conversation."
msgstr "Livechat sesija je aktivno sve dok posetilac ne napusti konverzaciju"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "Loading older messages..."
msgstr "Učitava starije poruke..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "Redosled poklapanja"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__message
msgid "Message"
msgstr "Poruka"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "Propuštene sesije"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__name
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Name"
msgstr "Ime"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "New messages"
msgstr "Nove poruke"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "No available collaborator, please try again later."
msgstr "Nema dostupnih saradnika, molimo pokušajte kasnije."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr "Još uvek nema ocena korisnika za live chat sesiju"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "Još uvek nema podataka!"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "No history found"
msgstr "Nije pronađena istorija"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_window/public_livechat_window.xml:0
#, python-format
msgid "No operator available"
msgstr "Nema dostupnih operatera"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "Note by"
msgstr "Beleška od"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__chatbot_script_count
msgid "Number of Chatbot"
msgstr "Broj Chatbot-ova"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
msgid "Number of conversation"
msgstr "Broj konverzacija"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr "Broj dana od prve sesije operatera"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "Broj različitih sagovornika"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "Broj poruka u konverzaciji"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "OK"
msgstr "U redu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr "Online Chat ime"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Only If"
msgstr "Samo ako"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Oops! Something went wrong."
msgstr "Ups! Nešto je krenulo naopako."

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Open automatically"
msgstr "Otvori automatski"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Open automatically timer"
msgstr "Otvori automatski tajmer"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_operator_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "Operater"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "Analiza operatera"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "Operateri"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"Operators\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"
msgstr ""
"Operateri\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""
"Operateri koji ne pokazuju nikakvu aktivnost u Odoo-u više od 30 minuta će "
"se smatrati diskonektovanima."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_answer_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "Optional Link"
msgstr "Opcioni link"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "Opcije"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Procenat zadovoljnih ocena"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_phone
msgid "Phone"
msgstr "Telefon"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "Please call me on: "
msgstr "Molim pozovite me na:"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Please check your internet connection."
msgstr "Molimo vas da proverite sovju internet konekciju."

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
#, python-format
msgid "Please contact me on: "
msgstr "Molim kontaktirajte me na:"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_just_looking
msgid "Please do! If there is anything we can help with, let us know"
msgstr "Molim vas! Ako postoji nešto u čemu možemo da pomognemo, javite nam"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "Please wait"
msgstr "Molimo sačekajte"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.xml:0
#, python-format
msgid "Please wait..."
msgstr "Molimo sačekajte..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "Platformu pokreće"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_selection
msgid "Question"
msgstr "Pitanje"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Rating"
msgstr "Ocene"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr "Tekst prosečne ocene"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Poslednja povratna informacija ocene"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Poslednja slika ocene"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Poslednja vrednost ocene"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Ocena zadovoljstva"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_text
msgid "Rating Text"
msgstr "Tekst ocene"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_count
msgid "Rating count"
msgstr "Brojač ocena"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.js:0
#, python-format
msgid "Rating: %s"
msgstr "Ocena: %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
msgid "Ratings"
msgstr "Ocene"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "Ocene za livechat kanal"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Receive a copy of this conversation"
msgstr "Primite kopiju ovog razgovora"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__redirect_link
msgid "Redirect Link"
msgstr "Link preusmerenja"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""
"Regularni izraz koji navodi web stranice na koje će se ovo pravilo "
"primeniti."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__mail_message_id
msgid "Related Mail Message"
msgstr "Povezana mail poruka"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "Izveštaj"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
#, python-format
msgid "Reset to default colors"
msgstr "Resetuj na podrazumevane boje"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#, python-format
msgid "Restart"
msgstr "Ponovo pokreni"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#, python-format
msgid "Restart Conversation"
msgstr "Ponovo pokreni konverzaciju"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Restarting conversation..."
msgstr "Restartovanje konverzacije..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "Pravila"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr "Ocena zadovoljstva"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr "Sačuvajte svoj kanal da biste dobili svoj vidžet za podešavanje."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/public_livechat_window.js:0
#, python-format
msgid "Say something"
msgstr "Recite nešto"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Script"
msgstr "Skripta"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "Skripta (eksterna)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__script_step_id
msgid "Script Step"
msgstr "Korak skripte"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__script_step_ids
msgid "Script Steps"
msgstr "Koraci skripte"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Search history"
msgstr "Pretraži istoriju"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "Pretraži izveštaj"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/models/messaging_initializer.js:0
#, python-format
msgid "See 15 last visited pages"
msgstr "Pogledaj 15 poslednjih posećenih stranica"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/public_livechat_window.js:0
#, python-format
msgid "Select an option above"
msgstr "Izaberite opciju iznad"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Send"
msgstr "Pošalji"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__sequence
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__sequence
msgid "Sequence"
msgstr "Niz"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Session Date"
msgstr "Datum sesije"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Session Form"
msgstr "Obrazac sesije"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "Statistike sesije"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "Session expired... Please refresh and try again."
msgstr "Sesija je istekla... Molimo osvežite stranicu i pokušajte ponovo."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr "Sesija nije ocenjena"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr "Sesija(e) bez odgovora"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "Sesije"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "Istorija sesija"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Show"
msgstr "Prikaži"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Show this step only if all of these answers have been selected."
msgstr "Prikaži ovaj korak samo ako su svi ovi odgovori izabrani."

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button_and_text
msgid "Show with notification"
msgstr "Prikaži sa notifikacijom"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__source_id
msgid "Source"
msgstr "Izvorno"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "Datum početka sesije"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr "Sat početka sesije"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "Vrsta koraka"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__text
msgid "Text"
msgstr "Tekst"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "Tekst dugmeta"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr "Tekst koji traži od korisnika da započne chat."

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.js:0
#, python-format
msgid "Thank you for your feedback"
msgstr "Hvala vam na povratnoj informaciji"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "Kanal pravila"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide', the "
"chat button will be hidden on the specified URL from the visitors located in"
" these 2 countries. This feature requires GeoIP installed on your server."
msgstr ""
"Pravilo će se primenjivati samo za ove zemlje. Primer: ako izaberete "
"'Belgija' i 'Sjedinjene Države' i ako radnju podesite na 'Sakrij', dugme za "
"chat će biti skriveno na navedenom URL-u od posetilaca koji se nalaze u ove"
" 2 zemlje. Ova funkcija zahteva GeoIP instaliran na vašem serveru."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "Korisnik će biti u mogućnosti da obriše kanale za podršku."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "Korisnik će biti u mogućnosti da se pridruži kanalima za podršku."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_answer__redirect_link
msgid ""
"The visitor will be redirected to this link upon clicking the option (note "
"that the script will end if the link is external to the livechat website)."
msgstr ""
"Posetilac će biti preusmeren na ovaj link nakon što klikne na opciju "
"(imajte na umu da će se skripta završiti ako je link spoljašnji za livechat "
"website)."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr "Trenutno nema ocena za ovaj kanal"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "Ove nedelje"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""
"Ovo je automatska poruka 'dobrodošlice' koju će vaš posetilac videti kada "
"započne novi razgovor."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr ""
"Ovo korisničko ime će biti korišćeno kao vaše ime u livechat kanalima."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "Vreme za odgovor"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr "Vreme za odgovor (sec)"

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr "Savet: Koristite pripremljene odgovora da biste brže chat-ovali"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__title
msgid "Title"
msgstr "Naslov"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr "Boja naslova"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/models/public_livechat_message.js:0
#, python-format
msgid "Today"
msgstr "Danas"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "Tretirane sesije"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "Try again"
msgstr "Pokušajte ponovo"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Type <b>:shortcut</b> to insert a canned response in your message.<br>"
msgstr ""
"Ukucajte <b>:shortcut</b> da umetnete pripremeni odgovor u svoju poruku.<br>"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "URL Regex"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""
"URL do statične stranice na kojoj vaš klijent može da razgovara sa "
"operaterom kanala."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr ""
"Koristite unapred pripremljene odgovore da biste definisali šablone poruka u"
" livechat aplikaciji. Da biste učitali unapred pripremljeni odgovor, "
"započnite rečenicu sa „:“ i izaberite šablon."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "Korisnik"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr "Korisničko ime korisnika Livechat-a"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr "Podešavanja korisnika"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_script_answer_id
msgid "User's answer"
msgstr "Odgovor korisnika"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_raw_answer
msgid "User's raw answer"
msgstr "Neobrađen odgovor korisnika"

#. module: im_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/public_models/livechat_button_view.js:0
#, python-format
msgid "Visitor"
msgstr "Posetilac"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Visitor has left the conversation."
msgstr "Posetilac je napustio konverzaciju."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr "Posetilac je zadovoljan"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "Web stranica"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "Website Live Chat kanali"

#. module: im_livechat
#: model:chatbot.script,title:im_livechat.chatbot_script_welcome_bot
msgid "Welcome Bot"
msgstr "Bot dobrodošlice"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "Poruka dobrodošlice"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_welcome
msgid "Welcome to CompanyName ! 👋"
msgstr "Dobro došli u ImeKompanije ! 👋"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_dispatch
msgid "What are you looking for?"
msgstr "Šta tražite?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "Vidžet"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr ""
"Da li biste želeli da ostavite svoju email adresu kako bismo mogli da vas "
"kontaktiramo?"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/models/public_livechat_message.js:0
#, python-format
msgid "Yesterday"
msgstr "Juče"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "You"
msgstr "Ti"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid ""
"You can create a new Chatbot with a defined script to speak to your website "
"visitors."
msgstr ""
"Možete kreirati novog Chatbot-a sa definisanom skriptom da razgovara sa "
"posetiocima vašeg website-a."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""
"Možete da kreirate kanala za svako od website-ova na kom želite\n"
"                da integrišete website live chat vidžet, koji dozvoljava posetiocima vašeg\n"
"                website-a da razgovaraju u realnom vremenu sa vašim operaterima."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Your chatter history is empty"
msgstr "Vaša chat istorija je prazna"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#, python-format
msgid "chatbot_image"
msgstr "chatbot_image"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "e.g. \"Meeting Scheduler Bot\""
msgstr "npr. \"Bot za zakazivanje sastanaka\""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "e.g. 'How can I help you?'"
msgstr "npr, 'Kako mogu da pomognem?'"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr "npr. /contactus"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "npr. Zdravo, kako mogu da pomognem?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "npr. TvojWebsite.com"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/public_livechat_chatbot.xml:0
#, python-format
msgid "is typing"
msgstr "kuca"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/feedback/feedback.xml:0
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr ""
"ili kopirajte ovaj url i pošaljite ga mail-om svojim korisnicima i "
"dobavljačima:"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.js:0
#, python-format
msgid "read less"
msgstr "čitaj manje"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/legacy/widgets/public_livechat_view/public_livechat_view.js:0
#, python-format
msgid "read more"
msgstr "čitaj više"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "sekunde"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr "{{author_name}}"
