# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_presence
# 
# Translators:
# krn<PERSON><PERSON>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 18:35+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_presence
#: model:mail.template,body_html:hr_presence.mail_template_presence
msgid ""
"<div>\n"
"                    Dear <t t-out=\"object.name or ''\"><PERSON></t>,<br><br>\n"
"                    Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.<br>\n"
"                    Please, take appropriate measures in order to carry out this work absence.<br>\n"
"                    Do not hesitate to contact your manager or the human resource department.\n"
"                    <br>Best Regards,<br><br>\n"
"                </div>\n"
"            "
msgstr ""

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
msgid "Absence/Presence"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__absent
msgid "Absent"
msgstr "Távol lévő"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_hr_employee_base
msgid "Basic Employee"
msgstr "Egyszerű munkavállaló"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_company
msgid "Companies"
msgstr "Vállalatok"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Compose Email"
msgstr "E-mail írás"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_actions_server_action_open_presence_view
msgid "Compute presence and open presence view"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__create_uid
msgid "Create Uid"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__email_sent
msgid "Email Sent"
msgstr "E-mail elküldve"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Employee's Presence to Define"
msgstr ""

#. module: hr_presence
#: model:sms.template,name:hr_presence.sms_template_data_hr_presence
msgid "Employee: Presence Reminder"
msgstr "Alkalmazott: Jelenlét emlékeztető"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
msgid "Employees"
msgstr "Alkalmazottak"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#: model:sms.template,body:hr_presence.sms_template_data_hr_presence
#, python-format
msgid ""
"Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.\n"
"Please, take appropriate measures in order to carry out this work absence.\n"
"Do not hesitate to contact your manager or the human resource department."
msgstr ""

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_cron_presence_control_ir_actions_server
#: model:ir.cron,cron_name:hr_presence.ir_cron_presence_control
msgid "HR Presence: cron"
msgstr ""

#. module: hr_presence
#: model:mail.template,name:hr_presence.mail_template_presence
msgid "HR: Employee Absence email"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_company__hr_presence_last_compute_date
msgid "Hr Presence Last Compute Date"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__hr_presence_state_display
msgid "Hr Presence State Display"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__ip
msgid "IP Address"
msgstr "IP cím"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__ip_connected
msgid "Ip Connected"
msgstr "IP kapcsolódva"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Log"
msgstr "Napló"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__manually_set_present
msgid "Manually Set Present"
msgstr ""

#. module: hr_presence
#: model:ir.ui.menu,name:hr_presence.menu_hr_presence_view
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_search
msgid "Presence"
msgstr "Jelenlét"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__present
msgid "Present"
msgstr "Jelen van"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "SMS"
msgstr "SMS"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Send SMS Text Message"
msgstr "SMS küldése"

#. module: hr_presence
#: model:mail.template,description:hr_presence.mail_template_presence
msgid ""
"Sent manually in presence module when an employee wasn't working despite not"
" being off"
msgstr ""

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Set as absent"
msgstr ""

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Set as present"
msgstr ""

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "There is no professional email address for this employee."
msgstr "Ennek az alkalmazottnak nincs céges e-mail címe."

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "There is no professional mobile for this employee."
msgstr "Ennek az alkalmazottnak nincs céges mobilja."

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Time Off"
msgstr "Szabadság"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__to_define
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__to_define
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__to_define
msgid "To Define"
msgstr ""

#. module: hr_presence
#: model:mail.template,subject:hr_presence.mail_template_presence
msgid "Unexpected Absence"
msgstr "Váratlan távollét"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_users_log
msgid "Users Log"
msgstr "Felhasználók naplója"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#: code:addons/hr_presence/models/hr_employee.py:0
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "You don't have the right to do this. Please contact an Administrator."
msgstr ""
"Önnek nincs megfelelő jogosultsága a művelet elvégzéséhez. Lépjen "
"kapcsolatba egy adminisztrátorral!"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_ir_websocket
msgid "websocket message handling"
msgstr ""
