# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_picking
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_sale_picking
#: model_terms:payment.provider,pending_msg:website_sale_picking.payment_provider_onsite
msgid ""
"<i>Your order has been saved.</i> Please come to the store to pay for your "
"products"
msgstr ""
"<i>Il tuo ordine è stato salvato.</i> Vieni in negozio per pagare i tuoi "
"prodotti"

#. module: website_sale_picking
#: model_terms:ir.ui.view,arch_db:website_sale_picking.payment_confirmation_status
msgid "<span class=\"text-muted\"> (On site picking)</span>"
msgstr "<span class=\"text-muted\"> (Ritiro in negozio)</span>"

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr "Modalità personalizzata"

#. module: website_sale_picking
#: model_terms:ir.ui.view,arch_db:website_sale_picking.res_config_settings_view_form
msgid "Customize Pickup Sites"
msgstr "Personalizza sedi ritiro"

#. module: website_sale_picking
#. odoo-javascript
#: code:addons/website_sale_picking/static/src/js/checkout_form.js:0
#, python-format
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr ""
"Se credi che si tratti di un errore, contatta l'amministratore del sito."

#. module: website_sale_picking
#. odoo-javascript
#: code:addons/website_sale_picking/static/src/js/checkout_form.js:0
#, python-format
msgid "No suitable payment option could be found."
msgstr "Non è stato possibile trovare un'opzione di pagamento adatta."

#. module: website_sale_picking
#: model:ir.model.fields.selection,name:website_sale_picking.selection__payment_provider__custom_mode__onsite
msgid "On Site"
msgstr "Sul campo"

#. module: website_sale_picking
#: model:product.template,name:website_sale_picking.onsite_delivery_product_product_template
msgid "On site picking"
msgstr "Ritiro in sede"

#. module: website_sale_picking
#: model:payment.provider,name:website_sale_picking.payment_provider_onsite
#: model_terms:product.template,description:website_sale_picking.onsite_delivery_product_product_template
msgid "Pay in store when picking the product"
msgstr "Paga in negozio quando ritiri il prodotto"

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_payment_provider
msgid "Payment Provider"
msgstr "Fornitore di pagamento"

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_res_config_settings__picking_site_ids
#: model:ir.model.fields,field_description:website_sale_picking.field_website__picking_site_ids
#: model_terms:ir.ui.view,arch_db:website_sale_picking.res_config_settings_view_form
msgid "Picking sites"
msgstr "Sedi ritiro"

#. module: website_sale_picking
#: model:ir.model.fields.selection,name:website_sale_picking.selection__delivery_carrier__delivery_type__onsite
msgid "Pickup in store"
msgstr "Ritira in negozio"

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Fornitore"

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Metodi di spedizione"

#. module: website_sale_picking
#. odoo-python
#: code:addons/website_sale_picking/models/delivery_carrier.py:0
#, python-format
msgid "The picking site and warehouse must share the same company"
msgstr ""
"La sede di ritiro e il magazzino devono appartenere alla stessa azienda"

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_delivery_carrier__warehouse_id
msgid "Warehouse"
msgstr "Magazzino"

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_website
msgid "Website"
msgstr "Sito web"

#. module: website_sale_picking
#. odoo-python
#: code:addons/website_sale_picking/controllers/main.py:0
#, python-format
msgid "You cannot pay onsite if the delivery is not onsite"
msgstr "Non puoi pagare in loco se la spedizione non è sul posto"

#. module: website_sale_picking
#: model_terms:payment.provider,auth_msg:website_sale_picking.payment_provider_onsite
msgid "Your payment has been authorized."
msgstr "Il pagamento è stato autorizzato."

#. module: website_sale_picking
#: model_terms:payment.provider,cancel_msg:website_sale_picking.payment_provider_onsite
msgid "Your payment has been cancelled."
msgstr "Il pagamento è stato annullato."

#. module: website_sale_picking
#: model_terms:payment.provider,done_msg:website_sale_picking.payment_provider_onsite
msgid "Your payment has been successfully processed. Thank you!"
msgstr "Il pagamento è stato elaborato con successo. Grazie!"

#. module: website_sale_picking
#: model:delivery.carrier,name:website_sale_picking.default_onsite_carrier
msgid "[On Site Pick] My Shop 1"
msgstr "[Ritiro in sede] Il mio negozio 1"
