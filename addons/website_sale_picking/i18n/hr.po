# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_picking
# 
# Translators:
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON> <olujic.vlad<PERSON><EMAIL>>, 2022
# <PERSON><PERSON> <davor.bo<PERSON><PERSON>@storm.hr>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: Bo<PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: website_sale_picking
#: model_terms:payment.provider,pending_msg:website_sale_picking.payment_provider_onsite
msgid ""
"<i>Your order has been saved.</i> Please come to the store to pay for your "
"products"
msgstr ""

#. module: website_sale_picking
#: model_terms:ir.ui.view,arch_db:website_sale_picking.payment_confirmation_status
msgid "<span class=\"text-muted\"> (On site picking)</span>"
msgstr ""

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_res_config_settings
msgid "Config Settings"
msgstr "Postavke"

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr ""

#. module: website_sale_picking
#: model_terms:ir.ui.view,arch_db:website_sale_picking.res_config_settings_view_form
msgid "Customize Pickup Sites"
msgstr ""

#. module: website_sale_picking
#. odoo-javascript
#: code:addons/website_sale_picking/static/src/js/checkout_form.js:0
#, python-format
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr ""

#. module: website_sale_picking
#. odoo-javascript
#: code:addons/website_sale_picking/static/src/js/checkout_form.js:0
#, python-format
msgid "No suitable payment option could be found."
msgstr ""

#. module: website_sale_picking
#: model:ir.model.fields.selection,name:website_sale_picking.selection__payment_provider__custom_mode__onsite
msgid "On Site"
msgstr ""

#. module: website_sale_picking
#: model:product.template,name:website_sale_picking.onsite_delivery_product_product_template
msgid "On site picking"
msgstr ""

#. module: website_sale_picking
#: model:payment.provider,name:website_sale_picking.payment_provider_onsite
#: model_terms:product.template,description:website_sale_picking.onsite_delivery_product_product_template
msgid "Pay in store when picking the product"
msgstr ""

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_payment_provider
msgid "Payment Provider"
msgstr "Pružatelj usluge naplate"

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_res_config_settings__picking_site_ids
#: model:ir.model.fields,field_description:website_sale_picking.field_website__picking_site_ids
#: model_terms:ir.ui.view,arch_db:website_sale_picking.res_config_settings_view_form
msgid "Picking sites"
msgstr ""

#. module: website_sale_picking
#: model:ir.model.fields.selection,name:website_sale_picking.selection__delivery_carrier__delivery_type__onsite
msgid "Pickup in store"
msgstr ""

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Davatelj "

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Načini dostave"

#. module: website_sale_picking
#. odoo-python
#: code:addons/website_sale_picking/models/delivery_carrier.py:0
#, python-format
msgid "The picking site and warehouse must share the same company"
msgstr ""

#. module: website_sale_picking
#: model:ir.model.fields,field_description:website_sale_picking.field_delivery_carrier__warehouse_id
msgid "Warehouse"
msgstr "Skladište"

#. module: website_sale_picking
#: model:ir.model,name:website_sale_picking.model_website
msgid "Website"
msgstr "Web stranica"

#. module: website_sale_picking
#. odoo-python
#: code:addons/website_sale_picking/controllers/main.py:0
#, python-format
msgid "You cannot pay onsite if the delivery is not onsite"
msgstr ""

#. module: website_sale_picking
#: model_terms:payment.provider,auth_msg:website_sale_picking.payment_provider_onsite
msgid "Your payment has been authorized."
msgstr "Vaše plaćanje je odobreno."

#. module: website_sale_picking
#: model_terms:payment.provider,cancel_msg:website_sale_picking.payment_provider_onsite
msgid "Your payment has been cancelled."
msgstr "Vaše plaćanje je otkazano."

#. module: website_sale_picking
#: model_terms:payment.provider,done_msg:website_sale_picking.payment_provider_onsite
msgid "Your payment has been successfully processed. Thank you!"
msgstr "Vaše plaćanje je uspješno obrađeno. Hvala!"

#. module: website_sale_picking
#: model:delivery.carrier,name:website_sale_picking.default_onsite_carrier
msgid "[On Site Pick] My Shop 1"
msgstr ""
