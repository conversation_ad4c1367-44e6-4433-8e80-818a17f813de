# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_product_matrix
# 
# Translators:
# KeyVillage, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2023
# <PERSON>, 2023
# Весел Карасто<PERSON>нов <<EMAIL>>, 2023
# Ивай<PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# V<PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: Vese<PERSON>lav<PERSON>, 2025\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Атрибутивни стойности"

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
#, python-format
msgid "Choose Product Variants"
msgstr ""

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
#, python-format
msgid "Close"
msgstr "Затвори"

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
#, python-format
msgid "Confirm"
msgstr "Потвърждение"

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
#, python-format
msgid "Edit Configuration"
msgstr "Промяна на конфигурацията"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid
msgid "Grid"
msgstr "Матрица"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_update
msgid "Grid Update"
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__report_grids
msgid ""
"If set, the matrix of configurable products will be shown on the report of "
"this order."
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__report_grids
msgid "Print Variant Grids"
msgstr ""

#. module: purchase_product_matrix
#: model_terms:ir.ui.view,arch_db:purchase_product_matrix.purchase_order_form_matrix
msgid "Product"
msgstr "Продукт"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_id
msgid "Product Template"
msgstr "Шаблон за продукт "

#. module: purchase_product_matrix
#: model_terms:ir.ui.view,arch_db:purchase_product_matrix.purchase_order_form_matrix
msgid "Product Variant"
msgstr "Продуктов вариант"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "Стойности на атрибутите на продукта, които не създават варианти"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order
msgid "Purchase Order"
msgstr "Поръчка"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Ред на поръчка за покупка"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid
msgid ""
"Technical storage of grid. \n"
"If grid_update, will be loaded on the PO. \n"
"If not, represents the matrix to open."
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr ""

#. module: purchase_product_matrix
#. odoo-python
#: code:addons/purchase_product_matrix/models/purchase.py:0
#, python-format
msgid ""
"You cannot change the quantity of a product present in multiple purchase "
"lines."
msgstr ""
