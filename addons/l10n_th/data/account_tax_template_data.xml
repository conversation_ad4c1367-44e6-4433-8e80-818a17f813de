<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="tax_input_vat" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Input VAT 7%</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="7"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_vat_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_input_tax_purchase_from_out_tax_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_input_vat'),
                'plus_report_expression_ids': [ref('tax_report_input_tax_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_input_tax_purchase_from_out_tax_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_input_vat'),
                'minus_report_expression_ids': [ref('tax_report_input_tax_tag')],
            }),
        ]"/>
    </record>
    <record id="tax_output_vat" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Output VAT 7%</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="7"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_vat_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_out_tax_sale_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_output_vat'),
                'plus_report_expression_ids': [ref('tax_report_out_tax_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_out_tax_sale_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_output_vat'),
                'minus_report_expression_ids': [ref('tax_report_out_tax_tag')],
            }),
        ]"/>
    </record>
    <record id="tax_input_vat_0" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Input VAT 0%</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="0"/>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_input_tax_purchase_from_out_tax_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_input_vat'),
                'plus_report_expression_ids': [ref('tax_report_input_tax_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_input_tax_purchase_from_out_tax_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_input_vat'),
                'plus_report_expression_ids': [ref('tax_report_input_tax_tag')],
            }),
        ]"/>
    </record>
    <record id="tax_output_vat_0" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Output VAT 0%</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="0"/>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_out_tax_sale_tag'), ref('tax_report_out_tax_less_sales_0_rate_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_output_vat'),
                'plus_report_expression_ids': [ref('tax_report_out_tax_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_out_tax_sale_tag'), ref('tax_report_out_tax_less_sales_0_rate_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_output_vat'),
                'minus_report_expression_ids': [ref('tax_report_out_tax_tag')],
            }),
        ]"/>
    </record>
    <record id="tax_input_vat_exempted" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Input VAT Exempted</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="0"/>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_input_tax_purchase_from_out_tax_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_input_vat'),
                'plus_report_expression_ids': [ref('tax_report_input_tax_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_input_tax_purchase_from_out_tax_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_input_vat'),
                'minus_report_expression_ids': [ref('tax_report_input_tax_tag')],
            }),
        ]"/>
    </record>
    <record id="tax_output_vat_exempted" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Output VAT Exempted</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="0"/>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_out_tax_sale_tag'), ref('tax_report_out_tax_less_exempted_sales_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_output_vat'),
                'plus_report_expression_ids': [ref('tax_report_out_tax_tag')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_out_tax_sale_tag'), ref('tax_report_out_tax_less_exempted_sales_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_output_vat'),
                'minus_report_expression_ids': [ref('tax_report_out_tax_tag')],
            }),
        ]"/>
    </record>
    <record id="tax_wht_co_1" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Company Withholding Tax 1% (Transportation)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-1"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_total_income_pnd53')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'minus_report_expression_ids': [ref('tax_report_total_remittance_pnd53')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_total_income_pnd53')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'plus_report_expression_ids': [ref('tax_report_total_remittance_pnd53')]
            }),
        ]"/>
    </record>
    <record id="tax_wht_co_2" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Company Withholding Tax 2% (Advertising)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-2"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_2"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_total_income_pnd53')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'minus_report_expression_ids': [ref('tax_report_total_remittance_pnd53')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_total_income_pnd53')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'plus_report_expression_ids': [ref('tax_report_total_remittance_pnd53')]
            }),
        ]"/>
    </record>
    <record id="tax_wht_co_3" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Company Withholding Tax 3% (Service)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-3"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_total_income_pnd53')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'minus_report_expression_ids': [ref('tax_report_total_remittance_pnd53')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_total_income_pnd53')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'plus_report_expression_ids': [ref('tax_report_total_remittance_pnd53')]
            }),
        ]"/>
    </record>
    <record id="tax_wht_co_5" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Company Withholding Tax 5% (Rental)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-5"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_total_income_pnd53')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'minus_report_expression_ids': [ref('tax_report_total_remittance_pnd53')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_total_income_pnd53')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'plus_report_expression_ids': [ref('tax_report_total_remittance_pnd53')]
            }),
        ]"/>
    </record>
    <record id="tax_wht_pers_1" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Personal Withholding Tax 1% (Transportation)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-1"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_total_income_pnd3')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'minus_report_expression_ids': [ref('tax_report_total_remittance_pnd3')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_total_income_pnd3')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'plus_report_expression_ids': [ref('tax_report_total_remittance_pnd3')]
            }),
        ]"/>
    </record>
    <record id="tax_wht_pers_2" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Personal Withholding Tax 2% (Advertising)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-2"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_2"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_total_income_pnd3')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'minus_report_expression_ids': [ref('tax_report_total_remittance_pnd3')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_total_income_pnd3')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'plus_report_expression_ids': [ref('tax_report_total_remittance_pnd3')]
            }),
        ]"/>
    </record>
    <record id="tax_wht_pers_3" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Personal Withholding Tax 3% (Service)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-3"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_total_income_pnd3')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'minus_report_expression_ids': [ref('tax_report_total_remittance_pnd3')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_total_income_pnd3')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'plus_report_expression_ids': [ref('tax_report_total_remittance_pnd3')]
            }),
        ]"/>
    </record>
    <record id="tax_wht_pers_5" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Personal Withholding Tax 5% (Rental)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-5"/>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_total_income_pnd3')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'minus_report_expression_ids': [ref('tax_report_total_remittance_pnd3')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_total_income_pnd3')]
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht'),
                'plus_report_expression_ids': [ref('tax_report_total_remittance_pnd3')]
            }),
        ]"/>
    </record>
    <record id="tax_wht_income_1" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Withholding Income Tax 1% (Transportation)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-1"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {'repartition_type': 'base'}),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht_income'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {'repartition_type': 'base'}),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht_income'),
            }),
        ]"/>
    </record>
    <record id="tax_wht_income_2" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Withholding Income Tax 2% (Advertising)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-2"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_2"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {'repartition_type': 'base'}),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht_income'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {'repartition_type': 'base'}),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht_income'),
            }),
        ]"/>
    </record>
    <record id="tax_wht_income_3" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Withholding Income Tax 3% (Service)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-3"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_3"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {'repartition_type': 'base'}),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht_income'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {'repartition_type': 'base'}),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht_income'),
            }),
        ]"/>
    </record>
    <record id="tax_wht_income_5" model="account.tax.template">
        <field name="chart_template_id" ref="chart"/>
        <field name="name">Withholding Income Tax 5% (Rental)</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="-5"/>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {'repartition_type': 'base'}),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht_income'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {'repartition_type': 'base'}),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('a_wht_income'),
            }),
        ]"/>
    </record>
</odoo>
