<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_ie" model="res.partner">
        <field name="name">IE Company</field>
        <field name="vat">IE1519572A</field>
        <field name="street">Celbridge Road</field>
        <field name="city">Lucan-St. Helens ED</field>
        <field name="country_id" ref="base.ie"/>
        <field name="state_id" ref="base.state_ie_32"/>
        <field name="zip">W23K5D0</field>
        <field name="phone">+353 85 012 3456</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.ieexample.com</field>
    </record>

    <record id="demo_company_ie" model="res.company">
        <field name="name">IE Company</field>
        <field name="partner_id" ref="partner_demo_company_ie"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_ie')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_ie.demo_company_ie'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_ie.l10n_ie')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_ie.demo_company_ie')"/>
    </function>
</odoo>
