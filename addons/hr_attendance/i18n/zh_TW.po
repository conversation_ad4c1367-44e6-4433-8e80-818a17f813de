# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr "\"下班打卡\"時間不能早於\"上班打卡\"時間。"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%(empl_name)s from %(check_in)s"
msgstr "%(empl_name)s 上班自%(check_in)s"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%(empl_name)s from %(check_in)s to %(check_out)s"
msgstr "%(empl_name)s 從%(check_in)s 到 %(check_out)s"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid ": Your user should be linked to an employee to use attendance."
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid ""
"<b>Warning! Last check in was over 12 hours ago.</b><br/>If this isn't "
"right, please contact Human Resource staff"
msgstr "<b>警告！ 距離最後一次上班打卡時間超過12小時.</b><br/>如果不正確，請聯絡人力資源部"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_kanban
msgid "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"期間\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"此處設定的值是特定於每間公司的。\" "
"role=\"img\" aria-label=\"此處設定的值是特定於每間公司的。\" "
"groups=\"base.group_multi_company\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Month\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            上個月\n"
"                        </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Extra Hours</span>"
msgstr "<span class=\"o_stat_text\">加班時數</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-success "
"me-1\" role=\"img\" aria-label=\"Available\" title=\"Available\"/>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-success "
"me-1\" role=\"img\" aria-label=\"可用\" title=\"可用\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"不可用\" title=\"不可用\">\n"
"                                    </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> Minutes</span>"
msgstr "<span>分鐘</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span> Minutes</span>\n"
"                                    <br/>\n"
"                                    <br/>"
msgstr ""
"<span> 分鐘</span>\n"
"                                    <br/>\n"
"                                    <br/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span>Time Period </span>"
msgstr "<span>時間段</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Activate the count of employees' extra hours."
msgstr "啟動員工加班時間的計數。"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""
"添加一些員工帳號，並用這些帳號登入系統，試著進行上班打卡/下班打卡操作\n"
"                若要添加員工點選員工選單即可."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__adjustment
msgid "Adjustment"
msgstr "調整"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Administrator"
msgstr "管理員"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"counted, in benefit of the company"
msgstr "允許一段時間（大約工作時間），不計算額外時間，以利於公司"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"deducted, in benefit of the employee"
msgstr "允許一段時間（大約工作時間）不會扣除額外的時間，以利於員工"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
msgid "Amount of extra hours"
msgstr "加班時數"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "An apple a day keeps the doctor away"
msgstr "一天一蘋果，醫生遠離我"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Another good day's work! See you soon!"
msgstr "又是一天超棒的工作！期待很快與您見面！"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_my_attendances
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_pivot
msgid "Attendance"
msgstr "考勤"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_report_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_report_action_filtered
msgid "Attendance Analysis"
msgstr "出勤分析"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_delay
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_delay
msgid "Attendance Kiosk Delay"
msgstr "自助考勤終端延遲"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_mode
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_mode
msgid "Attendance Mode"
msgstr "考勤模式"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_overtime
msgid "Attendance Overtime"
msgstr "考勤加班"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_report
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_graph
msgid "Attendance Statistics"
msgstr "出勤統計"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_state
msgid "Attendance Status"
msgstr "出勤狀態"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_employee
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_overview
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_kiosk_mode
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_attendances_overview
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr "考勤"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Available"
msgstr "可用"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__back
msgid "Back Camera"
msgstr "後方攝錄器"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Barcode"
msgstr "條碼"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode
msgid "Barcode / RFID"
msgstr "條碼 / RFID"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode_manual
msgid "Barcode / RFID and Manual Selection"
msgstr "條碼 / RFID 及手動選擇"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_barcode_source
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_barcode_source
msgid "Barcode Source"
msgstr "條碼來源"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr "無法為%(empl_name)s 建立新的出勤記錄, 因為自 %(datetime)s員工尚未下班打卡"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr "無法為%(empl_name)s 建立新的出勤記錄, 該員工已在 %(datetime)s中上班打卡"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr "在 %(empl_name)s上無法執行下班打卡, 無法找到相應的上班打卡資訊。您的出勤可能已由人力資源單位手動修改。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Check IN"
msgstr "簽到"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_in
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check In"
msgstr "上班打卡"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_my_attendances
msgid "Check In / Check Out"
msgstr "上班打卡/下班打卡"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Check OUT"
msgstr "簽出"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check Out"
msgstr "下班打卡"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Check-In/Out in Kiosk Mode"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_in
msgid "Checked in"
msgstr "已上班打卡"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked in at"
msgstr "上班打卡時間"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_out
msgid "Checked out"
msgstr "下班打卡"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked out at"
msgstr "下班打卡時間"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Choose how long the greeting message will be displayed."
msgstr "選擇問候訊息的顯示時間。"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_company
msgid "Companies"
msgstr "公司"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__company_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__company_id
msgid "Company"
msgstr "公司"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Company Logo"
msgstr "公司 Logo"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Compare attendance with working hours set on employee."
msgstr "將出勤與員工設置的工作時間進行比較。"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr "配置"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__hr_attendance_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__hr_attendance_overtime
msgid "Count Extra Hours"
msgstr "計算額外的時間"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Count of Extra Hours"
msgstr "加班時數"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Count of extra hours is considered from this date. Potential extra hours "
"prior to this date are not considered."
msgstr "從該日期開始考慮加班數。不考慮在此日期之前的潛在額外小時數。"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "Create a new employee"
msgstr "建立一位新員工"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_uid
msgid "Created by"
msgstr "創立者"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_date
msgid "Created on"
msgstr "建立於"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__date
msgid "Day"
msgstr "日"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the camera used for the barcode scan."
msgstr "定義掃瞄條碼的攝錄器"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the way the user will be identified by the application."
msgstr "定義應用程式識別用戶的方式"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__department_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__department_id
msgid "Department"
msgstr "部門"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Time"
msgstr "顯示時間"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Do not have access, user cannot edit the attendances that are not his own."
msgstr "無訪問權限，用戶無法編輯非本人的出勤情況"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Early to bed and early to rise, makes a man healthy, wealthy and wise"
msgstr "早睡早起，使男人健康，富有和明智"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Eat breakfast as a king, lunch as a merchant and supper as a beggar"
msgstr "早餐吃得像國王，午餐吃得像商人，晚餐吃得像乞丐"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid "Employee"
msgstr "員工"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__group_attendance_use_pin
msgid "Employee PIN"
msgstr "員工PIN碼"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Employee attendances"
msgstr "員工出勤"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
msgid "Employees"
msgstr "員工"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_use_pin
msgid "Enable PIN use"
msgstr "允許使用PIN碼登入"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Error: could not find corresponding employee."
msgstr "錯誤: 找不到相應的員工。"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_overtime_action
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__overtime_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Extra Hours"
msgstr "加班時數"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra Hours (Real)"
msgstr "加班時數（實際）"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_start_date
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_start_date
msgid "Extra Hours Starting Date"
msgstr "加班開始日期"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Extra hours today:"
msgstr "今天加班："

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra-hours including the threshold duration"
msgstr "包括閾值持續時間在內的加班時間"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "First come, first served"
msgstr "以別人的經驗，作自己的參考；以別人的成功，作自己的榜樣。"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__front
msgid "Front Camera"
msgstr "前方攝錄器"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Glad to have you back, it's been a while!"
msgstr "很高興您回來了，好久不見！"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Go back"
msgstr "返回"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good afternoon"
msgstr "下午好"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good evening"
msgstr "晚上好"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good morning"
msgstr "早上好"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good night"
msgstr "晚安"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Goodbye"
msgstr "再見"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Group By"
msgstr "分組方式"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
msgid "HR Attendance Search"
msgstr "人力資源考勤搜尋"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good afternoon"
msgstr "祝您下午好"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good day!"
msgstr "祝您有美好的一天！"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good evening"
msgstr "祝您晚上愉快"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a nice lunch!"
msgstr "去吃頓美味的午餐吧！"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Hours"
msgstr "小時"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month
msgid "Hours Last Month"
msgstr "上月上班小時數"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month_display
msgid "Hours Last Month Display"
msgstr "上月上班時間顯示"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_today
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_today
msgid "Hours Today"
msgstr "今日上班時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__worked_hours
msgid "Hours Worked"
msgstr "工作小時"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr "人力資源考勤搜尋"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__id
msgid "ID"
msgstr "ID"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Identify Manually"
msgstr "手動識別"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "If a job is worth doing, it is worth doing well!"
msgstr "如果一份工作值得做，就值得做好！"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Invalid request"
msgstr "無效請求"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_kiosk_no_user_mode
msgid "Kiosk Mode"
msgstr "打卡鐘模式"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_attendance_id
msgid "Last Attendance"
msgstr "最後一次出勤"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__manual
msgid "Manual Selection"
msgstr "手動選擇"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_ir_ui_menu
msgid "Menu"
msgstr "選單"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr "消息"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr "我的出勤"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "No Check Out"
msgstr "未下班打卡"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
msgid "No attendance records found"
msgstr "沒找到出勤記錄"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_overview
msgid "No attendance records to display"
msgstr "無出勤記錄可顯示"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "No employee corresponding to Badge ID '%(barcode)s.'"
msgstr "沒有與識別證 ID“%(barcode)s”相對應的員工。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "OK"
msgstr "確定"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_user
msgid "Officer : Manage all attendances"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__overtime_ids
msgid "Overtime"
msgstr "逾時"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please contact your administrator."
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please enter your PIN to"
msgstr "請輸入您的PIN碼"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please return to the main menu."
msgstr "請返回到主選單。"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_public
msgid "Public Employee"
msgstr "公開員工"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_report
msgid "Reporting"
msgstr "報表"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Scan your badge"
msgstr "掃瞄您的工作識別證"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__scanner
msgid "Scanner"
msgstr "掃瞄儀"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr "在員工詳細資訊表單（人力資源設定選項卡）中設定密碼。"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr "設定"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Sign out"
msgstr "登出"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Start from"
msgstr "開始於"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "Such grouping is not allowed."
msgstr "不允許進行此類分組。"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
msgid "The attendance records of your employees will be displayed here."
msgstr "您的員工出勤記錄將顯示在此處。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "The early bird catches the worm"
msgstr "早起的鳥兒有蟲吃"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_kiosk
msgid ""
"The user will be able to open the kiosk mode and validate the employee PIN."
msgstr "用戶將能夠打開打卡鐘模式並驗證員工 PIN。"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance
msgid ""
"The user will gain access to the human resources attendance menu, enabling "
"him to manage his own attendance."
msgstr "使用者將獲得權限存取人力資源的考勤選單，使他能管理自己的考勤。"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_use_pin
msgid ""
"The user will have to enter his PIN to check in and out manually at the "
"company screen."
msgstr "使用者必須輸入其密碼才能在公司螢幕上手動的上班打卡和下班打卡。"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"To activate Kiosk mode without pin code, you must have access right as an "
"Officer or above in the Attendance app. Please contact your administrator."
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Today's work hours:"
msgstr "今天的工作時間："

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_company_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_company_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Company"
msgstr "有利於公司的容忍時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_employee_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_employee_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Employee"
msgstr "有利於員工的容忍時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__total_overtime
msgid "Total Overtime"
msgstr "總加班時間"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Total extra hours:"
msgstr "總加班時間："

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes (defined on the Employee's profile) to check-in."
msgstr "使用 PIN 碼（個人識別密碼，在員工檔案中定義）簽到。"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_users
#: model:res.groups,name:hr_attendance.group_hr_attendance
msgid "User"
msgstr "使用者"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_kiosk
msgid "User : Only kiosk mode"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Want to check out?"
msgstr "是否打卡下班？"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome"
msgstr "歡迎"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome to"
msgstr "歡迎來到"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome!"
msgstr "歡迎!"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Work Hours"
msgstr "工作時間"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__worked_hours
msgid "Worked Hours"
msgstr "工作時數"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Worked hours last month"
msgstr "上個月的工作時間"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "Wrong PIN"
msgstr "錯誤的密碼"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "You cannot duplicate an attendance."
msgstr "您不能複製出勤記錄。"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_overview
msgid "Your attendance records will be displayed here."
msgstr "您的考勤記錄將顯示在此處。"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check in"
msgstr "上班打卡"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check out"
msgstr "下班打卡"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "seconds"
msgstr "秒"
