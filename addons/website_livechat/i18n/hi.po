# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_livechat
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: Hi<PERSON><PERSON>, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__session_count
msgid "# Sessions"
msgstr ""

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "%s has left the conversation."
msgstr ""

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid ""
"%s has started a conversation with %s. \n"
"                        The chat request has been canceled."
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<small>%</small>"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<span>Livechat Channel</span>"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "Available"
msgstr ""

#. module: website_livechat
#. odoo-javascript
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Avatar"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Bad"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__can_publish
msgid "Can Publish"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Channel"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.im_livechat_channel_view_form_add
msgid "Channel Name"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_tree
msgid "Chat"
msgstr ""

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr ""

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Chats"
msgstr ""

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_description
msgid "Description of the channel displayed on the website page"
msgstr ""

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_mail_channel
msgid "Discussion Channel"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Great"
msgstr ""

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Happy face"
msgstr ""

#. module: website_livechat
#. odoo-javascript
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "History"
msgstr "इतिहास"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "In Conversation"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__is_published
msgid "Is Published"
msgstr ""

#. module: website_livechat
#. odoo-javascript
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Lang"
msgstr ""

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/website.py:0
#, python-format
msgid "Live Support"
msgstr ""

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_im_livechat_channel
msgid "Livechat Channel"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "Livechat Support Channels"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Neutral face"
msgstr ""

#. module: website_livechat
#: model:ir.actions.act_window,name:website_livechat.im_livechat_channel_action_add
msgid "New Channel"
msgstr ""

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid "No Livechat Channel allows you to send a chat request for website %s."
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Not rated yet"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Okay"
msgstr ""

#. module: website_livechat
#. odoo-javascript
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Online"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Operator Avatar"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_name
msgid "Operator Name"
msgstr ""

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid ""
"Recipients are not available. Please refresh the page to get latest visitors"
" status."
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Sad face"
msgstr ""

#. module: website_livechat
#: model:ir.actions.server,name:website_livechat.website_livechat_send_chat_request_action_server
msgid "Send Chat Requests"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
msgid "Send chat request"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Speaking With"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_id
msgid "Speaking with"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Statistics"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.chatbot_script_view_form
msgid "Test"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The Team"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "The visitor"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "There are no public livechat channels to show."
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "There are no ratings for this channel for now."
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_mail_channel__livechat_visitor_id
msgid "Visitor"
msgstr ""

#. module: website_livechat
#. odoo-javascript
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Visitor is online"
msgstr ""

#. module: website_livechat
#: model:ir.actions.act_window,name:website_livechat.website_visitor_livechat_session_action
msgid "Visitor's Sessions"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__mail_channel_ids
msgid "Visitor's livechat channels"
msgstr ""

#. module: website_livechat
#: model:ir.ui.menu,name:website_livechat.website_livechat_visitor_menu
msgid "Visitors"
msgstr ""

#. module: website_livechat
#. odoo-javascript
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#: model:ir.model,name:website_livechat.model_website
#, python-format
msgid "Website"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_res_config_settings__channel_id
msgid "Website Live Channel"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website__channel_id
msgid "Website Live Chat Channel"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_url
msgid "Website URL"
msgstr ""

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/tests/test_livechat_basic_flow.py:0
#: model:ir.model,name:website_livechat.model_website_visitor
#, python-format
msgid "Website Visitor"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_description
msgid "Website description"
msgstr ""

#. module: website_livechat
#. odoo-python
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "an operator"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "last feedbacks"
msgstr ""
