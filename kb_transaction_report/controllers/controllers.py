# -*- coding: utf-8 -*-
# from odoo import http


# class KbAccountStatementReport(http.Controller):
#     @http.route('/kb_transaction_report/kb_transaction_report', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/kb_transaction_report/kb_transaction_report/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('kb_transaction_report.listing', {
#             'root': '/kb_transaction_report/kb_transaction_report',
#             'objects': http.request.env['kb_transaction_report.kb_transaction_report'].search([]),
#         })

#     @http.route('/kb_transaction_report/kb_transaction_report/objects/<model("kb_transaction_report.kb_transaction_report"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('kb_transaction_report.object', {
#             'object': obj
#         })
