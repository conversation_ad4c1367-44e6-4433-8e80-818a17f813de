# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_followup
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-06 03:04+0000\n"
"PO-Revision-Date: 2022-07-06 03:04+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_followup
#: model:followup.line,description:om_account_followup.demo_followup_line3
#: model:followup.line,description:om_account_followup.demo_followup_line4
#: model:followup.line,description:om_account_followup.demo_followup_line5
msgid ""
"\n"
"                Dear %(partner_name)s,\n"
"\n"
"                Despite several reminders, your account is still not settled.\n"
"\n"
"                Unless full payment is made in next 8 days, then legal action\n"
"                for the recovery of the debt will be taken without further\n"
"                notice.\n"
"\n"
"                I trust that this action will prove unnecessary and details of\n"
"                due payments is printed below.\n"
"\n"
"                In case of any queries concerning this matter, do not hesitate\n"
"                to contact our accounting department.\n"
"\n"
"                Best Regards,\n"
"            "
msgstr ""

#. module: om_account_followup
#: model:followup.line,description:om_account_followup.demo_followup_line1
msgid ""
"\n"
"                Dear %(partner_name)s,\n"
"\n"
"                Exception made if there was a mistake of ours, it seems that\n"
"                the following amount stays unpaid. Please, take appropriate\n"
"                measures in order to carry out this payment in the next 8 days.\n"
"\n"
"                Would your payment have been carried out after this mail was\n"
"                sent, please ignore this message. Do not hesitate to contact\n"
"                our accounting department.\n"
"\n"
"                Best Regards,\n"
"            "
msgstr ""

#. module: om_account_followup
#: model:followup.line,description:om_account_followup.demo_followup_line2
msgid ""
"\n"
"                Dear %(partner_name)s,\n"
"\n"
"                We are disappointed to see that despite sending a reminder,\n"
"                that your account is now seriously overdue.\n"
"\n"
"                It is essential that immediate payment is made, otherwise we\n"
"                will have to consider placing a stop on your account which\n"
"                means that we will no longer be able to supply your company\n"
"                with (goods/services).\n"
"                Please, take appropriate measures in order to carry out this\n"
"                payment in the next 8 days.\n"
"\n"
"                If there is a problem with paying invoice that we are not aware\n"
"                of, do not hesitate to contact our accounting department, so\n"
"                that we can resolve the matter quickly.\n"
"\n"
"                Details of due payments is printed below.\n"
"\n"
"                Best Regards,\n"
"            "
msgstr ""

#. module: om_account_followup
#: model:mail.template,body_html:om_account_followup.email_template_om_account_followup_level0
msgid ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, 255, 255); \">\n"
"\n"
"    <p>Dear ${object.name},</p>\n"
"    <p>\n"
"    Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take\n"
"appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to\n"
"contact our accounting department.  \n"
"\n"
"    </p>\n"
"<br/>\n"
"Best Regards,\n"
"<br/>\n"
"   <br/>\n"
"${user.name}\n"
"\n"
"<br/>\n"
"<br/>\n"
"\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"    <br/>\n"
"\n"
"</div>\n"
"            "
msgstr ""

#. module: om_account_followup
#: model:mail.template,body_html:om_account_followup.email_template_om_account_followup_level2
msgid ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, 255, 255); \">\n"
"    \n"
"    <p>Dear ${object.name},</p>\n"
"    <p>\n"
"    Despite several reminders, your account is still not settled.\n"
"Unless full payment is made in next 8 days, legal action for the recovery of the debt will be taken without\n"
"further notice.\n"
"I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"</p>\n"
"<br/>\n"
"Best Regards,\n"
"<br/>\n"
"<br/>\n"
"${user.name}\n"
"<br/>\n"
"<br/>\n"
"\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"    <br/>\n"
"\n"
"</div>\n"
"            "
msgstr ""

#. module: om_account_followup
#: model:mail.template,body_html:om_account_followup.email_template_om_account_followup_default
msgid ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, 255, 255); \">\n"
"    \n"
"    <p>Dear ${object.name},</p>\n"
"    <p>\n"
"    Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take\n"
"appropriate measures in order to carry out this payment in the next 8 days.\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to\n"
"contact our accounting department.\n"
"    </p>\n"
"<br/>\n"
"Best Regards,\n"
"<br/>\n"
"<br/>\n"
"${user.name}\n"
"<br/>\n"
"<br/>\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"<br/>\n"
"</div>\n"
"            "
msgstr ""

#. module: om_account_followup
#: model:mail.template,body_html:om_account_followup.email_template_om_account_followup_level1
msgid ""
"\n"
"<div style=\"font-family: 'Lucica Grande', Ubuntu, Arial, Verdana, sans-serif; font-size: 12px; color: rgb(34, 34, 34); background-color: rgb(255, 255, 255); \">\n"
"    \n"
"    <p>Dear ${object.name},</p>\n"
"   <p>\n"
"    We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account\n"
"which means that we will no longer be able to supply your company with (goods/services).\n"
"Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting\n"
"department. so that we can resolve the matter quickly.\n"
"Details of due payments is printed below.\n"
" </p>\n"
"<br/>\n"
"Best Regards,\n"
"    \n"
"<br/>\n"
"<br/>\n"
"${user.name}\n"
"    \n"
"<br/>\n"
"<br/>\n"
"\n"
"${object.get_followup_table_html() | safe}\n"
"\n"
"    <br/>\n"
"\n"
"</div>\n"
"            "
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " email(s) sent"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " email(s) should have been sent, but "
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " had unknown email address(es)"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " letter(s) in report"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " manual action(s) assigned:"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid " will be sent"
msgstr ""

#. module: om_account_followup
#: model:mail.template,subject:om_account_followup.email_template_om_account_followup_default
#: model:mail.template,subject:om_account_followup.email_template_om_account_followup_level0
#: model:mail.template,subject:om_account_followup.email_template_om_account_followup_level1
#: model:mail.template,subject:om_account_followup.email_template_om_account_followup_level2
msgid "${user.company_id.name} Payment Reminder"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid "%s partners have no credits and as such the action is cleared"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid ", the latest payment follow-up was:"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid ": Current Date"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid ": Partner Name"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid ": User Name"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid ": User's Company Name"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid ""
"<br/>\n"
"                                Customer ref:"
msgstr ""

#. module: om_account_followup
#: model:mail.template,name:om_account_followup.email_template_om_account_followup_level1
msgid "A bit urging second payment follow-up reminder email"
msgstr ""

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_followup
msgid "Account Follow-up"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Account Move line"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__manual_action_note
msgid "Action To Do"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Action to be taken e.g. Give a phonecall, Check if it's paid, ..."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "After"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
#, python-format
msgid "Amount"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_amount_due
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_amount_due
msgid "Amount Due"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_amount_overdue
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_amount_overdue
msgid "Amount Overdue"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Amount due"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid "Anybody"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__manual_action_responsible_id
msgid "Assign a Responsible"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__balance
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__balance
msgid "Balance"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.om_account_followup_stat_by_partner_search
msgid "Balance > 0"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_account_move_line__result
msgid "Balance Amount"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid ""
"Below is the history of the transactions of this\n"
"                            customer. You can check \"No Follow-up\" in\n"
"                            order to exclude it from the next follow-up\n"
"                            actions."
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__blocked
msgid "Blocked"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_print
msgid "Cancel"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_print__test_print
msgid ""
"Check if you want to print follow-ups without changing follow-up level."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.actions.act_window,help:om_account_followup.action_om_account_followup_definition_form
msgid "Click to define follow-up levels and their related actions."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Click to mark the action as done."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_sending_results
msgid "Close"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__company_id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__company_id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__company_id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__company_id
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Company"
msgstr ""

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الاعدادات"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__create_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__create_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__create_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__create_uid
msgid "Created by"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__create_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__create_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__create_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__create_date
msgid "Created on"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__credit
msgid "Credit"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_tree
msgid "Customer Followup"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_note
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_note
msgid "Customer Payment Promise"
msgstr ""

#. module: om_account_followup
#: model:ir.model.constraint,message:om_account_followup.constraint_followup_line_days_uniq
msgid "Days of the follow-up levels must be different"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__debit
msgid "Debit"
msgstr ""

#. module: om_account_followup
#: model:mail.template,name:om_account_followup.email_template_om_account_followup_default
msgid "Default payment follow-up reminder e-mail"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__description
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
#, python-format
msgid "Description"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__display_name
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__display_name
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__display_name
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__display_name
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__display_name
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__display_name
msgid "Display Name"
msgstr ""

#. module: om_account_followup
#: model:ir.ui.menu,name:om_account_followup.om_account_followup_s
msgid "Do Manual Follow-Ups"
msgstr "قم بالمتابعة اليدوية\n"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_print__partner_lang
msgid ""
"Do not change message text, if you want to send email in partner language, "
"or configure from company"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid ""
"Document: Customer account statement\n"
"                                <br/>\n"
"                                Date:"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_sending_results
msgid "Download Letters"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid "Due"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Due Date"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__delay
msgid "Due Days"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__email_body
msgid "Email Body"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__email_subject
msgid "Email Subject"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__email_template_id
msgid "Email Template"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Email not sent because of email address of partner not filled in"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__date_move
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__date_move
msgid "First move"
msgstr ""

#. module: om_account_followup
#: model:mail.template,name:om_account_followup.email_template_om_account_followup_level0
msgid "First polite payment follow-up reminder email"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__followup_id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__followup_id
msgid "Follow Ups"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__followup_id
msgid "Follow-Up"
msgstr "متابعة\n"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__name
msgid "Follow-Up Action"
msgstr ""

#. module: om_account_followup
#: model:ir.ui.menu,name:om_account_followup.menu_finance_followup
msgid "Follow-Ups"
msgstr "المتابعات\n"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__followup_line
#: model:ir.ui.menu,name:om_account_followup.om_account_followup_main_menu
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_form
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_tree
msgid "Follow-up"
msgstr "متابعة\n"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_line
msgid "Follow-up Criteria"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_account_move_line__followup_line_id
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Follow-up Level"
msgstr ""

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.action_om_account_followup_definition_form
#: model:ir.ui.menu,name:om_account_followup.om_account_followup_menu
msgid "Follow-up Levels"
msgstr "مستويات المتابعة\n"

#. module: om_account_followup
#: model:ir.actions.report,name:om_account_followup.action_report_followup
msgid "Follow-up Report"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_responsible_id
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "Follow-up Responsible"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__date
msgid "Follow-up Sending Date"
msgstr "متابعة تاريخ الإرسال\n"

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_stat
msgid "Follow-up Statistics"
msgstr ""

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_stat_by_partner
msgid "Follow-up Statistics by Partner"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid "Follow-up letter of "
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_graph
msgid "Follow-up lines"
msgstr ""

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.action_followup_stat
#: model:ir.ui.menu,name:om_account_followup.menu_action_followup_stat_follow
msgid "Follow-ups Analysis"
msgstr "تحليل المتابعات\n"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Follow-ups Sent"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "Follow-ups To Do"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "Followup Level"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.actions.act_window,help:om_account_followup.action_om_account_followup_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in\n"
"                    days. It is\n"
"                    possible to use print and e-mail templates to send specific\n"
"                    messages to\n"
"                    the customer."
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_line__sequence
#: model:ir.model.fields,help:om_account_followup.field_res_partner__latest_followup_sequence
#: model:ir.model.fields,help:om_account_followup.field_res_users__latest_followup_sequence
msgid "Gives the sequence order when displaying a list of follow-up lines."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Group By"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid ""
"He said the problem was temporary and promised to pay 50% before 15th of "
"May, balance before 1st of July."
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__id
msgid "ID"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid ""
"If not specified by the latest follow-up level, it will send from the "
"default email template"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Including journal entries marked as a litigation"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__invoice_partner_id
msgid "Invoice Address"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
#, python-format
msgid "Invoice Date"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid "Invoices Reminder"
msgstr ""

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.account_manual_reconcile_action
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_move_line_reconcile_tree
msgid "Journal Items to Reconcile"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup____last_update
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line____last_update
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print____last_update
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results____last_update
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat____last_update
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner____last_update
msgid "Last Modified on"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__write_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__write_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__write_uid
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__write_uid
msgid "Last Updated by"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__write_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__write_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__write_date
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__write_date
msgid "Last Updated on"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__date_move_last
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__date_move_last
msgid "Last move"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_account_move_line__followup_date
msgid "Latest Follow-up"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__latest_followup_date
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__latest_followup_date
msgid "Latest Follow-up Date"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__latest_followup_level_id
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__latest_followup_level_id
msgid "Latest Follow-up Level"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__latest_followup_level_id_without_lit
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__latest_followup_level_id_without_lit
msgid "Latest Follow-up Level without litigation"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Latest Follow-up Month"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__latest_followup_date
#: model:ir.model.fields,help:om_account_followup.field_res_users__latest_followup_date
msgid "Latest date that the follow-up level of the partner was changed"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__date_followup
msgid "Latest follow-up"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__date_followup
msgid "Latest followup"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Lit."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Litigation"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__manual_action
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "Manual Action"
msgstr ""

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.action_customer_followup
msgid "Manual Follow-Ups"
msgstr "المتابعة اليدوية\n"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid "Maturity Date"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__max_followup_id
msgid "Max Follow Up Level"
msgstr ""

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.action_customer_my_followup
#: model:ir.ui.menu,name:om_account_followup.menu_sale_followup
msgid "My Follow-Ups"
msgstr "المتابعات الخاصة بي\n"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "My Follow-ups"
msgstr "المتابعات الخاصة بي\n"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_followup__name
msgid "Name"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_sending_results__needprinting
msgid "Needs Printing"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_next_action
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_next_action
msgid "Next Action"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_next_action_date
msgid "Next Action Date"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "No Responsible"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.actions.act_window,help:om_account_followup.account_manual_reconcile_action
msgid "No journal items found."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Not Litigation"
msgstr ""

#. module: om_account_followup
#: model:ir.model.constraint,message:om_account_followup.constraint_followup_followup_company_uniq
msgid "Only one follow-up per company is allowed"
msgstr "يُسمح بمتابعة واحدة فقط لكل شركة\n"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,help:om_account_followup.field_res_users__payment_responsible_id
msgid ""
"Optionally you can assign a user to this field, which will make him "
"responsible for the action."
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Overdue email sent to %s, "
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat__partner_id
#: model:ir.model.fields,field_description:om_account_followup.field_followup_stat_by_partner__partner_id
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_stat_search
msgid "Partner"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.account_move_line_partner_tree
msgid "Partner entries"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.om_account_followup_stat_by_partner_search
#: model_terms:ir.ui.view,arch_db:om_account_followup.om_account_followup_stat_by_partner_tree
msgid "Partner to Remind"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__partner_ids
msgid "Partners"
msgstr "الشركاء"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.customer_followup_search_view
msgid "Partners with Overdue Credits"
msgstr "شركاء مع قروض متأخرة\n"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Payment Follow-up"
msgstr "متابعة الدفع\n"

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__payment_note
#: model:ir.model.fields,help:om_account_followup.field_res_users__payment_note
msgid "Payment Note"
msgstr ""

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_print
msgid "Print Follow-up & Send Mail to Customers"
msgstr "متابعة طباعة وإرسال بريد للعملاء\n"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Print Overdue Payments"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Print overdue payments report independent of follow-up line"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__description
msgid "Printed Message"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Printed overdue payments report"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid "Ref"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "Reference"
msgstr ""

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_report_om_account_followup_report_followup
msgid "Report Followup"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Responsible of credit collection"
msgstr ""

#. module: om_account_followup
#: model:ir.model,name:om_account_followup.model_followup_sending_results
msgid "Results from the sending of the different letters and emails"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_filter
msgid "Search Follow-up"
msgstr "متابعة البحث\n"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__email_conf
msgid "Send Email Confirmation"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__partner_lang
msgid "Send Email in Partner Language"
msgstr ""

#. module: om_account_followup
#: model:ir.actions.act_window,name:om_account_followup.action_om_account_followup_print
msgid "Send Follow-Ups"
msgstr "إرسال المتابعات\n"

#. module: om_account_followup
#: model:ir.ui.menu,name:om_account_followup.om_account_followup_print_menu
msgid "Send Letters and Emails"
msgstr "إرسال الرسائل والبريد الإلكتروني\n"

#. module: om_account_followup
#: code:addons/om_account_followup/wizard/followup_print.py:0
#, python-format
msgid "Send Letters and Emails: Actions Summary"
msgstr "إرسال الرسائل ورسائل البريد الإلكتروني: ملخص الإجراءات\n"

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "Send Overdue Email"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__send_letter
msgid "Send a Letter"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "Send a Letter or Email"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__send_email
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "Send an Email"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_print
msgid "Send emails and generate letters"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_print
msgid "Send follow-ups"
msgstr "إرسال المتابعات\n"

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_line__sequence
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__latest_followup_sequence
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__latest_followup_sequence
msgid "Sequence"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__summary
msgid "Summary"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_sending_results
msgid "Summary of actions"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_followup_print__test_print
msgid "Test Print"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "The"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/report/followup_print.py:0
#, python-format
msgid ""
"The followup plan defined for the current company does not have any followup"
" action."
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__latest_followup_level_id
#: model:ir.model.fields,help:om_account_followup.field_res_users__latest_followup_level_id
msgid "The maximum follow-up level"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__latest_followup_level_id_without_lit
#: model:ir.model.fields,help:om_account_followup.field_res_users__latest_followup_level_id_without_lit
msgid ""
"The maximum follow-up level without taking into account the account move "
"lines with litigation"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder. Could be negative if you want to send a polite alert "
"beforehand."
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid ""
"The partner does not have any accounting entries to print in the overdue "
"report for the current company."
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid "There is no followup plan defined for the current company."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_print
msgid ""
"This action will send follow-up emails, print the\n"
"                        letters and\n"
"                        set the manual actions per customer, according to the\n"
"                        follow-up levels defined."
msgstr ""
"سيرسل هذا الإجراء رسائل بريد إلكتروني للمتابعة ، ويطبع الرسائل ويضبط الإجراءات اليدوية لكل عميل ، وفقًا لـ\n"
"تحديد مستويات المتابعة."

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_print__date
msgid "This field allow you to select a forecast date to plan your follow-ups"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__payment_next_action
#: model:ir.model.fields,help:om_account_followup.field_res_users__payment_next_action
msgid ""
"This is the next action to be taken.  It will automatically be set when the "
"partner gets a follow-up level that requires a manual action. "
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,help:om_account_followup.field_res_users__payment_next_action_date
msgid ""
"This is when the manual follow-up is needed. The date will be set to the "
"current date when the partner gets a follow-up level that requires a manual "
"action. Can be practical to set manually e.g. to see if he keeps his "
"promises."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_form
msgid ""
"To remind customers of paying their invoices, you can\n"
"                        define different actions depending on how severely\n"
"                        overdue the customer is. These actions are bundled\n"
"                        into follow-up levels that are triggered when the due\n"
"                        date of an invoice has passed a certain\n"
"                        number of days. If there are other overdue invoices for\n"
"                        the\n"
"                        same customer, the actions of the most\n"
"                        overdue invoice will be executed."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.account_move_line_partner_tree
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_move_line_reconcile_tree
msgid "Total credit"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.account_move_line_partner_tree
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_move_line_reconcile_tree
msgid "Total debit"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.report_followup
msgid "Total:"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr ""

#. module: om_account_followup
#: model:mail.template,name:om_account_followup.email_template_om_account_followup_level2
msgid "Urging payment follow-up reminder email"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_line__send_letter
msgid "When processing, it will print a letter"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_line__send_email
msgid "When processing, it will send an email"
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,help:om_account_followup.field_followup_line__manual_action
msgid ""
"When processing, it will set the manual action to be taken for that "
"customer. "
msgstr ""

#. module: om_account_followup
#: model:ir.model.fields,field_description:om_account_followup.field_res_partner__payment_earliest_due_date
#: model:ir.model.fields,field_description:om_account_followup.field_res_users__payment_earliest_due_date
msgid "Worst Due Date"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid ""
"Write here the introduction in the letter,\n"
"                            according to the level of the follow-up. You can\n"
"                            use the following keywords in the text. Don't\n"
"                            forget to translate in all languages you installed\n"
"                            using to top right icon."
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/partner.py:0
#, python-format
msgid ""
"You became responsible to do the next action for the payment follow-up of"
msgstr ""

#. module: om_account_followup
#: code:addons/om_account_followup/models/followup.py:0
#, python-format
msgid ""
"Your description is invalid, use the right legend or %% if you want to use "
"the percent character."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "days overdue, do the following actions:"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_followup_line_form
msgid "e.g. Call the customer, check if it's paid, ..."
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_om_account_followup_print
msgid "or"
msgstr ""

#. module: om_account_followup
#: model_terms:ir.ui.view,arch_db:om_account_followup.view_partner_inherit_followup_form
msgid "⇾ Mark as Done"
msgstr ""
