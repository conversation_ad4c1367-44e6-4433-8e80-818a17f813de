
from odoo import api, fields, models, _
from logging import getLogger
from odoo.exceptions import ValidationError
import logging
from datetime import date,datetime, timedelta
_logger = logging.getLogger(__name__)


class kbOriginDate(models.Model):
    _inherit = "account.move"


    kb_origin_date = fields.Datetime(string=",")
    kb_origin_view = fields.Boolean("There", compute="get_sale_order_date")

    @api.depends('kb_origin_date')
    def get_sale_order_date(self):
        self.kb_origin_view = True
        if self.kb_origin_view:
            sale_id = self.env['sale.order'].search([('name', '=', self.invoice_origin)])
            if sale_id:
                self.kb_origin_date = sale_id.date_order
                print(sale_id.name)
        else:
            self.kb_origin_view = False

