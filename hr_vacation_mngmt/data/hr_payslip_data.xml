<odoo>
    <data>
        <record id="hr_leave_salary_register" model="hr.contribution.register">
            <field name="name">Leave Salary</field>
            <field name="partner_id" eval="False"/>
        </record>
        <!-- Salary Rules -->

        <record id="hr_salary_rule_leave_salary_basic" model="hr.salary.rule">
            <field name="name">Leave Salary Basic</field>
            <field name="code">LS</field>
            <field name="sequence" eval="90"/>
            <field name="category_id" ref="hr_payroll_community.ALW"/>
            <field name="register_id" ref="hr_leave_salary_register"/>
            <field name="condition_select">none</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = categories.BASIC</field>
        </record>

        <record id="hr_salary_rule_leave_salary_gross" model="hr.salary.rule">
            <field name="name">Leave Salary Gross</field>
            <field name="code">LS</field>
            <field name="sequence" eval="90"/>
            <field name="category_id" ref="hr_payroll_community.ALW"/>
            <field name="register_id" ref="hr_leave_salary_register"/>
            <field name="condition_select">none</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = categories.BASIC + categories.ALW</field>
        </record>
    </data>
</odoo>