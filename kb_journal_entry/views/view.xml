<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="extend_account_move" model="ir.ui.view">
        <field name="name">extend.account.move.create.journal</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='journal_id']" position="replace">
                <field name="journal_id" attrs="{'readonly': [('posted_before', '=', True)]}"/>
            </xpath>
            
        </field>
    </record>
</odoo>

<!-- <label for="journal_id" groups="account.group_account_readonly" options="{'no_create': True, 'no_open': True}" invisible="context.get('default_journal_id') and context.get('move_type', 'entry') != 'entry'"/>
<div name="journal_div" class="d-flex" groups="account.group_account_readonly" invisible="context.get('default_journal_id') and context.get('move_type', 'entry') != 'entry'">
<field name="journal_id" attrs="{'readonly': [('posted_before', '=', True)]}"/>
<span class="oe_inline o_form_label mx-3" groups="base.group_multi_currency" attrs="{'invisible': [('move_type', '=', 'entry')]}"> in </span>
<field name="currency_id" groups="base.group_multi_currency" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('move_type', '=', 'entry')]}"/>
</div> -->