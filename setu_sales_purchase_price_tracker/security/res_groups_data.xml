<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data noupdate="1">
        <record model="ir.module.category" id="module_category_setu">
            <field name="name">Price Tracker</field>
            <field name="sequence">60</field>
        </record>
        <record id="purchase_price_tracker_group" model="res.groups">
            <field name="name">Purchase Price Tracker</field>
            <field name="category_id" ref="setu_sales_purchase_price_tracker.module_category_setu"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
        <record id="sale_price_tracker_group" model="res.groups">
            <field name="name">Sale Price Tracker</field>
            <field name="category_id" ref="setu_sales_purchase_price_tracker.module_category_setu"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
    </data>
</odoo>