<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="setu_purchase_price_history_tree" model="ir.ui.view">
            <field name="name">setu.purchase.price.history.tree</field>
            <field name="model">setu.purchase.price.history</field>
            <field name="arch" type="xml">
                <tree string="Purchase Price Tracker" create="false"
                      edit="false" delete="false"
                      decoration-danger="price_movement=='price_up'"
                      decoration-muted="price_movement=='no_diff'"
                      decoration-success="price_movement=='price_down'">
                    <field name="order_id" widget="many2one"/>
                    <field name="state"/>
                    <field name="partner_id" widget="many2one"/>
                    <field name="product_id" widget="many2one"/>
                    <field name="purchase_uom"/>
                    <field name="category_id"/>
                    <field name="warehouse_id"/>
                    <field name="company_id" invisible="1"/>
                    <field name="tracker_date"/>
                    <field name="purchase_qty"/>
                    <field name="purchase_price" widget='monetary' options="{'currency_field': 'currency_id'}"/>
                    <field name="average_purchase_price"/>
                    <field name="currency_id" invisible="1"/>
                    <button name="action_none"
                            attrs="{'invisible':[('price_movement','!=','price_up')]}"
                            readonly="1" type="object" icon="fa-arrow-up" help="Price Up"/>
                    <button name="action_none"
                            attrs="{'invisible':[('price_movement','!=','price_down')]}"
                            readonly="1" type="object" icon="fa-arrow-down" help="Price Down"/>
                    <field name="price_movement"/>
                    <field name="current_cumulative_price" invisible="1"/>
                    <field name="cumulative_order" invisible="1"/>
                    <field name="change_in_price"/>
                    <field name="change_in_percentage" widget="progressbar"/>

                </tree>
            </field>
        </record>

        <record id="setu_purchase_price_history_search" model="ir.ui.view">
            <field name="name">setu.purchase.price.history.search</field>
            <field name="model">setu.purchase.price.history</field>
            <field name="arch" type="xml">
                <search string="Purchase Price Tracker">
                    <field name="order_id"/>
                    <field name="product_id"/>
                    <field name="purchase_uom"/>
                    <field name="category_id"/>
                    <field name="warehouse_id"/>
                    <field name="company_id"/>
                    <field name="partner_id"/>
                    <field name="tracker_date"/>
                    <filter string="Price Up" name="price_up" domain="[('price_movement','=','price_up')]"/>
                    <filter string="Price Down" name="price_down" domain="[('price_movement','=','price_down')]"/>
                    <filter string="Initial" name="price_down" domain="[('price_movement','=','initial')]"/>
                    <filter string="No Difference" name="price_down" domain="[('price_movement','=','no_diff')]"/>

                    <separator/>

                    <filter string="Purchase Order" context="{'group_by':'order_id'}" name="group_order_id"/>
                    <filter string="Order Status" context="{'group_by':'state'}" name="group_state"/>
                    <filter string="Vendor" context="{'group_by':'partner_id'}" name="group_partner_id"/>
                    <filter string="Product" context="{'group_by':'product_id'}" name="group_product_id"/>
                    <filter string="Purchase UOM" context="{'group_by':'purchase_uom'}" name="group_purchase_uom"/>
                    <filter string="Category" context="{'group_by':'category_id'}" name="group_category_id"/>
                    <filter string="Warehouse" context="{'group_by':'warehouse_id'}" name="group_warehouse_id"/>
                    <filter string="Company" context="{'group_by':'company_id'}" name="group_company_id"/>
                    <filter string="Currency" context="{'group_by':'currency_id'}" name="group_currency_id"/>
                    <filter string="Date" context="{'group_by':'tracker_date'}" name="group_tracker_date"/>
                    <separator/>

                    <filter string="Price Up/Down" name="group_price_up_down" context="{'group_by':'price_movement'}"/>
                </search>
            </field>
        </record>

        <record id="view_setu_purchase_price_history_graph" model="ir.ui.view">
			<field name="name">view.setu.purchase.price.history.graph</field>
			<field name="model">setu.purchase.price.history</field>
			<field name="arch" type="xml">
				<graph string="Purchase Price Tracker" >
<!--					<field name="product_id"/>-->
<!--                    <field name="category_id"/>-->
<!--                    <field name="company_id"/>-->
<!--                    <field name="order_id"/>-->
                    <field name="category_id" type="row"/>
<!--					<field name="change_in_price" type="column"/>-->
                    <field name="price_movement"  type="col" />
<!--					<field name="purchase_price"  type="measure" />-->
                    <field name="change_in_price"  type="measure" />
<!--                    <field name="change_in_percentage"  type="measure" />-->
<!--                    <field name="purchase_qty"  type="measure" />-->
				</graph>
			</field>
		</record>

        <record id="view_setu_purchase_price_history_pivot" model="ir.ui.view">
            <field name="name">view.setu.purchase.price.history.pivot</field>
            <field name="model">setu.purchase.price.history</field>
            <field name="arch" type="xml">
                <pivot string="Purchase Price Tracker">
                    <field name="category_id" type="row"/>
                    <field name="price_movement"  type="col" />
                    <field name="change_in_price"  type="measure"/>
                    <field name="purchase_price"  type="measure" />
                </pivot>
            </field>
        </record>

        <record id="setu_purchase_price_history_action" model="ir.actions.act_window">
            <field name="name">Purchase Price Tracker</field>
            <field name="res_model">setu.purchase.price.history</field>
            <field name="view_mode">tree,graph,pivot</field>
            <field name="context">{'search_default_group_product_id':1}</field>
        </record>


<!--        <menuitem id="setu_price_history_main_menu" name="Price Tracker"-->
<!--                  groups="setu_sales_purchase_price_tracker.purchase_price_tracker_group,setu_sales_purchase_price_tracker.sale_price_tracker_group"-->
<!--                  parent="account.menu_finance"-->
<!--                  sequence="5" />-->

        <menuitem id="setu_purchase_price_history_menu" name="Purchase Price Tracker"
                  groups="setu_sales_purchase_price_tracker.purchase_price_tracker_group"
                  action="action_wizard_setu_purchase_price_history"
                  parent="purchase.menu_purchase_root" sequence="7" />

    </data>
</odoo>