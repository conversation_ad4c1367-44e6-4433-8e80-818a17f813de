/** @odoo-module */

import {ReconcileController} from "./reconcile_controller.esm.js";
import {Reconcile<PERSON>ender<PERSON>} from "./reconcile_renderer.esm.js";
import {kanbanView} from "@web/views/kanban/kanban_view";
import {registry} from "@web/core/registry";

export const reconcileView = {
    ...kanbanView,
    Renderer: Reconcile<PERSON><PERSON><PERSON>,
    Controller: ReconcileController,
    buttonTemplate: "account_reconcile.ReconcileView.Buttons",
//    searchMenuTypes: ["filter",'groupBy'],
    searchMenuTypes: ['filter', 'favorite'],
};
registry.category("views").add("reconcile", reconcileView);
