<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_picking_move_tree" model="ir.ui.view">
        <field name="name">stock.move.picking.tree</field>
        <field name="model">stock.move</field>
        <field name="inherit_id" ref="stock.view_picking_move_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_uom']" position="after">
                <field
                    name="analytic_distribution"
                    widget="analytic_distribution"
                    groups="analytic.group_analytic_accounting"
                    options="{'product_field': 'product_id', 'business_domain': 'stock_move'}"
                />
            </xpath>
        </field>
    </record>
    <record id="view_move_tree" model="ir.ui.view">
        <field name="name">stock.move.tree</field>
        <field name="model">stock.move</field>
        <field name="inherit_id" ref="stock.view_move_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='location_dest_id']" position="after">
                <field
                    name="analytic_distribution"
                    widget="analytic_distribution"
                    optional="hide"
                    groups="analytic.group_analytic_accounting"
                    options="{'product_field': 'product_id', 'business_domain': 'stock_move'}"
                />
            </xpath>
        </field>
    </record>
    <record id="view_move_form" model="ir.ui.view">
        <field name="name">stock.move.form</field>
        <field name="model">stock.move</field>
        <field name="inherit_id" ref="stock.view_move_form" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='location_dest_id']" position="after">
                <field
                    name="analytic_distribution"
                    widget="analytic_distribution"
                    groups="analytic.group_analytic_accounting"
                    options="{'product_field': 'product_id', 'business_domain': 'stock_move'}"
                />
            </xpath>
        </field>
    </record>
    <record
        id="view_move_tree_receipt_picking_inherit_analytic_account"
        model="ir.ui.view"
    >
        <field name="name">stock.move.tree2 - Analytic Account</field>
        <field name="model">stock.move</field>
        <field name="inherit_id" ref="stock.view_move_tree_receipt_picking" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='location_dest_id']" position="after">
                <field
                    name="analytic_distribution"
                    widget="analytic_distribution"
                    groups="analytic.group_analytic_accounting"
                />
            </xpath>
        </field>
    </record>
</odoo>
