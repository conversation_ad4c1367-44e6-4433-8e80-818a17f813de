# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

{
    'name': 'KB Stock Discount',
    'version': '16.0',
    'category': 'Inventory/Inventory',
    'summary': 'Apply discounts to inventory valuation from KB Request for Sale',
    'description': """
KB Stock Discount
================
This module adds a "Product Discount" button to KB Request for Sale
and implements discount computation where discount amount = (kb_product_qty * kb_product_price) * (kb_product_discount / 100).
The discount percentage is applied to the total line amount and applied to inventory cost similar to stock valuation layer revaluation.
Supports both positive discounts (reductions) and negative discounts (surcharges).
    """,
    'depends': ['stock_account', 'kb_request_for_Sale'],
    'data': [
        'views/kb_request_for_sale_views.xml',
    ],
    # 'installable': True,
    # 'auto_install': False,
    'license': 'LGPL-3',
}