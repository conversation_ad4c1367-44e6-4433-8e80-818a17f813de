# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from datetime import timed<PERSON><PERSON>
from odoo import api, fields, models, _
from odoo.exceptions import UserError
from odoo.tools import float_compare, float_is_zero


class KbRequestForSales(models.Model):
    _inherit = "kb_request_for_sale"

    kb_discount_account_id = fields.Many2one('account.account', string='Discount Account')
    kb_account_journal_id = fields.Many2one('account.journal', string='Account Journal')
    valuation_count = fields.Integer(compute='_compute_valuation_count', string='Valuation Layers')

    def _compute_valuation_count(self):
        for record in self:
            record.valuation_count = self.env['stock.valuation.layer'].search_count([
                ('kb_request_id', '=', record.id)
            ])

    def action_view_valuation_layers(self):
        self.ensure_one()
        return {
            'name': _('Valuation Layers'),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.valuation.layer',
            'view_mode': 'tree,form',
            'domain': [('kb_request_id', '=', self.id)],
            'context': {'create': False}
        }

    def action_product_discount(self):
        for order in self:
            if not order.kb_product_line_ids:
                raise UserError(_("No product lines found to apply discount"))
            for line in order.kb_product_line_ids:
                if line.kb_product_id_pro:
                    add_value = line.kb_product_discount * line.kb_product_qty
                    if add_value != 0:
                        revaluation = self.env['stock.valuation.layer.revaluation'].create({
                            'product_id': line.kb_product_id_pro.id,
                            'company_id': self.env.company.id,
                            'account_id': order.kb_discount_account_id.id,
                            'account_journal_id': order.kb_account_journal_id.id,
                            'added_value': - add_value,
                            'reason': order.kb_sales_ids,
                        })
                        result = revaluation.action_validate_revaluation()
                        domain = [
                            ('product_id', '=', line.kb_product_id_pro.id),
                            ('description', 'like', order.kb_sales_ids),
                            ('kb_request_id', '=', False),
                            ('value', '=', revaluation.added_value),
                        ]
                        valuation_layer = self.env['stock.valuation.layer'].search(domain, limit=1, order='create_date desc')
                        if valuation_layer:
                            valuation_layer.kb_request_id = order.id

                            # Update the req_id field in the related account.move record
                            if valuation_layer.account_move_id:
                                # Set the req_id to point to this kb_request_for_sale record
                                valuation_layer.account_move_id.req_id = order.id

                                # Update the reference field to include the concatenated information
                                current_ref = valuation_layer.account_move_id.ref or ''
                                request_name = order.kb_sales_ids
                                journal_name = valuation_layer.account_move_id.name

                                # Check if the request name is already in the reference to avoid duplication
                                if request_name not in current_ref:
                                    if current_ref:
                                        # Format: existing_ref - request_name - journal_name
                                        valuation_layer.account_move_id.ref = f"{current_ref} - {request_name} - {journal_name}"
                                    else:
                                        # Format: request_name - journal_name
                                        valuation_layer.account_move_id.ref = f"{request_name} - {journal_name}"