# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _


class StockValuationLayer(models.Model):
    _inherit = 'stock.valuation.layer'
    
    # Add a computed field to link account moves based on req_id
    kb_account_move_id = fields.Many2one(
        'account.move',
        string='Related Account Move (via Request)',
        compute='_compute_kb_account_move_id',
        store=True,
        help="Account move linked through the sales request reference"
    )
    
    @api.depends('kb_request_id')
    def _compute_kb_account_move_id(self):
        """Compute the related account move based on kb_request_id"""
        for record in self:
            if record.kb_request_id:
                # Find account move with the same req_id
                account_move = self.env['account.move'].search([
                    ('req_id', '=', record.kb_request_id.id)
                ], limit=1, order='create_date desc')
                record.kb_account_move_id = account_move.id if account_move else False
            else:
                record.kb_account_move_id = False
    
    @api.model
    def create(self, vals):
        """Override create to automatically link account move when possible"""
        record = super(StockValuationLayer, self).create(vals)
        
        # If kb_request_id is set, try to link to account move
        if record.kb_request_id and not record.kb_account_move_id:
            record._compute_kb_account_move_id()
            
        return record
    
    def write(self, vals):
        """Override write to update account move link when kb_request_id changes"""
        result = super(StockValuationLayer, self).write(vals)
        
        # If kb_request_id was updated, recompute the account move link
        if 'kb_request_id' in vals:
            self._compute_kb_account_move_id()
            
        return result
    
    def action_view_account_move(self):
        """Action to view the related account move"""
        self.ensure_one()
        if not self.kb_account_move_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Account Move'),
                    'message': _('No account move found for this valuation layer.'),
                    'type': 'warning',
                }
            }
        
        return {
            'name': _('Account Move'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.move',
            'res_id': self.kb_account_move_id.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    def action_view_request(self):
        """Action to view the related sales request"""
        self.ensure_one()
        if not self.kb_request_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Sales Request'),
                    'message': _('No sales request found for this valuation layer.'),
                    'type': 'warning',
                }
            }
        
        return {
            'name': _('Sales Request'),
            'type': 'ir.actions.act_window',
            'res_model': 'kb_request_for_sale',
            'res_id': self.kb_request_id.id,
            'view_mode': 'form',
            'target': 'current',
        }


class AccountMove(models.Model):
    _inherit = 'account.move'
    
    # Add reverse relation to see valuation layers from account move
    kb_valuation_layer_ids = fields.One2many(
        'stock.valuation.layer',
        'kb_account_move_id',
        string='Related Valuation Layers',
        help="Stock valuation layers linked through the sales request"
    )
    
    kb_valuation_layer_count = fields.Integer(
        string='Valuation Layers Count',
        compute='_compute_kb_valuation_layer_count'
    )
    
    @api.depends('kb_valuation_layer_ids')
    def _compute_kb_valuation_layer_count(self):
        """Compute the count of related valuation layers"""
        for record in self:
            record.kb_valuation_layer_count = len(record.kb_valuation_layer_ids)
    
    def action_view_valuation_layers(self):
        """Action to view related valuation layers"""
        self.ensure_one()
        
        if not self.kb_valuation_layer_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Valuation Layers'),
                    'message': _('No valuation layers found for this account move.'),
                    'type': 'info',
                }
            }
        
        return {
            'name': _('Valuation Layers'),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.valuation.layer',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.kb_valuation_layer_ids.ids)],
            'context': {'default_kb_request_id': self.req_id.id if self.req_id else False},
        }
