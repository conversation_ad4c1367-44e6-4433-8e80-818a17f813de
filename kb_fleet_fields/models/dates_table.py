# from odoo import api, fields, models, _
# from calendar import month
# from email.policy import default
# from datetime import date
# from datetime import datetime
# from odoo.exceptions import ValidationError
# import logging
# from datetime import date,datetime
# _logger = logging.getLogger(__name__)
#
#
# class dates_table(models.Model):
#     _name = "dates_table"
#     _description = "dates_table"
#
#
#
#     # docs_title = fields.Many2one('dates_table_fields', store=True, string="Document")
#     docs_id = fields.Char(string="Document")
#     # startDates = fields.Date(string="Start Date")
#     # EndtDates = fields.Date(string="End Date")
#     # descriptionfield = fields.Char(string="Description")
#     #
#
#
#
#     datesTable_id_1 = fields.Many2one('fleet_new')
