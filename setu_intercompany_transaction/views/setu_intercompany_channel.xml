<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="setu_intercompany_channel_form" model="ir.ui.view">
            <field name="name">setu.intercompany.channel.form</field>
            <field name="model">setu.intercompany.channel</field>
            <field name="arch" type="xml">
                <form string="Intercompany Channel">
                    <sheet string="Intercompany Channel">
                        <div class="oe_title">
                            <h3>
                                <field name="name" required="True"/>
                            </h3>
                        </div>
                        <group expand="0" string="">
                            <group expand="0" string="Requestor">
                                <field name="requestor_company_id" required="True"/>
                                <field name="requestor_fiscal_position_id" domain="[('company_id','=',fulfiller_company_id)]" />
                                <field name="vendor_bill_journal_id" domain="[('company_id','=',requestor_company_id)]" />
                                <field name="pricelist_id" required="True"/>
                            </group>
                            <group expand="0" string="Fulfiller">
                                <field name="fulfiller_company_id" domain="[('id','!=',requestor_company_id)]" required="True" />
                                <field name="fulfiller_warehouse_id" required="True" domain="[('company_id','=',fulfiller_company_id)]" />
                                <field name="fulfiller_fiscal_position_id" domain="[('company_id','=',requestor_company_id)]" />
                                <field name="customer_invoice_journal_id" domain="[('company_id','=',fulfiller_company_id)]"  />
                            </group>
                        </group>

                        <group string="Advance Configurations">
                            <group>
                                <field name="direct_deliver_to_customer"/>
                            </group>
                            <group invisible="1">
                                <field name="manage_lot_serial"/>
                            </group>
                        </group>
                        <group string="Common Configurations">
                            <group>
                                <field name="sequence" />
                                <field name="ict_user_id" required="1" domain="[('company_ids','in',(requestor_company_id)),('company_ids','in',(fulfiller_company_id))]"/>
                                <field name="auto_workflow_id" />
                            </group>
                            <group>
                                <field name="sales_team_id"/>
                                <field name="active"/>
                            </group>
                        </group>

                    </sheet>
                </form>
            </field>
        </record>

        <record id="setu_intercompany_channel_tree" model="ir.ui.view">
            <field name="name">setu.intercompany.channel.tree</field>
            <field name="model">setu.intercompany.channel</field>
            <field name="arch" type="xml">
                <tree string="Intercompany Channel">
                    <field name="name"/>
                    <field name="requestor_company_id"/>
                    <field name="fulfiller_company_id" />
                    <field name="auto_workflow_id" optional="show"/>
                    <field name="vendor_bill_journal_id" optional="show"/>
                    <field name="customer_invoice_journal_id" optional="show"/>
                    <field name="manage_lot_serial" optional="hide"/>
                    <field name="direct_deliver_to_customer" optional="hide"/>
                    <field name="ict_user_id" optional="show"/>
                    <field name="sales_team_id" optional="show"/>
                    <field name="active" optional="hide"/>
                    <field name="sequence" />
                </tree>
            </field>
        </record>

        <record id="setu_intercompany_channel_search" model="ir.ui.view">
            <field name="name">setu.intercompany.channel.search</field>
            <field name="model">setu.intercompany.channel</field>
            <field name="arch" type="xml">
                <search string="Intercompany Channel">
                    <field name="name"/>
                    <field name="requestor_company_id"/>
                    <field name="fulfiller_company_id" />
                    <field name="ict_user_id" />
                    <field name="sales_team_id"/>
                    <field name="auto_workflow_id"/>

                    <separator/>

                    <filter string="Requestor Company" context="{'group_by':'requestor_company_id'}" name="requestor_company_id_groupby"/>
                    <filter string="Fulfiller Company" context="{'group_by':'fulfiller_company_id'}" name="fulfiller_company_id_groupby"/>
                    <filter string="ICT User" context="{'group_by':'ict_user_id'}" name="ict_user_id_groupby"/>
                    <filter string="Sales Team" context="{'group_by':'sales_team_id'}" name="sales_team_id_groupby"/>
                    <filter string="ICT Auto Workflow" context="{'group_by':'auto_workflow_id'}" name="auto_workflow_id_groupby"/>
                </search>
            </field>
        </record>

        <record id="setu_intercompany_channel_action" model="ir.actions.act_window">
            <field name="name">Inter Company Channel</field>
            <field name="res_model">setu.intercompany.channel</field>
            <field name="binding_view_types">form</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="setu_intercompany_channel_search"/>
        </record>

        <menuitem id="setu_intercompany_channel_menu" action="setu_intercompany_channel_action"
                  parent="setu_intercompany_transaction.intercompany_main_menu" sequence="11" />

    </data>
</odoo>