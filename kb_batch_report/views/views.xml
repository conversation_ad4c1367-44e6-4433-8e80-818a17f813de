<odoo>
  <data>
    <!-- explicit list view definition -->
<!--
    <record model="ir.ui.view" id="ids_batch_report.list">
      <field name="name">ids_batch_report list</field>
      <field name="model">ids_batch_report.ids_batch_report</field>
      <field name="arch" type="xml">
        <tree>
          <field name="name"/>
          <field name="value"/>
          <field name="value2"/>
        </tree>
      </field>
    </record>
-->

    <!-- actions opening views on models -->
<!--
    <record model="ir.actions.act_window" id="ids_batch_report.action_window">
      <field name="name">ids_batch_report window</field>
      <field name="res_model">ids_batch_report.ids_batch_report</field>
      <field name="view_mode">tree,form</field>
    </record>
-->

    <!-- server action to the one above -->
<!--
    <record model="ir.actions.server" id="ids_batch_report.action_server">
      <field name="name">ids_batch_report server</field>
      <field name="model_id" ref="model_ids_batch_report_ids_batch_report"/>
      <field name="state">code</field>
      <field name="code">
        action = {
          "type": "ir.actions.act_window",
          "view_mode": "tree,form",
          "res_model": model._name,
        }
      </field>
    </record>
-->

    <!-- Top menu item -->
<!--
    <menuitem name="ids_batch_report" id="ids_batch_report.menu_root"/>
-->
    <!-- menu categories -->
<!--
    <menuitem name="Menu 1" id="ids_batch_report.menu_1" parent="ids_batch_report.menu_root"/>
    <menuitem name="Menu 2" id="ids_batch_report.menu_2" parent="ids_batch_report.menu_root"/>
-->
    <!-- actions -->
<!--
    <menuitem name="List" id="ids_batch_report.menu_1_list" parent="ids_batch_report.menu_1"
              action="ids_batch_report.action_window"/>
    <menuitem name="Server to list" id="ids_batch_report" parent="ids_batch_report.menu_2"
              action="ids_batch_report.action_server"/>
-->
  </data>
</odoo>