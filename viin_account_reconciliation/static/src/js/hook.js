/** @odoo-module */

import { Many2OneField } from '@web/views/fields/many2one/many2one_field';
import { patch } from "@web/core/utils/patch";


patch(Many2OneField.prototype, "viin_account_reconciliation.account_bank_statement_line_view_form_quick_create", {
	async onExternalBtnClick() {
		if (this.props.record.mode === "edit" && this.context.auto_save) {
			var can_process = await this.props.record.save({
				noReload: true,
				stayInEdition: true,
				useSaveErrorDialog: true,
			});
			if (can_process) {
				this.openDialog(this.resId);
			}
		} else {
			this._super();
		}
	}
});
