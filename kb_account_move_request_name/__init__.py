from . import models

def post_init_hook(cr, registry):
    """Post-init hook to populate req_id_name for existing records and update stock valuation layers"""
    from odoo import api, SUPERUSER_ID

    env = api.Environment(cr, SUPERUSER_ID, {})

    # 1. Find account moves that have req_id but no req_id_name
    moves = env['account.move'].search([
        ('req_id', '!=', False),
        '|',
        ('req_id_name', '=', False),
        ('req_id_name', '=', '')
    ])

    if moves:
        # Trigger computation for these records
        moves._compute_req_id_name()

    # 2. Update stock valuation layers to set req_id in their account moves
    # Handle SVLs from purchase orders
    svls_purchase = env['stock.valuation.layer'].search([
        ('account_move_id', '!=', False),
        ('stock_move_id.purchase_line_id.order_id.req_id', '!=', False)
    ])

    for svl in svls_purchase:
        if svl.account_move_id and not svl.account_move_id.req_id:
            purchase_order = svl.stock_move_id.purchase_line_id.order_id
            if purchase_order.req_id:
                request_name = purchase_order.req_id.kb_sales_ids

                svl.account_move_id.write({
                    'req_id': purchase_order.req_id.id
                })

                # Update reference to include request name
                current_ref = svl.account_move_id.ref or ''
                if request_name and request_name not in current_ref:
                    if current_ref:
                        new_ref = f"{current_ref} - {request_name}"
                    else:
                        new_ref = request_name
                    svl.account_move_id.write({'ref': new_ref})

                # Trigger recomputation of req_id_name
                svl.account_move_id._compute_req_id_name()

    # 3. Handle SVLs from discount actions (with direct kb_request_id) - only if field exists
    if 'kb_request_id' in env['stock.valuation.layer']._fields:
        svls_discount = env['stock.valuation.layer'].search([
            ('account_move_id', '!=', False),
            ('kb_request_id', '!=', False)
        ])

        for svl in svls_discount:
            if svl.account_move_id and not svl.account_move_id.req_id:
                if getattr(svl, 'kb_request_id', False):
                    request_name = svl.kb_request_id.kb_sales_ids

                    svl.account_move_id.write({
                        'req_id': svl.kb_request_id.id
                    })

                    # Update reference to include request name
                    current_ref = svl.account_move_id.ref or ''
                    if request_name and request_name not in current_ref:
                        if current_ref:
                            new_ref = f"{current_ref} - {request_name}"
                        else:
                            new_ref = request_name
                        svl.account_move_id.write({'ref': new_ref})

                    # Trigger recomputation of req_id_name
                    svl.account_move_id._compute_req_id_name()
