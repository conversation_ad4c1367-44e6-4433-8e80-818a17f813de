from . import models

def post_init_hook(cr, registry):
    """Post-init hook to populate req_id_name for existing records - simplified"""
    from odoo import api, SUPERUSER_ID
    import logging

    _logger = logging.getLogger(__name__)

    try:
        env = api.Environment(cr, SUPERUSER_ID, {})

        # Simple computation trigger for existing records with req_id
        moves = env['account.move'].search([
            ('req_id', '!=', False)
        ], limit=1000)  # Limit to avoid timeout

        if moves:
            # Trigger computation for these records in batches
            for i in range(0, len(moves), 100):
                batch = moves[i:i+100]
                try:
                    batch._compute_req_id_name()
                except Exception as e:
                    _logger.warning(f"Error computing req_id_name for batch {i}: {e}")

        _logger.info(f"Post-init hook completed successfully for {len(moves)} records")

    except Exception as e:
        _logger.error(f"Error in post_init_hook: {e}")
        # Don't raise the error to avoid installation failure
