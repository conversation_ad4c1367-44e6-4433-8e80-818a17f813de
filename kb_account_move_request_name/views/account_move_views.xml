<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Account Move Form View - Add req_id_name field and update button -->
        <record id="view_move_form_inherit_req_id_name" model="ir.ui.view">
            <field name="name">account.move.form.inherit.req.id.name</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='ref']" position="after">
                    <field name="req_id_name" readonly="1" attrs="{'invisible': [('req_id_name', '=', False)]}"/>
                </xpath>
                <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="update_reference_with_request_name"
                            type="object"
                            class="oe_stat_button"
                            icon="fa-tag"
                            attrs="{'invisible': ['|', ('req_id', '=', False), ('req_id_name', '=', False)]}">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text">Update</span>
                            <span class="o_stat_text">Reference</span>
                        </div>
                    </button>
                </xpath>
            </field>
        </record>

        <!-- Account Move Tree View - Add req_id_name field as optional -->
        <record id="view_account_move_tree_inherit_req_id_name" model="ir.ui.view">
            <field name="name">account.move.tree.inherit.req.id.name</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="req_id_name" optional="hide"/>
                </xpath>
            </field>
        </record>

        <!-- Invoice Tree View - Add req_id_name field as optional -->
        <record id="view_invoice_tree_inherit_req_id_name" model="ir.ui.view">
            <field name="name">account.move.invoice.tree.inherit.req.id.name</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_invoice_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="req_id_name" optional="hide"/>
                </xpath>
            </field>
        </record>

        <!-- Removed server action to avoid method reference issues -->

    </data>
</odoo>
