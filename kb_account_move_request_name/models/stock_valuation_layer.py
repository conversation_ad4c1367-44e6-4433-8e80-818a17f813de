from odoo import api, fields, models, _


class StockValuationLayer(models.Model):
    _inherit = 'stock.valuation.layer'

    def _setup_complete(self):
        """Setup method to add dynamic dependencies after all modules are loaded"""
        super()._setup_complete()

        # Check if kb_request_id field exists (from kb_stock_discount module)
        if 'kb_request_id' in self._fields:
            # Add the dependency dynamically
            req_id_name_field = self._fields['req_id_name']
            if hasattr(req_id_name_field, 'depends'):
                current_depends = list(req_id_name_field.depends)
                if 'kb_request_id' not in current_depends:
                    current_depends.extend(['kb_request_id', 'kb_request_id.kb_sales_ids'])
                    req_id_name_field.depends = tuple(current_depends)

    # Override account_move_id to automatically set req_id when account move is linked
    account_move_id = fields.Many2one(
        'account.move',
        'Journal Entry',
        readonly=True,
        check_company=True,
        index="btree_not_null"
    )

    # Add computed field to show request name
    req_id_name = fields.Char(
        string='Request Name',
        compute='_compute_req_id_name',
        store=False,
        help='Name of the sales request from the related account move'
    )

    @api.depends('account_move_id', 'account_move_id.req_id_name')
    def _compute_req_id_name(self):
        """Compute the request name from account move - simplified"""
        for record in self:
            request_name = False

            # Get from account move req_id_name
            if record.account_move_id and record.account_move_id.req_id_name:
                request_name = record.account_move_id.req_id_name

            # Try to get from direct kb_request_id (for discount actions) if available
            elif hasattr(record, 'kb_request_id') and getattr(record, 'kb_request_id', False):
                try:
                    request_name = record.kb_request_id.kb_sales_ids
                except Exception:
                    pass  # Field might not be available

            record.req_id_name = request_name

    @api.model
    def create(self, vals):
        """Override create to set req_id in account move when SVL is created - simplified"""
        record = super().create(vals)

        # Only handle if we have an account move
        if record.account_move_id and not record.account_move_id.req_id:
            try:
                request_id = None

                # Check if this SVL has a direct kb_request_id (from discount actions)
                if hasattr(record, 'kb_request_id') and getattr(record, 'kb_request_id', False):
                    request_id = record.kb_request_id.id

                # Set req_id in the account move if found
                if request_id:
                    record.account_move_id.with_context(skip_ref_update=True).write({
                        'req_id': request_id
                    })
            except Exception as e:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning(f"Error setting req_id in account move from SVL: {e}")

        return record

    def write(self, vals):
        """Override write to handle account_move_id changes - simplified"""
        result = super().write(vals)

        # If account_move_id is being set, try to update req_id
        if 'account_move_id' in vals:
            try:
                for record in self:
                    if record.account_move_id and not record.account_move_id.req_id:
                        # Check if this SVL has a direct kb_request_id (from discount actions)
                        if hasattr(record, 'kb_request_id') and getattr(record, 'kb_request_id', False):
                            record.account_move_id.with_context(skip_ref_update=True).write({
                                'req_id': record.kb_request_id.id
                            })
            except Exception as e:
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning(f"Error updating account move req_id from SVL write: {e}")

        return result

    # Removed complex _validate_accounting_entries override to avoid issues

    # Removed complex batch update methods to avoid installation issues


