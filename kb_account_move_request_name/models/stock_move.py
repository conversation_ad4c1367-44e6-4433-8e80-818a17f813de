from odoo import api, fields, models, _


class StockMove(models.Model):
    _inherit = 'stock.move'

    def _prepare_account_move_vals(self, credit_account_id, debit_account_id, journal_id, qty, description, svl_id, cost):
        """Override to add req_id when creating account moves from stock valuation - simplified"""
        vals = super()._prepare_account_move_vals(credit_account_id, debit_account_id, journal_id, qty, description, svl_id, cost)

        # Add req_id if this stock move comes from a purchase order with req_id
        try:
            if self.purchase_line_id and self.purchase_line_id.order_id.req_id:
                vals['req_id'] = self.purchase_line_id.order_id.req_id.id
        except Exception:
            pass  # Don't break if req_id field is not available

        return vals
