from odoo import api, fields, models, _


class StockMove(models.Model):
    _inherit = 'stock.move'

    def _prepare_account_move_vals(self, credit_account_id, debit_account_id, journal_id, qty, description, svl_id, cost):
        """Override to add req_id when creating account moves from stock valuation"""
        vals = super()._prepare_account_move_vals(credit_account_id, debit_account_id, journal_id, qty, description, svl_id, cost)

        # Add req_id if this stock move comes from a purchase order with req_id
        if self.purchase_line_id and self.purchase_line_id.order_id.req_id:
            vals['req_id'] = self.purchase_line_id.order_id.req_id.id
            vals['stock_move_id'] = self.id

            # Update description to include request name
            request_name = self.purchase_line_id.order_id.req_id.kb_sales_ids
            if request_name and description:
                vals['ref'] = f"{description} - {request_name}"
            elif request_name:
                vals['ref'] = request_name

        return vals

    def _account_entry_move(self, qty, description, svl_id, cost):
        """Override to ensure proper req_id propagation in account moves"""
        # Set context to indicate this is from stock valuation
        self = self.with_context(stock_valuation=True)
        return super()._account_entry_move(qty, description, svl_id, cost)
