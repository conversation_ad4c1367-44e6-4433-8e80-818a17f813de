from odoo import api, fields, models, _
from odoo.tools import column_exists, create_column
import logging

_logger = logging.getLogger(__name__)


class AccountMove(models.Model):
    _inherit = "account.move"

    # Add a field to store the request name (keeping original req_id as Many2one for compatibility)
    req_id_name = fields.Char(
        string='Request Name',
        help='Name of the sales request',
        compute='_compute_req_id_name',
        store=True,
        readonly=True
    )


    @api.depends('req_id', 'req_id.kb_sales_ids', 'invoice_origin', 'stock_move_id', 'stock_move_id.purchase_line_id', 'stock_move_id.purchase_line_id.order_id.req_id')
    def _compute_req_id_name(self):
        """Compute req_id_name to store the request name"""


        for move in self:
            request_name = False

            # First try to get from existing req_id relationship
            if move.req_id:
                request_name = move.req_id.kb_sales_ids

            # Try to get from stock move -> purchase order -> req_id (for stock valuation entries)
            elif hasattr(move, 'stock_move_id') and move.stock_move_id:
                stock_move = move.stock_move_id
                if stock_move.purchase_line_id and stock_move.purchase_line_id.order_id.req_id:
                    request_name = stock_move.purchase_line_id.order_id.req_id.kb_sales_ids
                    # Also set the req_id for future reference
                    move.req_id = stock_move.purchase_line_id.order_id.req_id.id

            # Try to get from stock valuation layers linked to this account move
            elif hasattr(move, 'line_ids'):
                for line in move.line_ids:
                    if hasattr(line, 'stock_valuation_layer_ids'):
                        for svl in line.stock_valuation_layer_ids:
                            if svl.stock_move_id and svl.stock_move_id.purchase_line_id:
                                purchase_order = svl.stock_move_id.purchase_line_id.order_id
                                if purchase_order.req_id:
                                    request_name = purchase_order.req_id.kb_sales_ids
                                    move.req_id = purchase_order.req_id.id
                                    break
                    if request_name:
                        break

            # If no req_id yet, try to find through sale order via invoice_origin
            if not request_name and move.invoice_origin:
                # Search for sale order by name in invoice_origin
                sale_order = self.env['sale.order'].search([
                    ('name', '=', move.invoice_origin)
                ], limit=1)

                if sale_order and sale_order.req_id:
                    request_name = sale_order.req_id.kb_sales_ids
                    # Also set the req_id for future reference
                    move.req_id = sale_order.req_id.id

                # If not found directly, try to extract from comma-separated origins
                elif ',' in move.invoice_origin:
                    origins = [origin.strip() for origin in move.invoice_origin.split(',')]
                    for origin in origins:
                        sale_order = self.env['sale.order'].search([
                            ('name', '=', origin)
                        ], limit=1)
                        if sale_order and sale_order.req_id:
                            request_name = sale_order.req_id.kb_sales_ids
                            move.req_id = sale_order.req_id.id
                            break

            move.req_id_name = request_name

    @api.model
    def create(self, vals):
        """Override create to populate req_id from stock valuation context and concatenate request name"""
        request_name = None

        # Check if this account move is being created from stock valuation
        if self.env.context.get('stock_valuation') or vals.get('stock_move_id'):
            stock_move_id = vals.get('stock_move_id')
            if stock_move_id:
                stock_move = self.env['stock.move'].browse(stock_move_id)
                if stock_move.purchase_line_id and stock_move.purchase_line_id.order_id.req_id:
                    vals['req_id'] = stock_move.purchase_line_id.order_id.req_id.id
                    request_name = stock_move.purchase_line_id.order_id.req_id.kb_sales_ids

        # Create the account move first
        move = super(AccountMove, self).create(vals)

        # After creation, update the reference/name to include request name
        if request_name and move:
            self._update_journal_entry_with_request_name(move, request_name)

        return move

    def _update_journal_entry_with_request_name(self, move, request_name):
        """Update journal entry name/reference to include request name"""
        try:
            # Update the reference field to include request name
            current_ref = move.ref or ''

            # Check if request name is already in the reference
            if request_name not in current_ref:
                if current_ref:
                    new_ref = f"{current_ref} - {request_name}"
                else:
                    new_ref = request_name

                # Update the reference
                move.write({'ref': new_ref})

        except Exception as e:
            # Log the error but don't break the creation process
            _logger.warning(f"Could not update journal entry reference with request name: {e}")

    def write(self, vals):
        """Override write to update reference when req_id changes"""
        result = super().write(vals)

        # If req_id is being set and we don't have it in reference yet
        if 'req_id' in vals:
            for move in self:
                if move.req_id and move.req_id.kb_sales_ids:
                    request_name = move.req_id.kb_sales_ids
                    current_ref = move.ref or ''

                    # Check if request name is already in the reference
                    if request_name not in current_ref:
                        if current_ref:
                            new_ref = f"{current_ref} - {request_name}"
                        else:
                            new_ref = request_name

                        # Update the reference (use sudo to avoid recursion)
                        move.sudo().write({'ref': new_ref})

        return result

    def action_update_reference_with_request_name(self):
        """Action to update journal entry reference with request name"""
        for move in self:
            if move.req_id and move.req_id.kb_sales_ids:
                request_name = move.req_id.kb_sales_ids
                current_ref = move.ref or ''

                # Check if request name is already in the reference
                if request_name not in current_ref:
                    if current_ref:
                        new_ref = f"{current_ref} - {request_name}"
                    else:
                        new_ref = request_name

                    move.write({'ref': new_ref})

    @api.model
    def update_all_references_with_request_names(self):
        """Batch update all account moves to include request names in references"""
        moves = self.search([
            ('req_id', '!=', False),
            ('req_id.kb_sales_ids', '!=', False)
        ])

        updated_count = 0
        for move in moves:
            request_name = move.req_id.kb_sales_ids
            current_ref = move.ref or ''

            # Check if request name is already in the reference
            if request_name not in current_ref:
                if current_ref:
                    new_ref = f"{current_ref} - {request_name}"
                else:
                    new_ref = request_name

                move.write({'ref': new_ref})
                updated_count += 1

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Update Complete'),
                'message': _('Updated %d journal entries with request names.') % updated_count,
                'type': 'success',
            }
        }

