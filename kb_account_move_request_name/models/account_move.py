from odoo import api, fields, models, _
from odoo.tools import column_exists, create_column
import logging

_logger = logging.getLogger(__name__)


class AccountMove(models.Model):
    _inherit = "account.move"

    # Add a field to store the request name (keeping original req_id as Many2one for compatibility)
    req_id_name = fields.Char(
        string='Request Name',
        help='Name of the sales request',
        compute='_compute_req_id_name',
        store=True,
        readonly=True
    )


    @api.depends('req_id', 'req_id.kb_sales_ids')
    def _compute_req_id_name(self):
        """Compute req_id_name to store the request name - simplified to avoid recursion"""
        for move in self:
            request_name = False

            # Only get from existing req_id relationship to avoid recursion
            if move.req_id and hasattr(move.req_id, 'kb_sales_ids'):
                request_name = move.req_id.kb_sales_ids

            move.req_id_name = request_name

    def _set_req_id_from_context(self):
        """Set req_id based on context without triggering compute recursion"""
        for move in self:
            if move.req_id:
                continue  # Already has req_id, skip

            request_id = None

            # Try to get from sale order via invoice_origin (simplified)
            if move.invoice_origin:
                try:
                    sale_order = self.env['sale.order'].search([
                        ('name', '=', move.invoice_origin)
                    ], limit=1)

                    if sale_order and hasattr(sale_order, 'req_id') and sale_order.req_id:
                        request_id = sale_order.req_id.id
                except Exception as e:
                    _logger.warning(f"Error finding sale order for invoice_origin {move.invoice_origin}: {e}")

            # Set the req_id if found
            if request_id:
                try:
                    # Use sudo and disable tracking to avoid recursion
                    move.with_context(tracking_disable=True).sudo().write({'req_id': request_id})
                except Exception as e:
                    _logger.warning(f"Error setting req_id for move {move.id}: {e}")

    @api.model
    def create(self, vals):
        """Override create to populate req_id from context - simplified"""
        # Create the account move first
        move = super(AccountMove, self).create(vals)

        # After creation, try to set req_id if not already set (simplified)
        if not move.req_id:
            try:
                move._set_req_id_from_context()
            except Exception as e:
                _logger.warning(f"Error setting req_id from context for move {move.id}: {e}")

        return move

    def write(self, vals):
        """Override write to update reference when req_id changes - simplified"""
        # Avoid recursion by checking if we're already updating ref
        if 'ref' in vals and len(vals) == 1:
            return super().write(vals)

        result = super().write(vals)

        # If req_id is being set, update reference (but avoid recursion)
        if 'req_id' in vals and not self.env.context.get('skip_ref_update'):
            try:
                for move in self:
                    if move.req_id and hasattr(move.req_id, 'kb_sales_ids') and move.req_id.kb_sales_ids:
                        request_name = move.req_id.kb_sales_ids
                        current_ref = move.ref or ''

                        # Check if request name is already in the reference
                        if request_name not in current_ref:
                            if current_ref:
                                new_ref = f"{current_ref} - {request_name}"
                            else:
                                new_ref = request_name

                            # Update the reference with context to avoid recursion
                            move.with_context(skip_ref_update=True).write({'ref': new_ref})
            except Exception as e:
                _logger.warning(f"Error updating reference with request name: {e}")

        return result

    # Removed complex batch update methods to avoid installation issues

