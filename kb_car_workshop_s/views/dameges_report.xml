<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!--Your comment-->
    <record id="damage_report" model="ir.actions.act_window">
        <field name="name">Damages Report</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">damages_report</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Report
            </p>
        </field>
    </record>

    <!--damages checklist-->
    <record id="damage_views_form" model="ir.ui.view">
        <field name="name">damages_report.form</field>
        <field name="model">damages_report</field>
        <field name="arch" type="xml">

            <form>
                <sheet>
                    <group>
                        <group string="DAMAGE">
                            <field name="date_2" />
                            <!-- <field name="partner_id" /> -->
                            <field name="JLR" />
                            <field name="TK" />
                            <field name="tri" />
                            <field name="fireExt" />
                            <field name="FKB" />
                            <field name="tandM" />
                            <field name="spareT" />
                            <field name="chBox" />
                            <field name="APG" />
                            <field name="RgCard" />
                            <field name="driver_name" />
                            <!--upload photo to order-->
                            <field name="attachment_id_damage" widget="many2many_binary" class="oe_inline"/>


                        </group>
                        <group string="Car">
                            <!-- <field name="car_id" /> -->
                            <!-- <field name="carMake" /> -->
                            <field name="carModel" />
                            <!-- <field name="carPlate" /> -->
                            <!-- <field name="carYear" /> -->
                            <!-- <field name="carVin" /> -->
                            <field name="CarMilage" />
                        </group>




                    </group>
                    <!--Display damages in table form-->
                    <notebook>
                        <page string="Damage" name="damagas" >
                            <field name="damage_ids">
                                <tree editable="bottom">
                                    <field name="no" />
                                    <field name="choseSide" />
                                    <field name="reportdiscr" />
                                </tree>

                                <form>
                                    <group>
                                        <field name="no" />
                                        <field name="choseSide" />
                                        <field name="reportdiscr" />
                                    </group>
                                </form>
                            </field>
                        </page>


                    </notebook>


                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" />
                    <field name="activity_ids" />
                    <field name="message_ids" />
                </div>
            </form>
        </field>
    </record>


    
    <record id="damage_report_treee1" model="ir.ui.view">
        <field name="name">damages_report.tree</field>
        <field name="model">damages_report</field>
        <field name="arch" type="xml">
            <tree string="Reports">
                <!-- <field name="partner_id"/> -->
                <field name="date_2" />
            </tree>
        </field>
    </record>


<menuitem id="damage_views"
              name="Damages Report"
              parent= "config"
              action="damage_report"
              sequence="4"/>

</odoo>