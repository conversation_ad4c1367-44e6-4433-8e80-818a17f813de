from odoo import api, fields, models
import logging

_logger = logging.getLogger(__name__)


class ResPartner(models.Model):
    _inherit = 'res.partner'

    company_id = fields.Many2one('res.company', 'Company', index=True, required=True)

    @api.model
    def _search(self, args, offset=0, limit=None, order=None, count=False, access_rights_uid=None):
        args = ['|', (
            'company_id', 'in', self.env.companies.ids),
                ('company_id', '=', False)] + args
        return super(<PERSON>s<PERSON>artner, self)._search(args, offset=offset, limit=limit, order=order, count=count,
                                               access_rights_uid=access_rights_uid)

    @api.model
    def _name_search(self, name='', args=None, operator='ilike', limit=100, name_get_uid=None, order=None):
        args = list(args or [])
        if name:
            args += ['|', ('name', operator, name), ('id', operator, name)]
        return self._search(args, limit=limit, access_rights_uid=name_get_uid)

    def _get_name(self):
        result = super()._get_name()
        result += f" [{self.id}] "
        # Use logging instead of print to avoid BrokenPipeError
        _logger.debug("Partner name result: %s", result)
        return result
